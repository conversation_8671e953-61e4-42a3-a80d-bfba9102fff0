package constDef

/*
*
1,Energy_Limit,200,玩家体力上限
2,Energy_resumption,60,每xx秒恢复1点体力
3,Energy_Level_Deduction,5,所有关卡扣除体力数量
*/
const (
	GAME_CONFIG_ENERGY_LIMIT                     int32 = 1  // 玩家体力上限
	GAME_CONFIG_ENERGY_RESUMPTION                int32 = 2  // 每xx秒恢复1点体力
	GAME_CONFIG_ENERGY_LEVEL_DEDUCTION           int32 = 3  // 所有关卡扣除体力数量
	GAME_CONFIG_RENAME_COST_DIAMOND              int32 = 4  //改名字花费钻石数量
	GAME_CONFIG_FREE_SWEEP_TIMES                 int32 = 5  //每天免费扫荡次数
	GAME_CONFIG_NICK_NAME_CHARACTER_LIMIT_MIN    int32 = 6  // 昵称字符下限
	GAME_CONFIG_NICK_NAME_CHARACTER_LIMIT_MAX    int32 = 7  // 昵称字符上限
	GAME_CONFIG_NICK_NAME_CHANGE_FREE_CNT        int32 = 8  // 免费改名次数
	GAME_CONFIG_ROUGE_LEVEL_MAX_LIMIT            int32 = 9  //转盘英雄池肉鸽等级上限
	GAME_CONFIG_ROUGE_RECOVERY_COEFF             int32 = 10 // 恢复权重系数
	GAME_CONFIG_ROUGE_CONT_COEFF                 int32 = 11 // 连续选择限制系数
	GAME_CONFIG_ROUGE_SKILL_COEFF                int32 = 12 // 技能等级限制系数
	GAME_CONFIG_ROUGE_MIN_WEIGHT                 int32 = 13 // 最低权重
	GAME_CONFIG_ROUGE_INITIAL_WEIGHT             int32 = 14 // 初始权重
	GAME_CONFIG_GUILD_NOTICE_CHARACTER_LIMIT     int32 = 15 // 公会公告字符上限
	GAME_CONFIG_GUILD_NAME_CHANGE_FREE_CNT       int32 = 16 // 公会免费改名次数
	GAME_CONFIG_GUILD_SHORT_NAME_CHANGE_FREE_CNT int32 = 17 // 公会简称免费修改次数
	GAME_CONFIG_GUILD_RENAME_DIAMOND_CNT         int32 = 18 // 公会改名花费钻石数
	GAME_CONFIG_GUILD_RESHORTNAME_DIAMOND_CNT    int32 = 19 // 公会改简称消耗钻石数
	GAME_CONFIG_GUILD_RANK_TITLE_CHARACTER_LIMIT int32 = 20 // 公会阶级头衔字符上下限
	GAME_CONFIG_GUILD_BUILD_FREE_CNT             int32 = 21 // 创建公会免费次数
	GAME_CONFIG_GUILD_BUILD_DIAMOND_CNT          int32 = 22 // 创建公会花费钻石
	GAME_CONFIG_GUILD_R5_OFFLINE_STEP_DOWN_TIME  int32 = 23 // 会长离线几天自动转让
	GAME_CONFIG_COIN_DUNGEON_KEY_LIMIT           int32 = 35 // 金币副本钥匙上限
	GAME_CONFIG_GENE_DUNGEON_KEY_LIMIT           int32 = 36 // 基因副本钥匙上限
	GAME_CONFIG_SUNSHINE_DUNGEON_KEY_LIMIT       int32 = 37 //  阳光副本钥匙上限
	GAME_CONFIG_LORD_EQUIP_DUNGEON_KEY_LIMIT     int32 = 38 // 领主装备副本钥匙上限
)
