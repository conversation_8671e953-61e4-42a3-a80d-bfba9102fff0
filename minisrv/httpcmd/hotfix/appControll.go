package hotfix

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"context"
	"encoding/json"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"net/http"
)

func init() {
	http_post.AddPostHandler("generateAppVersion", generateAppVersion)
	http_post.AddPostHandler("uploadBundle2", uploadBundle2)
	http_post.AddPostHandler("uploadVersionDiff2", uploadVersionDiff2)
}

func generateAppVersion(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	values := r.URL.Query()
	packageVersion := values.Get("version")
	platform := values.Get("platform")
	bvr := values.Get("bvr")
	bvg := values.Get("bvg")
	bundleMd5 := values.Get("bundle_md5")
	preloadMd5 := values.Get("preload_md5")
	configMd5 := values.Get("config_md5")

	if packageVersion == "" || platform == "" {
		http.Error(w, "version and platform are required", http.StatusBadRequest)
		return
	}

	// 添加应用版本到数据库
	if err := addAppVersion(platform, packageVersion, bvr, bvg, bundleMd5, preloadMd5, configMd5); err != nil {
		http.Error(w, "Failed to add app version: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	response := map[string]interface{}{
		"code":    200,
		"message": "App version generated successfully",
		"data": map[string]interface{}{
			"version":  packageVersion,
			"platform": platform,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// addAppVersion 添加应用版本，类似PHP的_addAppVersion方法
func addAppVersion(platform, packageVersion, bvr, bvg, bundleMd5, preloadMd5, configMd5 string) error {
	ctx := context.Background()

	// 检查版本是否已存在
	existingVersion, err := model.GetClientVersionModel(ctx, platform, packageVersion)
	if err != nil {
		return fmt.Errorf("failed to check existing version: %w", err)
	}

	if existingVersion != nil {
		// 更新现有版本
		if bvr != "" {
			existingVersion.SetBundleVersionR(ctx, bvr)
		}
		if bvg != "" {
			existingVersion.SetBundleVersionG(ctx, bvg)
		}
		if bundleMd5 != "" {
			existingVersion.SetBundleMd5(ctx, bundleMd5)
		}
		if preloadMd5 != "" {
			existingVersion.SetPreloadMd5(ctx, preloadMd5)
		}
		if configMd5 != "" {
			existingVersion.SetConfigMd5(ctx, configMd5)
		}
		return nil
	}

	// 创建新版本
	_, err = orm.Create[*model.ClientVersionModel](ctx, &minirpc.ClientVersion{
		Platform:       platform,
		ClientVersion:  packageVersion,
		BundleVersionR: bvr,
		BundleVersionG: bvg,
		BundleMd5:      bundleMd5,
		PreloadMd5:     preloadMd5,
		ConfigMd5:      configMd5,
	})

	if err != nil {
		return fmt.Errorf("failed to create version: %w", err)
	}

	return nil
}

func uploadBundle2(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	values := r.URL.Query()
	baseVersion := values.Get("version")
	platform := values.Get("platform")
	gogCfg := values.Get("bundle_cfg_gog")
	gogPath := values.Get("bundle_path_gog")
	mcCfg := values.Get("bundle_cfg_mc")
	mcPath := values.Get("bundle_path_mc")
	jobId := values.Get("build_job")
	versionModel, _ := model.GetHuatuoVersionModel(context.Background(), platform, baseVersion)
	if versionModel == nil {
		versionModel, _ = orm.Create[*model.HuatuoVersionModel](context.Background(), &minirpc.ClientVersion{
			Platform:      platform,
			ClientVersion: baseVersion,
		})
	}
	ctx := context.Background()
	versionModel.SetBundlePathGog(ctx, gogPath)
	versionModel.SetBundleCfgGog(ctx, gogCfg)
	versionModel.SetBundleCfgMc(ctx, mcCfg)
	versionModel.SetBundlePathMc(ctx, mcPath)
	versionModel.SetJobId(ctx, jobId)

}

func uploadVersionDiff2(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	values := r.URL.Query()
	//$baseVersion = $params['version'];
	//$targetVersion = $params['target_version'];
	//$platform = $params['platform'];
	//$diffData = $params['data'];
	//$jobId = $params['build_job'];

	baseVersion := values.Get("base_version")
	targetVersion := values.Get("target_version")
	platform := values.Get("platform")
	diffData := values.Get("data")
	jobId := values.Get("build_job")
	if baseVersion == "" || platform == "" {
		http.Error(w, "base_version, platform are required", http.StatusBadRequest)
		return
	}

	versionModel, _ := model.GetHuatuoVersionModel(context.Background(), platform, baseVersion)
	if versionModel == nil {
		versionModel, _ = orm.Create[*model.HuatuoVersionModel](context.Background(), &minirpc.ClientVersion{
			Platform:      platform,
			ClientVersion: baseVersion,
		})
	}
	ctx := context.Background()
	versionModel.SetTargetVersion(ctx, targetVersion)
	versionModel.SetData(ctx, diffData)
	versionModel.SetJobId(ctx, jobId)
}
