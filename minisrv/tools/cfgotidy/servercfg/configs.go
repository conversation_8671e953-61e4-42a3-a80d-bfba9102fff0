// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"time"
)

const enumDefaultValue = int32(0)

type Configs struct {
	timeFormat                      string
	loc                             *time.Location
	cfgoVersion                     string
	cfgoCommitHash                  string
	metaVersion                     string
	dataVersion                     string
	versionExtraInfos               map[string]string
	AchievementTable                *AchievementTable
	ActivityTable                   *ActivityTable
	ArenaBotTable                   *ArenaBotTable
	ArenaChallengeRewardTable       *ArenaChallengeRewardTable
	ArenaDailyRankRewardTable       *ArenaDailyRankRewardTable
	ArenaExtraChallengeCntTable     *ArenaExtraChallengeCntTable
	ArenaMatchTable                 *ArenaMatchTable
	ArenaRefreshTable               *ArenaRefreshTable
	ArenaScoreTable                 *ArenaScoreTable
	ArenaShopTable                  *ArenaShopTable
	ArenaWeeklyRankRewardTable      *ArenaWeeklyRankRewardTable
	AttributeHierarchyTable         *AttributeHierarchyTable
	AvatarFrameTable                *AvatarFrameTable
	AvatarTable                     *AvatarTable
	BattleAttributeTable            *BattleAttributeTable
	BattleModelTable                *BattleModelTable
	BenefitsCalcJustShowTable       *BenefitsCalcJustShowTable
	BenefitsCalcTable               *BenefitsCalcTable
	BenefitsTable                   *BenefitsTable
	BlackShopTable                  *BlackShopTable
	ChapterLevelTable               *ChapterLevelTable
	ChapterTaskMainTable            *ChapterTaskMainTable
	ChapterTaskTable                *ChapterTaskTable
	DailyTasksScoreTable            *DailyTasksScoreTable
	DailyTasksTable                 *DailyTasksTable
	DaveLevelTable                  *DaveLevelTable
	DropGroupTable                  *DropGroupTable
	DropMainTable                   *DropMainTable
	DungeonChapterLevelTable        *DungeonChapterLevelTable
	DungeonCoinLevelTable           *DungeonCoinLevelTable
	DungeonGeneLevelTable           *DungeonGeneLevelTable
	DungeonLordEquipLevelTable      *DungeonLordEquipLevelTable
	DungeonRefreshTable             *DungeonRefreshTable
	DungeonSunshineLevelTable       *DungeonSunshineLevelTable
	DungeonTypeTable                *DungeonTypeTable
	FunctionPreviewTable            *FunctionPreviewTable
	FunctionTable                   *FunctionTable
	GameConfigs                     *GameConfigs
	GemAffixQualityTable            *GemAffixQualityTable
	GemQualityTypeTable             *GemQualityTypeTable
	GoToTable                       *GoToTable
	GuildFlagTable                  *GuildFlagTable
	GuildHaggleTable                *GuildHaggleTable
	GuildLevelTable                 *GuildLevelTable
	GuildPermissionTable            *GuildPermissionTable
	GuildRankTable                  *GuildRankTable
	GuildShopTable                  *GuildShopTable
	GuildTaskTable                  *GuildTaskTable
	GuildTasksScoreTable            *GuildTasksScoreTable
	HeroBondsTable                  *HeroBondsTable
	HeroCareerTable                 *HeroCareerTable
	HeroConfigTable                 *HeroConfigTable
	HeroElementTable                *HeroElementTable
	HeroFragmentTable               *HeroFragmentTable
	HeroGeneFragmentTable           *HeroGeneFragmentTable
	HeroGeneTable                   *HeroGeneTable
	HeroLevelTable                  *HeroLevelTable
	HeroLotteryGroupTable           *HeroLotteryGroupTable
	HeroLotteryMustTable            *HeroLotteryMustTable
	HeroLotteryRandomGroupTable     *HeroLotteryRandomGroupTable
	HeroLotteryRandomTable          *HeroLotteryRandomTable
	HeroQualityTable                *HeroQualityTable
	HeroRestrainTable               *HeroRestrainTable
	HeroSkillAttrTable              *HeroSkillAttrTable
	HeroSkillAwakeTable             *HeroSkillAwakeTable
	HeroSkillBuffTable              *HeroSkillBuffTable
	HeroSkillBuffTypeTable          *HeroSkillBuffTypeTable
	HeroSkillEffectTable            *HeroSkillEffectTable
	HeroSkillGroupTable             *HeroSkillGroupTable
	HeroSkillTypeTable              *HeroSkillTypeTable
	HeroStarTable                   *HeroStarTable
	HeroTable                       *HeroTable
	HeroTypeTable                   *HeroTypeTable
	Iap1stTable                     *Iap1stTable
	Iap2XTable                      *Iap2XTable
	IapAdFreeTable                  *IapAdFreeTable
	IapBPTable                      *IapBPTable
	IapBpRewardTable                *IapBpRewardTable
	IapDailySaleFreeRewardTable     *IapDailySaleFreeRewardTable
	IapDailySaleRewardGroupTable    *IapDailySaleRewardGroupTable
	IapDailySaleRewardTable         *IapDailySaleRewardTable
	IapDailySaleTable               *IapDailySaleTable
	IapDealTable                    *IapDealTable
	IapLevelFundRewardTable         *IapLevelFundRewardTable
	IapLevelFundTable               *IapLevelFundTable
	IapLifeCardTable                *IapLifeCardTable
	IapMonthCardTable               *IapMonthCardTable
	IapPackageDiamondShopTable      *IapPackageDiamondShopTable
	IapPackageRewardTable           *IapPackageRewardTable
	IapPackageTable                 *IapPackageTable
	IapPriceTable                   *IapPriceTable
	IapRegularPackGroupTable        *IapRegularPackGroupTable
	IapRegularPackTable             *IapRegularPackTable
	IapShopMallTable                *IapShopMallTable
	IapSignRewardTable              *IapSignRewardTable
	IapSignTable                    *IapSignTable
	IapTriggerPackGroupTable        *IapTriggerPackGroupTable
	IapTriggerPackTable             *IapTriggerPackTable
	IapTurnPackTable                *IapTurnPackTable
	IdleMonsterTable                *IdleMonsterTable
	IdleRewardTable                 *IdleRewardTable
	IdleRewardTime                  *IdleRewardTime
	ItemQualityTable                *ItemQualityTable
	ItemSourceTable                 *ItemSourceTable
	ItemTable                       *ItemTable
	LanguageCnTable                 *LanguageCnTable
	LevelShopTable                  *LevelShopTable
	LoginOpenTable                  *LoginOpenTable
	LordEquipGradeTable             *LordEquipGradeTable
	LordEquipGradeTypeTable         *LordEquipGradeTypeTable
	LordEquipSlotsTable             *LordEquipSlotsTable
	LordEquipTable                  *LordEquipTable
	LordEquipTypeTable              *LordEquipTypeTable
	LordGemCraftTable               *LordGemCraftTable
	LordGemDropCntTable             *LordGemDropCntTable
	LordGemDropQualityTable         *LordGemDropQualityTable
	LordGemRandomGroupChanceTable   *LordGemRandomGroupChanceTable
	LordGemRandomGroupMustTable     *LordGemRandomGroupMustTable
	LordGemRandomGroupTable         *LordGemRandomGroupTable
	LordGemRandomRewardGroupTable   *LordGemRandomRewardGroupTable
	LordGemReforgeTable             *LordGemReforgeTable
	LordGemTable                    *LordGemTable
	MailTable                       *MailTable
	MainChapterLevelTable           *MainChapterLevelTable
	MainChapterTable                *MainChapterTable
	MainLevelPassRewardTable        *MainLevelPassRewardTable
	MainLevelRangeDmgTable          *MainLevelRangeDmgTable
	MainLevelRewardRatioTable       *MainLevelRewardRatioTable
	MainLevelRewardTable            *MainLevelRewardTable
	MainLevelRogueRewardWeightTable *MainLevelRogueRewardWeightTable
	MainLevelTable                  *MainLevelTable
	MainLineTasksTable              *MainLineTasksTable
	MapEventBuffTable               *MapEventBuffTable
	MapEventMonsterGroupTable       *MapEventMonsterGroupTable
	MapEventMonsterTable            *MapEventMonsterTable
	MapEventObstacleTable           *MapEventObstacleTable
	MapEventPropTable               *MapEventPropTable
	MapEventRewardTable             *MapEventRewardTable
	MapEventSkillTable              *MapEventSkillTable
	MapEventTable                   *MapEventTable
	MapRefreshMonsterEventTable     *MapRefreshMonsterEventTable
	ModifierTable                   *ModifierTable
	MonsterCareerTable              *MonsterCareerTable
	MonsterGradeTable               *MonsterGradeTable
	MonsterPosTypeTable             *MonsterPosTypeTable
	MonsterPreviewSchemeTable       *MonsterPreviewSchemeTable
	MonsterSkillTable               *MonsterSkillTable
	MonsterTable                    *MonsterTable
	MonsterTypeTable                *MonsterTypeTable
	NewbieTable                     *NewbieTable
	NpcDialogueTable                *NpcDialogueTable
	PhotovoltaicTable               *PhotovoltaicTable
	PresetsTable                    *PresetsTable
	RankMainTable                   *RankMainTable
	RankRewardTable                 *RankRewardTable
	RougeNameCn                     *RougeNameCn
	RougeRefreshTable               *RougeRefreshTable
	RougeTabEffectTable             *RougeTabEffectTable
	RougeTabGroupRandomTable        *RougeTabGroupRandomTable
	RougeTabGroupTable              *RougeTabGroupTable
	RougeTabNewbieTable             *RougeTabNewbieTable
	RougeTabTable                   *RougeTabTable
	RougeWeightCoef                 *RougeWeightCoef
	SelectChestGroupTable           *SelectChestGroupTable
	SevenDayTasksScoreTable         *SevenDayTasksScoreTable
	SevenDayTasksTable              *SevenDayTasksTable
	ShopTable                       *ShopTable
	Sign7Table                      *Sign7Table
	SkillDmgTypeTable               *SkillDmgTypeTable
	TowerAILevelTable               *TowerAILevelTable
	TowerAITable                    *TowerAITable
	TowerTable                      *TowerTable
	TurnRewardTable                 *TurnRewardTable
	TurnScoreRewardTable            *TurnScoreRewardTable
	TurnTable                       *TurnTable
	VehicleTable                    *VehicleTable
}

func NewConfigs() *Configs {
	configs := &Configs{}
	configs.timeFormat = "2006-01-02 15:04:05"
	configs.loc = time.FixedZone("DTZ", 0*3600)
	configs.cfgoVersion = "1.0.5"
	configs.cfgoCommitHash = "71e74f5"
	configs.metaVersion = "8245"
	configs.versionExtraInfos = make(map[string]string)
	configs.AchievementTable = NewAchievementTable(configs)
	configs.ActivityTable = NewActivityTable(configs)
	configs.ArenaBotTable = NewArenaBotTable(configs)
	configs.ArenaChallengeRewardTable = NewArenaChallengeRewardTable(configs)
	configs.ArenaDailyRankRewardTable = NewArenaDailyRankRewardTable(configs)
	configs.ArenaExtraChallengeCntTable = NewArenaExtraChallengeCntTable(configs)
	configs.ArenaMatchTable = NewArenaMatchTable(configs)
	configs.ArenaRefreshTable = NewArenaRefreshTable(configs)
	configs.ArenaScoreTable = NewArenaScoreTable(configs)
	configs.ArenaShopTable = NewArenaShopTable(configs)
	configs.ArenaWeeklyRankRewardTable = NewArenaWeeklyRankRewardTable(configs)
	configs.AttributeHierarchyTable = NewAttributeHierarchyTable(configs)
	configs.AvatarFrameTable = NewAvatarFrameTable(configs)
	configs.AvatarTable = NewAvatarTable(configs)
	configs.BattleAttributeTable = NewBattleAttributeTable(configs)
	configs.BattleModelTable = NewBattleModelTable(configs)
	configs.BenefitsCalcJustShowTable = NewBenefitsCalcJustShowTable(configs)
	configs.BenefitsCalcTable = NewBenefitsCalcTable(configs)
	configs.BenefitsTable = NewBenefitsTable(configs)
	configs.BlackShopTable = NewBlackShopTable(configs)
	configs.ChapterLevelTable = NewChapterLevelTable(configs)
	configs.ChapterTaskMainTable = NewChapterTaskMainTable(configs)
	configs.ChapterTaskTable = NewChapterTaskTable(configs)
	configs.DailyTasksScoreTable = NewDailyTasksScoreTable(configs)
	configs.DailyTasksTable = NewDailyTasksTable(configs)
	configs.DaveLevelTable = NewDaveLevelTable(configs)
	configs.DropGroupTable = NewDropGroupTable(configs)
	configs.DropMainTable = NewDropMainTable(configs)
	configs.DungeonChapterLevelTable = NewDungeonChapterLevelTable(configs)
	configs.DungeonCoinLevelTable = NewDungeonCoinLevelTable(configs)
	configs.DungeonGeneLevelTable = NewDungeonGeneLevelTable(configs)
	configs.DungeonLordEquipLevelTable = NewDungeonLordEquipLevelTable(configs)
	configs.DungeonRefreshTable = NewDungeonRefreshTable(configs)
	configs.DungeonSunshineLevelTable = NewDungeonSunshineLevelTable(configs)
	configs.DungeonTypeTable = NewDungeonTypeTable(configs)
	configs.FunctionPreviewTable = NewFunctionPreviewTable(configs)
	configs.FunctionTable = NewFunctionTable(configs)
	configs.GameConfigs = NewGameConfigs(configs)
	configs.GemAffixQualityTable = NewGemAffixQualityTable(configs)
	configs.GemQualityTypeTable = NewGemQualityTypeTable(configs)
	configs.GoToTable = NewGoToTable(configs)
	configs.GuildFlagTable = NewGuildFlagTable(configs)
	configs.GuildHaggleTable = NewGuildHaggleTable(configs)
	configs.GuildLevelTable = NewGuildLevelTable(configs)
	configs.GuildPermissionTable = NewGuildPermissionTable(configs)
	configs.GuildRankTable = NewGuildRankTable(configs)
	configs.GuildShopTable = NewGuildShopTable(configs)
	configs.GuildTaskTable = NewGuildTaskTable(configs)
	configs.GuildTasksScoreTable = NewGuildTasksScoreTable(configs)
	configs.HeroBondsTable = NewHeroBondsTable(configs)
	configs.HeroCareerTable = NewHeroCareerTable(configs)
	configs.HeroConfigTable = NewHeroConfigTable(configs)
	configs.HeroElementTable = NewHeroElementTable(configs)
	configs.HeroFragmentTable = NewHeroFragmentTable(configs)
	configs.HeroGeneFragmentTable = NewHeroGeneFragmentTable(configs)
	configs.HeroGeneTable = NewHeroGeneTable(configs)
	configs.HeroLevelTable = NewHeroLevelTable(configs)
	configs.HeroLotteryGroupTable = NewHeroLotteryGroupTable(configs)
	configs.HeroLotteryMustTable = NewHeroLotteryMustTable(configs)
	configs.HeroLotteryRandomGroupTable = NewHeroLotteryRandomGroupTable(configs)
	configs.HeroLotteryRandomTable = NewHeroLotteryRandomTable(configs)
	configs.HeroQualityTable = NewHeroQualityTable(configs)
	configs.HeroRestrainTable = NewHeroRestrainTable(configs)
	configs.HeroSkillAttrTable = NewHeroSkillAttrTable(configs)
	configs.HeroSkillAwakeTable = NewHeroSkillAwakeTable(configs)
	configs.HeroSkillBuffTable = NewHeroSkillBuffTable(configs)
	configs.HeroSkillBuffTypeTable = NewHeroSkillBuffTypeTable(configs)
	configs.HeroSkillEffectTable = NewHeroSkillEffectTable(configs)
	configs.HeroSkillGroupTable = NewHeroSkillGroupTable(configs)
	configs.HeroSkillTypeTable = NewHeroSkillTypeTable(configs)
	configs.HeroStarTable = NewHeroStarTable(configs)
	configs.HeroTable = NewHeroTable(configs)
	configs.HeroTypeTable = NewHeroTypeTable(configs)
	configs.Iap1stTable = NewIap1stTable(configs)
	configs.Iap2XTable = NewIap2XTable(configs)
	configs.IapAdFreeTable = NewIapAdFreeTable(configs)
	configs.IapBPTable = NewIapBPTable(configs)
	configs.IapBpRewardTable = NewIapBpRewardTable(configs)
	configs.IapDailySaleFreeRewardTable = NewIapDailySaleFreeRewardTable(configs)
	configs.IapDailySaleRewardGroupTable = NewIapDailySaleRewardGroupTable(configs)
	configs.IapDailySaleRewardTable = NewIapDailySaleRewardTable(configs)
	configs.IapDailySaleTable = NewIapDailySaleTable(configs)
	configs.IapDealTable = NewIapDealTable(configs)
	configs.IapLevelFundRewardTable = NewIapLevelFundRewardTable(configs)
	configs.IapLevelFundTable = NewIapLevelFundTable(configs)
	configs.IapLifeCardTable = NewIapLifeCardTable(configs)
	configs.IapMonthCardTable = NewIapMonthCardTable(configs)
	configs.IapPackageDiamondShopTable = NewIapPackageDiamondShopTable(configs)
	configs.IapPackageRewardTable = NewIapPackageRewardTable(configs)
	configs.IapPackageTable = NewIapPackageTable(configs)
	configs.IapPriceTable = NewIapPriceTable(configs)
	configs.IapRegularPackGroupTable = NewIapRegularPackGroupTable(configs)
	configs.IapRegularPackTable = NewIapRegularPackTable(configs)
	configs.IapShopMallTable = NewIapShopMallTable(configs)
	configs.IapSignRewardTable = NewIapSignRewardTable(configs)
	configs.IapSignTable = NewIapSignTable(configs)
	configs.IapTriggerPackGroupTable = NewIapTriggerPackGroupTable(configs)
	configs.IapTriggerPackTable = NewIapTriggerPackTable(configs)
	configs.IapTurnPackTable = NewIapTurnPackTable(configs)
	configs.IdleMonsterTable = NewIdleMonsterTable(configs)
	configs.IdleRewardTable = NewIdleRewardTable(configs)
	configs.IdleRewardTime = NewIdleRewardTime(configs)
	configs.ItemQualityTable = NewItemQualityTable(configs)
	configs.ItemSourceTable = NewItemSourceTable(configs)
	configs.ItemTable = NewItemTable(configs)
	configs.LanguageCnTable = NewLanguageCnTable(configs)
	configs.LevelShopTable = NewLevelShopTable(configs)
	configs.LoginOpenTable = NewLoginOpenTable(configs)
	configs.LordEquipGradeTable = NewLordEquipGradeTable(configs)
	configs.LordEquipGradeTypeTable = NewLordEquipGradeTypeTable(configs)
	configs.LordEquipSlotsTable = NewLordEquipSlotsTable(configs)
	configs.LordEquipTable = NewLordEquipTable(configs)
	configs.LordEquipTypeTable = NewLordEquipTypeTable(configs)
	configs.LordGemCraftTable = NewLordGemCraftTable(configs)
	configs.LordGemDropCntTable = NewLordGemDropCntTable(configs)
	configs.LordGemDropQualityTable = NewLordGemDropQualityTable(configs)
	configs.LordGemRandomGroupChanceTable = NewLordGemRandomGroupChanceTable(configs)
	configs.LordGemRandomGroupMustTable = NewLordGemRandomGroupMustTable(configs)
	configs.LordGemRandomGroupTable = NewLordGemRandomGroupTable(configs)
	configs.LordGemRandomRewardGroupTable = NewLordGemRandomRewardGroupTable(configs)
	configs.LordGemReforgeTable = NewLordGemReforgeTable(configs)
	configs.LordGemTable = NewLordGemTable(configs)
	configs.MailTable = NewMailTable(configs)
	configs.MainChapterLevelTable = NewMainChapterLevelTable(configs)
	configs.MainChapterTable = NewMainChapterTable(configs)
	configs.MainLevelPassRewardTable = NewMainLevelPassRewardTable(configs)
	configs.MainLevelRangeDmgTable = NewMainLevelRangeDmgTable(configs)
	configs.MainLevelRewardRatioTable = NewMainLevelRewardRatioTable(configs)
	configs.MainLevelRewardTable = NewMainLevelRewardTable(configs)
	configs.MainLevelRogueRewardWeightTable = NewMainLevelRogueRewardWeightTable(configs)
	configs.MainLevelTable = NewMainLevelTable(configs)
	configs.MainLineTasksTable = NewMainLineTasksTable(configs)
	configs.MapEventBuffTable = NewMapEventBuffTable(configs)
	configs.MapEventMonsterGroupTable = NewMapEventMonsterGroupTable(configs)
	configs.MapEventMonsterTable = NewMapEventMonsterTable(configs)
	configs.MapEventObstacleTable = NewMapEventObstacleTable(configs)
	configs.MapEventPropTable = NewMapEventPropTable(configs)
	configs.MapEventRewardTable = NewMapEventRewardTable(configs)
	configs.MapEventSkillTable = NewMapEventSkillTable(configs)
	configs.MapEventTable = NewMapEventTable(configs)
	configs.MapRefreshMonsterEventTable = NewMapRefreshMonsterEventTable(configs)
	configs.ModifierTable = NewModifierTable(configs)
	configs.MonsterCareerTable = NewMonsterCareerTable(configs)
	configs.MonsterGradeTable = NewMonsterGradeTable(configs)
	configs.MonsterPosTypeTable = NewMonsterPosTypeTable(configs)
	configs.MonsterPreviewSchemeTable = NewMonsterPreviewSchemeTable(configs)
	configs.MonsterSkillTable = NewMonsterSkillTable(configs)
	configs.MonsterTable = NewMonsterTable(configs)
	configs.MonsterTypeTable = NewMonsterTypeTable(configs)
	configs.NewbieTable = NewNewbieTable(configs)
	configs.NpcDialogueTable = NewNpcDialogueTable(configs)
	configs.PhotovoltaicTable = NewPhotovoltaicTable(configs)
	configs.PresetsTable = NewPresetsTable(configs)
	configs.RankMainTable = NewRankMainTable(configs)
	configs.RankRewardTable = NewRankRewardTable(configs)
	configs.RougeNameCn = NewRougeNameCn(configs)
	configs.RougeRefreshTable = NewRougeRefreshTable(configs)
	configs.RougeTabEffectTable = NewRougeTabEffectTable(configs)
	configs.RougeTabGroupRandomTable = NewRougeTabGroupRandomTable(configs)
	configs.RougeTabGroupTable = NewRougeTabGroupTable(configs)
	configs.RougeTabNewbieTable = NewRougeTabNewbieTable(configs)
	configs.RougeTabTable = NewRougeTabTable(configs)
	configs.RougeWeightCoef = NewRougeWeightCoef(configs)
	configs.SelectChestGroupTable = NewSelectChestGroupTable(configs)
	configs.SevenDayTasksScoreTable = NewSevenDayTasksScoreTable(configs)
	configs.SevenDayTasksTable = NewSevenDayTasksTable(configs)
	configs.ShopTable = NewShopTable(configs)
	configs.Sign7Table = NewSign7Table(configs)
	configs.SkillDmgTypeTable = NewSkillDmgTypeTable(configs)
	configs.TowerAILevelTable = NewTowerAILevelTable(configs)
	configs.TowerAITable = NewTowerAITable(configs)
	configs.TowerTable = NewTowerTable(configs)
	configs.TurnRewardTable = NewTurnRewardTable(configs)
	configs.TurnScoreRewardTable = NewTurnScoreRewardTable(configs)
	configs.TurnTable = NewTurnTable(configs)
	configs.VehicleTable = NewVehicleTable(configs)
	return configs
}
func (c *Configs) LoadCsv(dir string, debugMode bool) error {
	var err error
	if err = c.AchievementTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ActivityTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaBotTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaChallengeRewardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaDailyRankRewardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaExtraChallengeCntTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaMatchTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaRefreshTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaScoreTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaShopTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaWeeklyRankRewardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.AttributeHierarchyTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.AvatarFrameTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.AvatarTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.BattleAttributeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.BattleModelTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.BenefitsCalcJustShowTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.BenefitsCalcTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.BenefitsTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.BlackShopTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ChapterLevelTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ChapterTaskMainTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ChapterTaskTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DailyTasksScoreTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DailyTasksTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DaveLevelTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DropGroupTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DropMainTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DungeonChapterLevelTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DungeonCoinLevelTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DungeonGeneLevelTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DungeonLordEquipLevelTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DungeonRefreshTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DungeonSunshineLevelTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DungeonTypeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.FunctionPreviewTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.FunctionTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GameConfigs.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GemAffixQualityTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GemQualityTypeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GoToTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GuildFlagTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GuildHaggleTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GuildLevelTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GuildPermissionTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GuildRankTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GuildShopTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GuildTaskTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GuildTasksScoreTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroBondsTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroCareerTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroConfigTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroElementTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroFragmentTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroGeneFragmentTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroGeneTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroLevelTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroLotteryGroupTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroLotteryMustTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroLotteryRandomGroupTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroLotteryRandomTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroQualityTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroRestrainTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroSkillAttrTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroSkillAwakeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroSkillBuffTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroSkillBuffTypeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroSkillEffectTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroSkillGroupTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroSkillTypeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroStarTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroTypeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.Iap1stTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.Iap2XTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapAdFreeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapBPTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapBpRewardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapDailySaleFreeRewardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapDailySaleRewardGroupTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapDailySaleRewardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapDailySaleTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapDealTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapLevelFundRewardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapLevelFundTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapLifeCardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapMonthCardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapPackageDiamondShopTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapPackageRewardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapPackageTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapPriceTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapRegularPackGroupTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapRegularPackTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapShopMallTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapSignRewardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapSignTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapTriggerPackGroupTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapTriggerPackTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapTurnPackTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IdleMonsterTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IdleRewardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IdleRewardTime.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ItemQualityTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ItemSourceTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ItemTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LanguageCnTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LevelShopTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LoginOpenTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordEquipGradeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordEquipGradeTypeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordEquipSlotsTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordEquipTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordEquipTypeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemCraftTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemDropCntTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemDropQualityTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupChanceTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupMustTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemRandomRewardGroupTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemReforgeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MailTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainChapterLevelTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainChapterTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainLevelPassRewardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainLevelRangeDmgTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainLevelRewardRatioTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainLevelRewardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainLevelRogueRewardWeightTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainLevelTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainLineTasksTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapEventBuffTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapEventMonsterGroupTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapEventMonsterTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapEventObstacleTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapEventPropTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapEventRewardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapEventSkillTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapEventTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapRefreshMonsterEventTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ModifierTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MonsterCareerTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MonsterGradeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MonsterPosTypeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MonsterPreviewSchemeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MonsterSkillTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MonsterTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MonsterTypeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.NewbieTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.NpcDialogueTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.PhotovoltaicTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.PresetsTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RankMainTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RankRewardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RougeNameCn.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RougeRefreshTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RougeTabEffectTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RougeTabGroupRandomTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RougeTabGroupTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RougeTabNewbieTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RougeTabTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RougeWeightCoef.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.SelectChestGroupTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.SevenDayTasksScoreTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.SevenDayTasksTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ShopTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.Sign7Table.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.SkillDmgTypeTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.TowerAILevelTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.TowerAITable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.TowerTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.TurnRewardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.TurnScoreRewardTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.TurnTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.VehicleTable.loadCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.AchievementTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ActivityTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaBotTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaChallengeRewardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaDailyRankRewardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaExtraChallengeCntTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaMatchTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaRefreshTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaScoreTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaShopTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ArenaWeeklyRankRewardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.AttributeHierarchyTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.AvatarFrameTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.AvatarTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.BattleAttributeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.BattleModelTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.BenefitsCalcJustShowTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.BenefitsCalcTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.BenefitsTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.BlackShopTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ChapterLevelTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ChapterTaskMainTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ChapterTaskTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DailyTasksScoreTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DailyTasksTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DaveLevelTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DropGroupTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DropMainTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DungeonChapterLevelTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DungeonCoinLevelTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DungeonGeneLevelTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DungeonLordEquipLevelTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DungeonRefreshTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DungeonSunshineLevelTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.DungeonTypeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.FunctionPreviewTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.FunctionTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GameConfigs.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GemAffixQualityTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GemQualityTypeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GoToTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GuildFlagTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GuildHaggleTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GuildLevelTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GuildPermissionTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GuildRankTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GuildShopTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GuildTaskTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.GuildTasksScoreTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroBondsTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroCareerTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroConfigTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroElementTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroFragmentTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroGeneFragmentTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroGeneTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroLevelTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroLotteryGroupTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroLotteryMustTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroLotteryRandomGroupTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroLotteryRandomTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroQualityTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroRestrainTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroSkillAttrTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroSkillAwakeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroSkillBuffTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroSkillBuffTypeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroSkillEffectTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroSkillGroupTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroSkillTypeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroStarTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.HeroTypeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.Iap1stTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.Iap2XTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapAdFreeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapBPTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapBpRewardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapDailySaleFreeRewardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapDailySaleRewardGroupTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapDailySaleRewardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapDailySaleTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapDealTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapLevelFundRewardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapLevelFundTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapLifeCardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapMonthCardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapPackageDiamondShopTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapPackageRewardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapPackageTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapPriceTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapRegularPackGroupTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapRegularPackTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapShopMallTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapSignRewardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapSignTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapTriggerPackGroupTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapTriggerPackTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IapTurnPackTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IdleMonsterTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IdleRewardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.IdleRewardTime.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ItemQualityTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ItemSourceTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ItemTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LanguageCnTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LevelShopTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LoginOpenTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordEquipGradeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordEquipGradeTypeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordEquipSlotsTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordEquipTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordEquipTypeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemCraftTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemDropCntTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemDropQualityTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupChanceTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupMustTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemRandomRewardGroupTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemReforgeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.LordGemTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MailTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainChapterLevelTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainChapterTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainLevelPassRewardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainLevelRangeDmgTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainLevelRewardRatioTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainLevelRewardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainLevelRogueRewardWeightTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainLevelTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MainLineTasksTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapEventBuffTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapEventMonsterGroupTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapEventMonsterTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapEventObstacleTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapEventPropTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapEventRewardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapEventSkillTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapEventTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MapRefreshMonsterEventTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ModifierTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MonsterCareerTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MonsterGradeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MonsterPosTypeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MonsterPreviewSchemeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MonsterSkillTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MonsterTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.MonsterTypeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.NewbieTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.NpcDialogueTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.PhotovoltaicTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.PresetsTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RankMainTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RankRewardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RougeNameCn.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RougeRefreshTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RougeTabEffectTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RougeTabGroupRandomTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RougeTabGroupTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RougeTabNewbieTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RougeTabTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.RougeWeightCoef.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.SelectChestGroupTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.SevenDayTasksScoreTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.SevenDayTasksTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.ShopTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.Sign7Table.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.SkillDmgTypeTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.TowerAILevelTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.TowerAITable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.TowerTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.TurnRewardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.TurnScoreRewardTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.TurnTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.VehicleTable.unmarshalCsv(dir, c, debugMode); err != nil {
		return err
	}
	if err = c.AchievementTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ActivityTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaBotTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaChallengeRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaDailyRankRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaExtraChallengeCntTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaMatchTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaRefreshTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaScoreTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaShopTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaWeeklyRankRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.AttributeHierarchyTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.AvatarFrameTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.AvatarTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.BattleAttributeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.BattleModelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.BenefitsCalcJustShowTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.BenefitsCalcTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.BenefitsTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.BlackShopTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ChapterLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ChapterTaskMainTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ChapterTaskTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DailyTasksScoreTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DailyTasksTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DaveLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DropGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DropMainTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DungeonChapterLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DungeonCoinLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DungeonGeneLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DungeonLordEquipLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DungeonRefreshTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DungeonSunshineLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DungeonTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.FunctionPreviewTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.FunctionTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GameConfigs.setupIndexes(); err != nil {
		return err
	}
	if err = c.GemAffixQualityTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GemQualityTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GoToTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GuildFlagTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GuildHaggleTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GuildLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GuildPermissionTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GuildRankTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GuildShopTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GuildTaskTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GuildTasksScoreTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroBondsTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroCareerTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroConfigTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroElementTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroFragmentTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroGeneFragmentTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroGeneTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroLotteryGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroLotteryMustTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroLotteryRandomGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroLotteryRandomTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroQualityTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroRestrainTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroSkillAttrTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroSkillAwakeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroSkillBuffTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroSkillBuffTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroSkillEffectTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroSkillGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroSkillTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroStarTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.Iap1stTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.Iap2XTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapAdFreeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapBPTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapBpRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapDailySaleFreeRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapDailySaleRewardGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapDailySaleRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapDailySaleTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapDealTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapLevelFundRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapLevelFundTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapLifeCardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapMonthCardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapPackageDiamondShopTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapPackageRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapPackageTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapPriceTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapRegularPackGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapRegularPackTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapShopMallTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapSignRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapSignTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapTriggerPackGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapTriggerPackTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapTurnPackTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IdleMonsterTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IdleRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IdleRewardTime.setupIndexes(); err != nil {
		return err
	}
	if err = c.ItemQualityTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ItemSourceTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ItemTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LanguageCnTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LevelShopTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LoginOpenTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordEquipGradeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordEquipGradeTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordEquipSlotsTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordEquipTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordEquipTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemCraftTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemDropCntTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemDropQualityTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupChanceTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupMustTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemRandomRewardGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemReforgeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MailTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainChapterLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainChapterTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainLevelPassRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainLevelRangeDmgTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainLevelRewardRatioTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainLevelRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainLevelRogueRewardWeightTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainLineTasksTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapEventBuffTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapEventMonsterGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapEventMonsterTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapEventObstacleTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapEventPropTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapEventRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapEventSkillTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapEventTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapRefreshMonsterEventTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ModifierTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MonsterCareerTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MonsterGradeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MonsterPosTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MonsterPreviewSchemeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MonsterSkillTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MonsterTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MonsterTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.NewbieTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.NpcDialogueTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.PhotovoltaicTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.PresetsTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RankMainTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RankRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RougeNameCn.setupIndexes(); err != nil {
		return err
	}
	if err = c.RougeRefreshTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RougeTabEffectTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RougeTabGroupRandomTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RougeTabGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RougeTabNewbieTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RougeTabTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RougeWeightCoef.setupIndexes(); err != nil {
		return err
	}
	if err = c.SelectChestGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.SevenDayTasksScoreTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.SevenDayTasksTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ShopTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.Sign7Table.setupIndexes(); err != nil {
		return err
	}
	if err = c.SkillDmgTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.TowerAILevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.TowerAITable.setupIndexes(); err != nil {
		return err
	}
	if err = c.TowerTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.TurnRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.TurnScoreRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.TurnTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.VehicleTable.setupIndexes(); err != nil {
		return err
	}
	return nil
}
