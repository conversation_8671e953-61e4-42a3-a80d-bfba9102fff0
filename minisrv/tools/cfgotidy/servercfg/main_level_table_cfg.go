// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type MainLevelTableCfg struct {
	Id                             int32                               `json:"Id"`                          // Id
	StringId                       string                              `json:"StringId"`                    // StringId
	IsMaxLevel                     bool                                `json:"IsMaxLevel"`                  // 是否最大关卡
	ChapterLevel                   int32                               `json:"ChapterLevel"`                // 所属章节
	ChapterLevelRef                *ChapterLevelTableCfg               `json:"-"`                           // 所属章节
	Chapter                        int32                               `json:"Chapter"`                     // 章
	Level                          int32                               `json:"Level"`                       // 关卡
	EliteMonsterAtkRatio           float32                             `json:"EliteMonsterAtkRatio"`        // 精英关卡怪物攻击放大系数
	EliteMonsterDefRatio           float32                             `json:"EliteMonsterDefRatio"`        // 精英关卡怪物防御放大系数
	EliteMonsterHpRatio            float32                             `json:"EliteMonsterHpRatio"`         // 精英关卡怪物血量放大系数
	LevelType                      LevelType                           `json:"LevelType"`                   // 关卡类型
	KillRewardRougeTabCntScheme    int32                               `json:"KillRewardRougeTabCntScheme"` // 方案
	KillRewardRougeTabCntSchemeRef *MainLevelRogueRewardWeightTableCfg `json:"-"`                           // 方案
	CommonChallengeReward          int32                               `json:"CommonChallengeReward"`       // 普通关卡挑战奖励
	CommonChallengeRewardRef       *MainLevelRewardTableCfg            `json:"-"`                           // 普通关卡挑战奖励
	EliteChallengeReward           int32                               `json:"EliteChallengeReward"`        // 精英关卡挑战奖励
	EliteChallengeRewardRef        *MainLevelRewardTableCfg            `json:"-"`                           // 精英关卡挑战奖励
	OneStarReward                  int32                               `json:"OneStarReward"`               // 1星通关奖励（仅普通关）
	OneStarRewardRef               *MainLevelPassRewardTableCfg        `json:"-"`                           // 1星通关奖励（仅普通关）
	TwoStarReward                  int32                               `json:"TwoStarReward"`               // 2星通关奖励（仅普通关）
	TwoStarRewardRef               *MainLevelPassRewardTableCfg        `json:"-"`                           // 2星通关奖励（仅普通关）
	ThreeStarReward                int32                               `json:"ThreeStarReward"`             // 3星通关奖励（仅普通关）
	ThreeStarRewardRef             *MainLevelPassRewardTableCfg        `json:"-"`                           // 3星通关奖励（仅普通关）
	ExtraReward                    []*RewardKVS                        `json:"ExtraReward"`                 // 奖励
}

func NewMainLevelTableCfg() *MainLevelTableCfg {
	return &MainLevelTableCfg{
		Id:                             0,
		StringId:                       "",
		IsMaxLevel:                     false,
		ChapterLevel:                   0,
		ChapterLevelRef:                nil,
		Chapter:                        0,
		Level:                          0,
		EliteMonsterAtkRatio:           0.0,
		EliteMonsterDefRatio:           0.0,
		EliteMonsterHpRatio:            0.0,
		LevelType:                      LevelType(enumDefaultValue),
		KillRewardRougeTabCntScheme:    0,
		KillRewardRougeTabCntSchemeRef: nil,
		CommonChallengeReward:          0,
		CommonChallengeRewardRef:       nil,
		EliteChallengeReward:           0,
		EliteChallengeRewardRef:        nil,
		OneStarReward:                  0,
		OneStarRewardRef:               nil,
		TwoStarReward:                  0,
		TwoStarRewardRef:               nil,
		ThreeStarReward:                0,
		ThreeStarRewardRef:             nil,
		ExtraReward:                    []*RewardKVS{},
	}
}

func NewMockMainLevelTableCfg() *MainLevelTableCfg {
	return &MainLevelTableCfg{
		Id:                             0,
		StringId:                       "",
		IsMaxLevel:                     false,
		ChapterLevel:                   0,
		ChapterLevelRef:                nil,
		Chapter:                        0,
		Level:                          0,
		EliteMonsterAtkRatio:           0.0,
		EliteMonsterDefRatio:           0.0,
		EliteMonsterHpRatio:            0.0,
		LevelType:                      LevelType(enumDefaultValue),
		KillRewardRougeTabCntScheme:    0,
		KillRewardRougeTabCntSchemeRef: nil,
		CommonChallengeReward:          0,
		CommonChallengeRewardRef:       nil,
		EliteChallengeReward:           0,
		EliteChallengeRewardRef:        nil,
		OneStarReward:                  0,
		OneStarRewardRef:               nil,
		TwoStarReward:                  0,
		TwoStarRewardRef:               nil,
		ThreeStarReward:                0,
		ThreeStarRewardRef:             nil,
		ExtraReward:                    []*RewardKVS{NewMockRewardKVS()},
	}
}

type MainLevelTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*MainLevelTableCfg
	localIds         map[int32]struct{}
}

func NewMainLevelTable(configs *Configs) *MainLevelTable {
	return &MainLevelTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*MainLevelTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *MainLevelTable) Get(key int32) *MainLevelTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *MainLevelTable) GetAll() map[int32]*MainLevelTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *MainLevelTable) put(key int32, value *MainLevelTableCfg, local bool) *MainLevelTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *MainLevelTable) putFromInheritedTable(key int32, value *MainLevelTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[MainLevelTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *MainLevelTable) Put(key int32, value *MainLevelTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[MainLevelTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *MainLevelTable) PutAll(m map[int32]*MainLevelTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *MainLevelTable) Range(f func(v *MainLevelTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *MainLevelTable) Filter(filterFuncs ...func(v *MainLevelTableCfg) bool) map[int32]*MainLevelTableCfg {
	filtered := map[int32]*MainLevelTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *MainLevelTable) FilterSlice(filterFuncs ...func(v *MainLevelTableCfg) bool) []*MainLevelTableCfg {
	filtered := []*MainLevelTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *MainLevelTable) FilterKeys(filterFuncs ...func(v *MainLevelTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *MainLevelTable) satisfied(v *MainLevelTableCfg, filterFuncs ...func(v *MainLevelTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *MainLevelTable) setupIndexes() error {
	return nil
}

func (t *MainLevelTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *MainLevelTableCfg) bindRefs(c *Configs) {
	r.ChapterLevelRef = c.ChapterLevelTable.Get(r.ChapterLevel)
	r.KillRewardRougeTabCntSchemeRef = c.MainLevelRogueRewardWeightTable.Get(r.KillRewardRougeTabCntScheme)
	r.CommonChallengeRewardRef = c.MainLevelRewardTable.Get(r.CommonChallengeReward)
	r.EliteChallengeRewardRef = c.MainLevelRewardTable.Get(r.EliteChallengeReward)
	r.OneStarRewardRef = c.MainLevelPassRewardTable.Get(r.OneStarReward)
	r.TwoStarRewardRef = c.MainLevelPassRewardTable.Get(r.TwoStarReward)
	r.ThreeStarRewardRef = c.MainLevelPassRewardTable.Get(r.ThreeStarReward)
	for _, e := range r.ExtraReward {
		e.bindRefs(c)
	}
}

func (t *MainLevelTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[MainLevelTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewMainLevelTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MainLevelTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[MainLevelTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// StringId
		{
			recordCfg.StringId = strings.TrimSpace(record[t.getIndexInCsv("StringId")])
		}
		// IsMaxLevel
		{
			if record[t.getIndexInCsv("IsMaxLevel")] == "" {
				recordCfg.IsMaxLevel = false
			} else {
				var err error
				recordCfg.IsMaxLevel, err = strconv.ParseBool(record[t.getIndexInCsv("IsMaxLevel")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MainLevelTable]unmarshal csv record failed, varName=IsMaxLevel, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("IsMaxLevel")], err)
					} else {
						return fmt.Errorf("[MainLevelTable]unmarshal csv record failed, varName=IsMaxLevel, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("IsMaxLevel")], err)
					}
				}
			}
		}
		// ChapterLevel
		if record[t.getIndexInCsv("ChapterLevel")] == "" {
			recordCfg.ChapterLevel = 0
		} else {
			var err error
			recordCfg.ChapterLevel, err = configs.ChapterLevelTable.getIdByRef(record[t.getIndexInCsv("ChapterLevel")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [MainLevelTable]unmarshal csv record failed, varName=ChapterLevel, type=ref@ChapterLevelTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("ChapterLevel")], err)
				} else {
					return fmt.Errorf("[MainLevelTable]unmarshal csv record failed, varName=ChapterLevel, type=ref@ChapterLevelTable, value=%s, err:[%s]", record[t.getIndexInCsv("ChapterLevel")], err)
				}
			}
		}
		// Chapter
		{
			if record[t.getIndexInCsv("Chapter")] == "" {
				recordCfg.Chapter = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Chapter")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MainLevelTable]unmarshal csv record failed, varName=Chapter, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Chapter")], err)
					} else {
						return fmt.Errorf("[MainLevelTable]unmarshal csv record failed, varName=Chapter, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Chapter")], err)
					}
				}
				recordCfg.Chapter = int32(cfgoInt)
			}
		}
		// Level
		{
			if record[t.getIndexInCsv("Level")] == "" {
				recordCfg.Level = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Level")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MainLevelTable]unmarshal csv record failed, varName=Level, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Level")], err)
					} else {
						return fmt.Errorf("[MainLevelTable]unmarshal csv record failed, varName=Level, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Level")], err)
					}
				}
				recordCfg.Level = int32(cfgoInt)
			}
		}
		// EliteMonsterAtkRatio
		{
			if record[t.getIndexInCsv("EliteMonsterAtkRatio")] == "" {
				recordCfg.EliteMonsterAtkRatio = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("EliteMonsterAtkRatio")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MainLevelTable]unmarshal csv record failed, varName=EliteMonsterAtkRatio, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("EliteMonsterAtkRatio")], err)
					} else {
						return fmt.Errorf("[MainLevelTable]unmarshal csv record failed, varName=EliteMonsterAtkRatio, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("EliteMonsterAtkRatio")], err)
					}
				}
				recordCfg.EliteMonsterAtkRatio = float32(cfgoFloat)
			}
		}
		// EliteMonsterDefRatio
		{
			if record[t.getIndexInCsv("EliteMonsterDefRatio")] == "" {
				recordCfg.EliteMonsterDefRatio = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("EliteMonsterDefRatio")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MainLevelTable]unmarshal csv record failed, varName=EliteMonsterDefRatio, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("EliteMonsterDefRatio")], err)
					} else {
						return fmt.Errorf("[MainLevelTable]unmarshal csv record failed, varName=EliteMonsterDefRatio, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("EliteMonsterDefRatio")], err)
					}
				}
				recordCfg.EliteMonsterDefRatio = float32(cfgoFloat)
			}
		}
		// EliteMonsterHpRatio
		{
			if record[t.getIndexInCsv("EliteMonsterHpRatio")] == "" {
				recordCfg.EliteMonsterHpRatio = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("EliteMonsterHpRatio")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MainLevelTable]unmarshal csv record failed, varName=EliteMonsterHpRatio, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("EliteMonsterHpRatio")], err)
					} else {
						return fmt.Errorf("[MainLevelTable]unmarshal csv record failed, varName=EliteMonsterHpRatio, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("EliteMonsterHpRatio")], err)
					}
				}
				recordCfg.EliteMonsterHpRatio = float32(cfgoFloat)
			}
		}
		// LevelType
		{
			if record[t.getIndexInCsv("LevelType")] == "" {
				recordCfg.LevelType = LevelType(enumDefaultValue)
			} else {
				cfgoEnum, err := parseLevelType(record[t.getIndexInCsv("LevelType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MainLevelTable]unmarshal csv record failed, varName=LevelType, type=enum@LevelType, value=%s, err:[%s]\n", record[t.getIndexInCsv("LevelType")], err)
					} else {
						return fmt.Errorf("[MainLevelTable]unmarshal csv record failed, varName=LevelType, type=enum@LevelType, value=%s, err:[%s]", record[t.getIndexInCsv("LevelType")], err)
					}
				}
				recordCfg.LevelType = cfgoEnum
			}
		}
		// KillRewardRougeTabCntScheme
		if record[t.getIndexInCsv("KillRewardRougeTabCntScheme")] == "" {
			recordCfg.KillRewardRougeTabCntScheme = 0
		} else {
			var err error
			recordCfg.KillRewardRougeTabCntScheme, err = configs.MainLevelRogueRewardWeightTable.getIdByRef(record[t.getIndexInCsv("KillRewardRougeTabCntScheme")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [MainLevelTable]unmarshal csv record failed, varName=KillRewardRougeTabCntScheme, type=ref@MainLevelRogueRewardWeightTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("KillRewardRougeTabCntScheme")], err)
				} else {
					return fmt.Errorf("[MainLevelTable]unmarshal csv record failed, varName=KillRewardRougeTabCntScheme, type=ref@MainLevelRogueRewardWeightTable, value=%s, err:[%s]", record[t.getIndexInCsv("KillRewardRougeTabCntScheme")], err)
				}
			}
		}
		// CommonChallengeReward
		if record[t.getIndexInCsv("CommonChallengeReward")] == "" {
			recordCfg.CommonChallengeReward = 0
		} else {
			var err error
			recordCfg.CommonChallengeReward, err = configs.MainLevelRewardTable.getIdByRef(record[t.getIndexInCsv("CommonChallengeReward")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [MainLevelTable]unmarshal csv record failed, varName=CommonChallengeReward, type=ref@MainLevelRewardTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("CommonChallengeReward")], err)
				} else {
					return fmt.Errorf("[MainLevelTable]unmarshal csv record failed, varName=CommonChallengeReward, type=ref@MainLevelRewardTable, value=%s, err:[%s]", record[t.getIndexInCsv("CommonChallengeReward")], err)
				}
			}
		}
		// EliteChallengeReward
		if record[t.getIndexInCsv("EliteChallengeReward")] == "" {
			recordCfg.EliteChallengeReward = 0
		} else {
			var err error
			recordCfg.EliteChallengeReward, err = configs.MainLevelRewardTable.getIdByRef(record[t.getIndexInCsv("EliteChallengeReward")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [MainLevelTable]unmarshal csv record failed, varName=EliteChallengeReward, type=ref@MainLevelRewardTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("EliteChallengeReward")], err)
				} else {
					return fmt.Errorf("[MainLevelTable]unmarshal csv record failed, varName=EliteChallengeReward, type=ref@MainLevelRewardTable, value=%s, err:[%s]", record[t.getIndexInCsv("EliteChallengeReward")], err)
				}
			}
		}
		// OneStarReward
		if record[t.getIndexInCsv("OneStarReward")] == "" {
			recordCfg.OneStarReward = 0
		} else {
			var err error
			recordCfg.OneStarReward, err = configs.MainLevelPassRewardTable.getIdByRef(record[t.getIndexInCsv("OneStarReward")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [MainLevelTable]unmarshal csv record failed, varName=OneStarReward, type=ref@MainLevelPassRewardTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("OneStarReward")], err)
				} else {
					return fmt.Errorf("[MainLevelTable]unmarshal csv record failed, varName=OneStarReward, type=ref@MainLevelPassRewardTable, value=%s, err:[%s]", record[t.getIndexInCsv("OneStarReward")], err)
				}
			}
		}
		// TwoStarReward
		if record[t.getIndexInCsv("TwoStarReward")] == "" {
			recordCfg.TwoStarReward = 0
		} else {
			var err error
			recordCfg.TwoStarReward, err = configs.MainLevelPassRewardTable.getIdByRef(record[t.getIndexInCsv("TwoStarReward")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [MainLevelTable]unmarshal csv record failed, varName=TwoStarReward, type=ref@MainLevelPassRewardTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("TwoStarReward")], err)
				} else {
					return fmt.Errorf("[MainLevelTable]unmarshal csv record failed, varName=TwoStarReward, type=ref@MainLevelPassRewardTable, value=%s, err:[%s]", record[t.getIndexInCsv("TwoStarReward")], err)
				}
			}
		}
		// ThreeStarReward
		if record[t.getIndexInCsv("ThreeStarReward")] == "" {
			recordCfg.ThreeStarReward = 0
		} else {
			var err error
			recordCfg.ThreeStarReward, err = configs.MainLevelPassRewardTable.getIdByRef(record[t.getIndexInCsv("ThreeStarReward")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [MainLevelTable]unmarshal csv record failed, varName=ThreeStarReward, type=ref@MainLevelPassRewardTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("ThreeStarReward")], err)
				} else {
					return fmt.Errorf("[MainLevelTable]unmarshal csv record failed, varName=ThreeStarReward, type=ref@MainLevelPassRewardTable, value=%s, err:[%s]", record[t.getIndexInCsv("ThreeStarReward")], err)
				}
			}
		}
		// ExtraReward
		{
			cfgoMeetNilForExtraRewardOfRecordCfg := false
			// element 0 of ExtraReward
			if !cfgoMeetNilForExtraRewardOfRecordCfg {
				cfgoMeetNilForExtraRewardOfRecordCfg = true
				var cfgoElemOfExtraRewardOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("ExtraReward1RewardType")] != "" {
						cfgoMeetNilForExtraRewardOfRecordCfg = false
						var err error
						cfgoElemOfExtraRewardOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("ExtraReward1RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MainLevelTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfExtraRewardOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("ExtraReward1RewardType")], err)
							} else {
								return fmt.Errorf("[MainLevelTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfExtraRewardOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("ExtraReward1RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("ExtraReward1RewardValue")] != "" {
						cfgoMeetNilForExtraRewardOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("ExtraReward1RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MainLevelTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfExtraRewardOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("ExtraReward1RewardValue")], err)
							} else {
								return fmt.Errorf("[MainLevelTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfExtraRewardOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("ExtraReward1RewardValue")], err)
							}
						}
						cfgoElemOfExtraRewardOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForExtraRewardOfRecordCfg {
					recordCfg.ExtraReward = append(recordCfg.ExtraReward, cfgoElemOfExtraRewardOfRecordCfg)
				}
			}

		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [MainLevelTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[MainLevelTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *MainLevelTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "MainLevelTable.csv") && (!strings.HasPrefix(fileName, "MainLevelTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for MainLevelTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[MainLevelTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[MainLevelTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[MainLevelTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[MainLevelTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[MainLevelTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[MainLevelTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[MainLevelTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[MainLevelTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [MainLevelTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *MainLevelTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[MainLevelTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [MainLevelTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *MainLevelTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[MainLevelTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *MainLevelTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[MainLevelTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[MainLevelTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
