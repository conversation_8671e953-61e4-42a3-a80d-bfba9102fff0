// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"fmt"
	"strconv"
)

func parseAttrDefaultType(enumStr string) (AttrDefaultType, error) {
	switch enumStr {
	case "数字":
		return AttrDefaultType_Number, nil
	case "模型":
		return AttrDefaultType_Model, nil
	case "Buff":
		return AttrDefaultType_Buff, nil
	case "Effect":
		return AttrDefaultType_Effect, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(AttrDefaultType_Number):
			return AttrDefaultType_Number, nil
		case int(AttrDefaultType_Model):
			return AttrDefaultType_Model, nil
		case int(AttrDefaultType_Buff):
			return AttrDefaultType_Buff, nil
		case int(AttrDefaultType_Effect):
			return AttrDefaultType_Effect, nil
		}
	}
	return AttrDefaultType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for AttrDefaultType", enumStr)
}

func parseBagType(enumStr string) (BagType, error) {
	switch enumStr {
	case "植物":
		return BagType_Plants, nil
	case "消耗":
		return BagType_Consumption, nil
	case "材料":
		return BagType_Materials, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(BagType_Plants):
			return BagType_Plants, nil
		case int(BagType_Consumption):
			return BagType_Consumption, nil
		case int(BagType_Materials):
			return BagType_Materials, nil
		}
	}
	return BagType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for BagType", enumStr)
}

func parseBenefitCalcFormula(enumStr string) (BenefitCalcFormula, error) {
	switch enumStr {
	case "BaseValue+para1":
		return BenefitCalcFormula_Formula1, nil
	case "BaseValue*(1+para1)":
		return BenefitCalcFormula_Formula2, nil
	case "[BaseValue*(1+para1)+para2]*(1+para3)":
		return BenefitCalcFormula_Formula3, nil
	case "(BaseValue+para1)*(1+para2)*(1+para3)":
		return BenefitCalcFormula_Formula4, nil
	case "(BaseValue+para1)*(1+para2)":
		return BenefitCalcFormula_Formula5, nil
	case "BaseValue/(1+para1)":
		return BenefitCalcFormula_Formula6, nil
	case "(BaseValue+para1)/(1+para2)":
		return BenefitCalcFormula_Formula7, nil
	case "BaseValue*(1+para1)/(1+para2)":
		return BenefitCalcFormula_Formula8, nil
	case "BaseValue*(1-para1)":
		return BenefitCalcFormula_Formula9, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(BenefitCalcFormula_Formula1):
			return BenefitCalcFormula_Formula1, nil
		case int(BenefitCalcFormula_Formula2):
			return BenefitCalcFormula_Formula2, nil
		case int(BenefitCalcFormula_Formula3):
			return BenefitCalcFormula_Formula3, nil
		case int(BenefitCalcFormula_Formula4):
			return BenefitCalcFormula_Formula4, nil
		case int(BenefitCalcFormula_Formula5):
			return BenefitCalcFormula_Formula5, nil
		case int(BenefitCalcFormula_Formula6):
			return BenefitCalcFormula_Formula6, nil
		case int(BenefitCalcFormula_Formula7):
			return BenefitCalcFormula_Formula7, nil
		case int(BenefitCalcFormula_Formula8):
			return BenefitCalcFormula_Formula8, nil
		case int(BenefitCalcFormula_Formula9):
			return BenefitCalcFormula_Formula9, nil
		}
	}
	return BenefitCalcFormula(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for BenefitCalcFormula", enumStr)
}

func parseBuffOverlyingType(enumStr string) (BuffOverlyingType, error) {
	switch enumStr {
	case "不可叠加":
		return BuffOverlyingType_NoOverlying, nil
	case "时间叠加":
		return BuffOverlyingType_TimeOverlying, nil
	case "效果叠加":
		return BuffOverlyingType_EffectOverlying, nil
	case "效果覆盖":
		return BuffOverlyingType_EffectCover, nil
	case "效果替换":
		return BuffOverlyingType_EffectReplace, nil
	case "效果影分身":
		return BuffOverlyingType_EffectMultiple, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(BuffOverlyingType_NoOverlying):
			return BuffOverlyingType_NoOverlying, nil
		case int(BuffOverlyingType_TimeOverlying):
			return BuffOverlyingType_TimeOverlying, nil
		case int(BuffOverlyingType_EffectOverlying):
			return BuffOverlyingType_EffectOverlying, nil
		case int(BuffOverlyingType_EffectCover):
			return BuffOverlyingType_EffectCover, nil
		case int(BuffOverlyingType_EffectReplace):
			return BuffOverlyingType_EffectReplace, nil
		case int(BuffOverlyingType_EffectMultiple):
			return BuffOverlyingType_EffectMultiple, nil
		}
	}
	return BuffOverlyingType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for BuffOverlyingType", enumStr)
}

func parseBuffTarget(enumStr string) (BuffTarget, error) {
	switch enumStr {
	case "自己":
		return BuffTarget_Own, nil
	case "己方":
		return BuffTarget_OwnSide, nil
	case "己方防御型英雄":
		return BuffTarget_OwnSideHeroDefense, nil
	case "己方远程型英雄":
		return BuffTarget_OwnSideHeroRanged, nil
	case "己方功能型英雄":
		return BuffTarget_OwnSideHeroSupport, nil
	case "己方前排英雄":
		return BuffTarget_OwnSideHeroFront, nil
	case "己方后排英雄":
		return BuffTarget_OwnSideHeroBehind, nil
	case "己方生命值最低英雄（同值随机）":
		return BuffTarget_OwnSideHeroHPLowest, nil
	case "自动攻击目标":
		return BuffTarget_Opposite, nil
	case "对方":
		return BuffTarget_OppositeSide, nil
	case "对方防御型英雄":
		return BuffTarget_OppositeSideHeroDefense, nil
	case "对方远程型英雄":
		return BuffTarget_OppositeSideHeroRanged, nil
	case "对方功能型英雄":
		return BuffTarget_OppositeSideHeroSupport, nil
	case "对方前排英雄":
		return BuffTarget_OppoSideHeroFront, nil
	case "对方后排英雄":
		return BuffTarget_OppoSideHeroBehind, nil
	case "对方生命值最低英雄（同值随机）":
		return BuffTarget_OppositeSideHeroHpLowest, nil
	case "前一技能段影响目标":
		return BuffTarget_PreSkillEffectTarget, nil
	case "前一技能段影响目标（除boss）":
		return BuffTarget_PreSkillEffectTargetElseBoss, nil
	case "boss":
		return BuffTarget_Boss, nil
	case "载具":
		return BuffTarget_Vehicle, nil
	case "自己前方":
		return BuffTarget_OwnForward, nil
	case "己方魔法英雄":
		return BuffTarget_OwnSideHeroMagic, nil
	case "己方异能英雄":
		return BuffTarget_OwnSideHeroSuperPowers, nil
	case "己方科技英雄":
		return BuffTarget_OwnSideHeroTech, nil
	case "对方魔法英雄":
		return BuffTarget_OppositeSideHeroMagic, nil
	case "对方异能英雄":
		return BuffTarget_OppositeSideHeroSuperPowers, nil
	case "对方科技英雄":
		return BuffTarget_OppositeSideHeroTech, nil
	case "己方攻击力最高英雄（同值随机）":
		return BuffTarget_OwnSideHeroAtkHighest, nil
	case "己方后排魔法英雄":
		return BuffTarget_OwnSideHeroBehindMagic, nil
	case "己方后排科技英雄":
		return BuffTarget_OwnSideHeroBehindTech, nil
	case "己方随机前排防御型英雄":
		return BuffTarget_OwnSideHeroFrontDefenseRandom, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(BuffTarget_Own):
			return BuffTarget_Own, nil
		case int(BuffTarget_OwnSide):
			return BuffTarget_OwnSide, nil
		case int(BuffTarget_OwnSideHeroDefense):
			return BuffTarget_OwnSideHeroDefense, nil
		case int(BuffTarget_OwnSideHeroRanged):
			return BuffTarget_OwnSideHeroRanged, nil
		case int(BuffTarget_OwnSideHeroSupport):
			return BuffTarget_OwnSideHeroSupport, nil
		case int(BuffTarget_OwnSideHeroFront):
			return BuffTarget_OwnSideHeroFront, nil
		case int(BuffTarget_OwnSideHeroBehind):
			return BuffTarget_OwnSideHeroBehind, nil
		case int(BuffTarget_OwnSideHeroHPLowest):
			return BuffTarget_OwnSideHeroHPLowest, nil
		case int(BuffTarget_Opposite):
			return BuffTarget_Opposite, nil
		case int(BuffTarget_OppositeSide):
			return BuffTarget_OppositeSide, nil
		case int(BuffTarget_OppositeSideHeroDefense):
			return BuffTarget_OppositeSideHeroDefense, nil
		case int(BuffTarget_OppositeSideHeroRanged):
			return BuffTarget_OppositeSideHeroRanged, nil
		case int(BuffTarget_OppositeSideHeroSupport):
			return BuffTarget_OppositeSideHeroSupport, nil
		case int(BuffTarget_OppoSideHeroFront):
			return BuffTarget_OppoSideHeroFront, nil
		case int(BuffTarget_OppoSideHeroBehind):
			return BuffTarget_OppoSideHeroBehind, nil
		case int(BuffTarget_OppositeSideHeroHpLowest):
			return BuffTarget_OppositeSideHeroHpLowest, nil
		case int(BuffTarget_PreSkillEffectTarget):
			return BuffTarget_PreSkillEffectTarget, nil
		case int(BuffTarget_PreSkillEffectTargetElseBoss):
			return BuffTarget_PreSkillEffectTargetElseBoss, nil
		case int(BuffTarget_Boss):
			return BuffTarget_Boss, nil
		case int(BuffTarget_Vehicle):
			return BuffTarget_Vehicle, nil
		case int(BuffTarget_OwnForward):
			return BuffTarget_OwnForward, nil
		case int(BuffTarget_OwnSideHeroMagic):
			return BuffTarget_OwnSideHeroMagic, nil
		case int(BuffTarget_OwnSideHeroSuperPowers):
			return BuffTarget_OwnSideHeroSuperPowers, nil
		case int(BuffTarget_OwnSideHeroTech):
			return BuffTarget_OwnSideHeroTech, nil
		case int(BuffTarget_OppositeSideHeroMagic):
			return BuffTarget_OppositeSideHeroMagic, nil
		case int(BuffTarget_OppositeSideHeroSuperPowers):
			return BuffTarget_OppositeSideHeroSuperPowers, nil
		case int(BuffTarget_OppositeSideHeroTech):
			return BuffTarget_OppositeSideHeroTech, nil
		case int(BuffTarget_OwnSideHeroAtkHighest):
			return BuffTarget_OwnSideHeroAtkHighest, nil
		case int(BuffTarget_OwnSideHeroBehindMagic):
			return BuffTarget_OwnSideHeroBehindMagic, nil
		case int(BuffTarget_OwnSideHeroBehindTech):
			return BuffTarget_OwnSideHeroBehindTech, nil
		case int(BuffTarget_OwnSideHeroFrontDefenseRandom):
			return BuffTarget_OwnSideHeroFrontDefenseRandom, nil
		}
	}
	return BuffTarget(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for BuffTarget", enumStr)
}

func parseBuffType(enumStr string) (BuffType, error) {
	switch enumStr {
	case "增益":
		return BuffType_Buff, nil
	case "减益":
		return BuffType_Debuff, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(BuffType_Buff):
			return BuffType_Buff, nil
		case int(BuffType_Debuff):
			return BuffType_Debuff, nil
		}
	}
	return BuffType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for BuffType", enumStr)
}

func parseCorrectType(enumStr string) (CorrectType, error) {
	switch enumStr {
	case "叠加":
		return CorrectType_Overlying, nil
	case "覆盖":
		return CorrectType_Cover, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(CorrectType_Overlying):
			return CorrectType_Overlying, nil
		case int(CorrectType_Cover):
			return CorrectType_Cover, nil
		}
	}
	return CorrectType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for CorrectType", enumStr)
}

func parseDailyOrWeekly(enumStr string) (DailyOrWeekly, error) {
	switch enumStr {
	case "日常":
		return DailyOrWeekly_Daily, nil
	case "周常":
		return DailyOrWeekly_Weekly, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(DailyOrWeekly_Daily):
			return DailyOrWeekly_Daily, nil
		case int(DailyOrWeekly_Weekly):
			return DailyOrWeekly_Weekly, nil
		}
	}
	return DailyOrWeekly(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for DailyOrWeekly", enumStr)
}

func parseDungeonType(enumStr string) (DungeonType, error) {
	switch enumStr {
	case "金币副本":
		return DungeonType_CoinDungeon, nil
	case "技能书副本":
		return DungeonType_GeneDungeon, nil
	case "培养仓齿轮副本":
		return DungeonType_LordEquipDungeon, nil
	case "阳光副本":
		return DungeonType_SunshineDungeon, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(DungeonType_CoinDungeon):
			return DungeonType_CoinDungeon, nil
		case int(DungeonType_GeneDungeon):
			return DungeonType_GeneDungeon, nil
		case int(DungeonType_LordEquipDungeon):
			return DungeonType_LordEquipDungeon, nil
		case int(DungeonType_SunshineDungeon):
			return DungeonType_SunshineDungeon, nil
		}
	}
	return DungeonType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for DungeonType", enumStr)
}

func parseGemAffixQuality(enumStr string) (GemAffixQuality, error) {
	switch enumStr {
	case "专属":
		return GemAffixQuality_GemAffixQuality1, nil
	case "稀有":
		return GemAffixQuality_GemAffixQuality2, nil
	case "基础":
		return GemAffixQuality_GemAffixQuality3, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(GemAffixQuality_GemAffixQuality1):
			return GemAffixQuality_GemAffixQuality1, nil
		case int(GemAffixQuality_GemAffixQuality2):
			return GemAffixQuality_GemAffixQuality2, nil
		case int(GemAffixQuality_GemAffixQuality3):
			return GemAffixQuality_GemAffixQuality3, nil
		}
	}
	return GemAffixQuality(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for GemAffixQuality", enumStr)
}

func parseGemQualityType(enumStr string) (GemQualityType, error) {
	switch enumStr {
	case "破损":
		return GemQualityType_GemQualityType1, nil
	case "普通":
		return GemQualityType_GemQualityType2, nil
	case "优秀":
		return GemQualityType_GemQualityType3, nil
	case "史诗":
		return GemQualityType_GemQualityType4, nil
	case "传说":
		return GemQualityType_GemQualityType5, nil
	case "神话":
		return GemQualityType_GemQualityType6, nil
	case "至尊":
		return GemQualityType_GemQualityType7, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(GemQualityType_GemQualityType1):
			return GemQualityType_GemQualityType1, nil
		case int(GemQualityType_GemQualityType2):
			return GemQualityType_GemQualityType2, nil
		case int(GemQualityType_GemQualityType3):
			return GemQualityType_GemQualityType3, nil
		case int(GemQualityType_GemQualityType4):
			return GemQualityType_GemQualityType4, nil
		case int(GemQualityType_GemQualityType5):
			return GemQualityType_GemQualityType5, nil
		case int(GemQualityType_GemQualityType6):
			return GemQualityType_GemQualityType6, nil
		case int(GemQualityType_GemQualityType7):
			return GemQualityType_GemQualityType7, nil
		}
	}
	return GemQualityType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for GemQualityType", enumStr)
}

func parseGuildFlagType(enumStr string) (GuildFlagType, error) {
	switch enumStr {
	case "底座":
		return GuildFlagType_Base, nil
	case "纹章":
		return GuildFlagType_Badge, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(GuildFlagType_Base):
			return GuildFlagType_Base, nil
		case int(GuildFlagType_Badge):
			return GuildFlagType_Badge, nil
		}
	}
	return GuildFlagType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for GuildFlagType", enumStr)
}

func parseGuildPermission(enumStr string) (GuildPermission, error) {
	switch enumStr {
	case "变更旗帜":
		return GuildPermission_ChangeGuildFlag, nil
	case "变更简称":
		return GuildPermission_ChangeGuildShortName, nil
	case "变更名称":
		return GuildPermission_ChangeGuildName, nil
	case "编辑公告":
		return GuildPermission_EditNotice, nil
	case "变更招募设定":
		return GuildPermission_ChangeRecruitSetting, nil
	case "管理入会申請":
		return GuildPermission_ManageJoinApplication, nil
	case "解散公会":
		return GuildPermission_DisbandGuild, nil
	case "转让会长":
		return GuildPermission_TransferPresident, nil
	case "移除成员":
		return GuildPermission_RemoveMember, nil
	case "变更成员阶级":
		return GuildPermission_ChangeMemberRank, nil
	case "查看成员信息":
		return GuildPermission_ViewMemberInfo, nil
	case "变更阶级头衔":
		return GuildPermission_ChangeRankTitle, nil
	case "变更语言":
		return GuildPermission_ChangeLanguage, nil
	case "退出公会":
		return GuildPermission_ExitGuild, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(GuildPermission_ChangeGuildFlag):
			return GuildPermission_ChangeGuildFlag, nil
		case int(GuildPermission_ChangeGuildShortName):
			return GuildPermission_ChangeGuildShortName, nil
		case int(GuildPermission_ChangeGuildName):
			return GuildPermission_ChangeGuildName, nil
		case int(GuildPermission_EditNotice):
			return GuildPermission_EditNotice, nil
		case int(GuildPermission_ChangeRecruitSetting):
			return GuildPermission_ChangeRecruitSetting, nil
		case int(GuildPermission_ManageJoinApplication):
			return GuildPermission_ManageJoinApplication, nil
		case int(GuildPermission_DisbandGuild):
			return GuildPermission_DisbandGuild, nil
		case int(GuildPermission_TransferPresident):
			return GuildPermission_TransferPresident, nil
		case int(GuildPermission_RemoveMember):
			return GuildPermission_RemoveMember, nil
		case int(GuildPermission_ChangeMemberRank):
			return GuildPermission_ChangeMemberRank, nil
		case int(GuildPermission_ViewMemberInfo):
			return GuildPermission_ViewMemberInfo, nil
		case int(GuildPermission_ChangeRankTitle):
			return GuildPermission_ChangeRankTitle, nil
		case int(GuildPermission_ChangeLanguage):
			return GuildPermission_ChangeLanguage, nil
		case int(GuildPermission_ExitGuild):
			return GuildPermission_ExitGuild, nil
		}
	}
	return GuildPermission(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for GuildPermission", enumStr)
}

func parseGuildRank(enumStr string) (GuildRank, error) {
	switch enumStr {
	case "5阶":
		return GuildRank_Rank5, nil
	case "4阶":
		return GuildRank_Rank4, nil
	case "3阶":
		return GuildRank_Rank3, nil
	case "2阶":
		return GuildRank_Rank2, nil
	case "1阶":
		return GuildRank_Rank1, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(GuildRank_Rank5):
			return GuildRank_Rank5, nil
		case int(GuildRank_Rank4):
			return GuildRank_Rank4, nil
		case int(GuildRank_Rank3):
			return GuildRank_Rank3, nil
		case int(GuildRank_Rank2):
			return GuildRank_Rank2, nil
		case int(GuildRank_Rank1):
			return GuildRank_Rank1, nil
		}
	}
	return GuildRank(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for GuildRank", enumStr)
}

func parseHeroCareer(enumStr string) (HeroCareer, error) {
	switch enumStr {
	case "防御型":
		return HeroCareer_HeroDefense, nil
	case "远程型":
		return HeroCareer_HeroRanged, nil
	case "支援型":
		return HeroCareer_HeroSupport, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(HeroCareer_HeroDefense):
			return HeroCareer_HeroDefense, nil
		case int(HeroCareer_HeroRanged):
			return HeroCareer_HeroRanged, nil
		case int(HeroCareer_HeroSupport):
			return HeroCareer_HeroSupport, nil
		}
	}
	return HeroCareer(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for HeroCareer", enumStr)
}

func parseHeroConfig(enumStr string) (HeroConfig, error) {
	switch enumStr {
	case "前排左":
		return HeroConfig_HeroConfig1, nil
	case "后排中":
		return HeroConfig_HeroConfig2, nil
	case "后排左":
		return HeroConfig_HeroConfig3, nil
	case "前排右":
		return HeroConfig_HeroConfig4, nil
	case "后排右":
		return HeroConfig_HeroConfig5, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(HeroConfig_HeroConfig1):
			return HeroConfig_HeroConfig1, nil
		case int(HeroConfig_HeroConfig2):
			return HeroConfig_HeroConfig2, nil
		case int(HeroConfig_HeroConfig3):
			return HeroConfig_HeroConfig3, nil
		case int(HeroConfig_HeroConfig4):
			return HeroConfig_HeroConfig4, nil
		case int(HeroConfig_HeroConfig5):
			return HeroConfig_HeroConfig5, nil
		}
	}
	return HeroConfig(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for HeroConfig", enumStr)
}

func parseHeroLevelUpPlan(enumStr string) (HeroLevelUpPlan, error) {
	switch enumStr {
	case "ssr肉盾升星方案":
		return HeroLevelUpPlan_HeroLevelPlanLegendaryDefense, nil
	case "ssr远程升星方案":
		return HeroLevelUpPlan_HeroLevelPlanLegendaryRanged, nil
	case "ssr支援升星方案":
		return HeroLevelUpPlan_HeroLevelPlanLegendarySupport, nil
	case "sr肉盾升星方案":
		return HeroLevelUpPlan_HeroLevelPlanEpicDefense, nil
	case "sr远程升星方案":
		return HeroLevelUpPlan_HeroLevelPlanEpicRanged, nil
	case "sr支援升星方案":
		return HeroLevelUpPlan_HeroLevelPlanEpicSupport, nil
	case "r肉盾升星方案":
		return HeroLevelUpPlan_HeroLevelPlanRareDefense, nil
	case "r远程升星方案":
		return HeroLevelUpPlan_HeroLevelPlanRareRanged, nil
	case "r支援升星方案":
		return HeroLevelUpPlan_HeroLevelPlanRareSupport, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(HeroLevelUpPlan_HeroLevelPlanLegendaryDefense):
			return HeroLevelUpPlan_HeroLevelPlanLegendaryDefense, nil
		case int(HeroLevelUpPlan_HeroLevelPlanLegendaryRanged):
			return HeroLevelUpPlan_HeroLevelPlanLegendaryRanged, nil
		case int(HeroLevelUpPlan_HeroLevelPlanLegendarySupport):
			return HeroLevelUpPlan_HeroLevelPlanLegendarySupport, nil
		case int(HeroLevelUpPlan_HeroLevelPlanEpicDefense):
			return HeroLevelUpPlan_HeroLevelPlanEpicDefense, nil
		case int(HeroLevelUpPlan_HeroLevelPlanEpicRanged):
			return HeroLevelUpPlan_HeroLevelPlanEpicRanged, nil
		case int(HeroLevelUpPlan_HeroLevelPlanEpicSupport):
			return HeroLevelUpPlan_HeroLevelPlanEpicSupport, nil
		case int(HeroLevelUpPlan_HeroLevelPlanRareDefense):
			return HeroLevelUpPlan_HeroLevelPlanRareDefense, nil
		case int(HeroLevelUpPlan_HeroLevelPlanRareRanged):
			return HeroLevelUpPlan_HeroLevelPlanRareRanged, nil
		case int(HeroLevelUpPlan_HeroLevelPlanRareSupport):
			return HeroLevelUpPlan_HeroLevelPlanRareSupport, nil
		}
	}
	return HeroLevelUpPlan(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for HeroLevelUpPlan", enumStr)
}

func parseHeroQuality(enumStr string) (HeroQuality, error) {
	switch enumStr {
	case "传说":
		return HeroQuality_HeroLegendary, nil
	case "史诗":
		return HeroQuality_HeroEpic, nil
	case "稀有":
		return HeroQuality_HeroRare, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(HeroQuality_HeroLegendary):
			return HeroQuality_HeroLegendary, nil
		case int(HeroQuality_HeroEpic):
			return HeroQuality_HeroEpic, nil
		case int(HeroQuality_HeroRare):
			return HeroQuality_HeroRare, nil
		}
	}
	return HeroQuality(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for HeroQuality", enumStr)
}

func parseHeroSkillBuffType(enumStr string) (HeroSkillBuffType, error) {
	switch enumStr {
	case "Benefit":
		return HeroSkillBuffType_Benefit, nil
	case "沉默":
		return HeroSkillBuffType_Silent, nil
	case "眩晕":
		return HeroSkillBuffType_Stun, nil
	case "麻痹":
		return HeroSkillBuffType_Paralysis, nil
	case "睡眠":
		return HeroSkillBuffType_Sleep, nil
	case "束缚":
		return HeroSkillBuffType_Bind, nil
	case "不死":
		return HeroSkillBuffType_Immortal, nil
	case "伤害无效化":
		return HeroSkillBuffType_Veil, nil
	case "隐身":
		return HeroSkillBuffType_Stealth, nil
	case "诅咒":
		return HeroSkillBuffType_Curse, nil
	case "dot_流血":
		return HeroSkillBuffType_Dot_bleed, nil
	case "dot_中毒":
		return HeroSkillBuffType_Dot_poison, nil
	case "dot_冻伤":
		return HeroSkillBuffType_Dot_frostbite, nil
	case "dot_灼烧":
		return HeroSkillBuffType_Dot_burn, nil
	case "格挡":
		return HeroSkillBuffType_Block, nil
	case "无法复活":
		return HeroSkillBuffType_Unrevive, nil
	case "长睡不醒":
		return HeroSkillBuffType_EternalSlumber, nil
	case "硬直":
		return HeroSkillBuffType_Tense, nil
	case "免疫":
		return HeroSkillBuffType_Immunity, nil
	case "护盾":
		return HeroSkillBuffType_Shield, nil
	case "半睡半醒":
		return HeroSkillBuffType_HalfAsleep, nil
	case "梦魇":
		return HeroSkillBuffType_Nightmare, nil
	case "吸血":
		return HeroSkillBuffType_LifeSteal, nil
	case "复活":
		return HeroSkillBuffType_Revive, nil
	case "回血":
		return HeroSkillBuffType_HpRecovery, nil
	case "技能转换":
		return HeroSkillBuffType_SkillSwitch, nil
	case "挑衅":
		return HeroSkillBuffType_Taunted, nil
	case "伤害":
		return HeroSkillBuffType_Dmg, nil
	case "移除buff":
		return HeroSkillBuffType_RemoveBuff, nil
	case "死后爆炸":
		return HeroSkillBuffType_OnSideExplosion, nil
	case "冻结":
		return HeroSkillBuffType_Frozen, nil
	case "击退":
		return HeroSkillBuffType_Repel, nil
	case "牵引":
		return HeroSkillBuffType_Pull, nil
	case "招雷劈":
		return HeroSkillBuffType_LightingStruck, nil
	case "易伤":
		return HeroSkillBuffType_Vulnerability, nil
	case "减速":
		return HeroSkillBuffType_Lame, nil
	case "爆发移速":
		return HeroSkillBuffType_Rampage, nil
	case "Buff延时":
		return HeroSkillBuffType_BuffDelay, nil
	case "冷静期":
		return HeroSkillBuffType_CoolingOff, nil
	case "重伤":
		return HeroSkillBuffType_RecoveryDown, nil
	case "护甲":
		return HeroSkillBuffType_Armour, nil
	case "冻结回血":
		return HeroSkillBuffType_FrozenHpRecovery, nil
	case "电系溅射":
		return HeroSkillBuffType_ElectrostaticSputtering, nil
	case "受伤回血":
		return HeroSkillBuffType_InjuryHealing, nil
	case "召唤":
		return HeroSkillBuffType_Summon, nil
	case "螃蟹步":
		return HeroSkillBuffType_CrabWalk, nil
	case "无敌":
		return HeroSkillBuffType_Invulnerable, nil
	case "分裂":
		return HeroSkillBuffType_Split, nil
	case "触发器":
		return HeroSkillBuffType_Trigger, nil
	case "钻地":
		return HeroSkillBuffType_Excavation, nil
	case "墓碑":
		return HeroSkillBuffType_TombStone, nil
	case "即死":
		return HeroSkillBuffType_InstantDeath, nil
	case "群体爆发移速":
		return HeroSkillBuffType_RangeRampage, nil
	case "dot_风蚀":
		return HeroSkillBuffType_Dot_wind, nil
	case "条件触发器":
		return HeroSkillBuffType_ConditionTrigger, nil
	case "献祭":
		return HeroSkillBuffType_Immolate, nil
	case "生命交换机":
		return HeroSkillBuffType_HpSwitch, nil
	case "嫁衣":
		return HeroSkillBuffType_StepingStone, nil
	case "早死早托生":
		return HeroSkillBuffType_EDER, nil
	case "疾风":
		return HeroSkillBuffType_Shippuden, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(HeroSkillBuffType_Benefit):
			return HeroSkillBuffType_Benefit, nil
		case int(HeroSkillBuffType_Silent):
			return HeroSkillBuffType_Silent, nil
		case int(HeroSkillBuffType_Stun):
			return HeroSkillBuffType_Stun, nil
		case int(HeroSkillBuffType_Paralysis):
			return HeroSkillBuffType_Paralysis, nil
		case int(HeroSkillBuffType_Sleep):
			return HeroSkillBuffType_Sleep, nil
		case int(HeroSkillBuffType_Bind):
			return HeroSkillBuffType_Bind, nil
		case int(HeroSkillBuffType_Immortal):
			return HeroSkillBuffType_Immortal, nil
		case int(HeroSkillBuffType_Veil):
			return HeroSkillBuffType_Veil, nil
		case int(HeroSkillBuffType_Stealth):
			return HeroSkillBuffType_Stealth, nil
		case int(HeroSkillBuffType_Curse):
			return HeroSkillBuffType_Curse, nil
		case int(HeroSkillBuffType_Dot_bleed):
			return HeroSkillBuffType_Dot_bleed, nil
		case int(HeroSkillBuffType_Dot_poison):
			return HeroSkillBuffType_Dot_poison, nil
		case int(HeroSkillBuffType_Dot_frostbite):
			return HeroSkillBuffType_Dot_frostbite, nil
		case int(HeroSkillBuffType_Dot_burn):
			return HeroSkillBuffType_Dot_burn, nil
		case int(HeroSkillBuffType_Block):
			return HeroSkillBuffType_Block, nil
		case int(HeroSkillBuffType_Unrevive):
			return HeroSkillBuffType_Unrevive, nil
		case int(HeroSkillBuffType_EternalSlumber):
			return HeroSkillBuffType_EternalSlumber, nil
		case int(HeroSkillBuffType_Tense):
			return HeroSkillBuffType_Tense, nil
		case int(HeroSkillBuffType_Immunity):
			return HeroSkillBuffType_Immunity, nil
		case int(HeroSkillBuffType_Shield):
			return HeroSkillBuffType_Shield, nil
		case int(HeroSkillBuffType_HalfAsleep):
			return HeroSkillBuffType_HalfAsleep, nil
		case int(HeroSkillBuffType_Nightmare):
			return HeroSkillBuffType_Nightmare, nil
		case int(HeroSkillBuffType_LifeSteal):
			return HeroSkillBuffType_LifeSteal, nil
		case int(HeroSkillBuffType_Revive):
			return HeroSkillBuffType_Revive, nil
		case int(HeroSkillBuffType_HpRecovery):
			return HeroSkillBuffType_HpRecovery, nil
		case int(HeroSkillBuffType_SkillSwitch):
			return HeroSkillBuffType_SkillSwitch, nil
		case int(HeroSkillBuffType_Taunted):
			return HeroSkillBuffType_Taunted, nil
		case int(HeroSkillBuffType_Dmg):
			return HeroSkillBuffType_Dmg, nil
		case int(HeroSkillBuffType_RemoveBuff):
			return HeroSkillBuffType_RemoveBuff, nil
		case int(HeroSkillBuffType_OnSideExplosion):
			return HeroSkillBuffType_OnSideExplosion, nil
		case int(HeroSkillBuffType_Frozen):
			return HeroSkillBuffType_Frozen, nil
		case int(HeroSkillBuffType_Repel):
			return HeroSkillBuffType_Repel, nil
		case int(HeroSkillBuffType_Pull):
			return HeroSkillBuffType_Pull, nil
		case int(HeroSkillBuffType_LightingStruck):
			return HeroSkillBuffType_LightingStruck, nil
		case int(HeroSkillBuffType_Vulnerability):
			return HeroSkillBuffType_Vulnerability, nil
		case int(HeroSkillBuffType_Lame):
			return HeroSkillBuffType_Lame, nil
		case int(HeroSkillBuffType_Rampage):
			return HeroSkillBuffType_Rampage, nil
		case int(HeroSkillBuffType_BuffDelay):
			return HeroSkillBuffType_BuffDelay, nil
		case int(HeroSkillBuffType_CoolingOff):
			return HeroSkillBuffType_CoolingOff, nil
		case int(HeroSkillBuffType_RecoveryDown):
			return HeroSkillBuffType_RecoveryDown, nil
		case int(HeroSkillBuffType_Armour):
			return HeroSkillBuffType_Armour, nil
		case int(HeroSkillBuffType_FrozenHpRecovery):
			return HeroSkillBuffType_FrozenHpRecovery, nil
		case int(HeroSkillBuffType_ElectrostaticSputtering):
			return HeroSkillBuffType_ElectrostaticSputtering, nil
		case int(HeroSkillBuffType_InjuryHealing):
			return HeroSkillBuffType_InjuryHealing, nil
		case int(HeroSkillBuffType_Summon):
			return HeroSkillBuffType_Summon, nil
		case int(HeroSkillBuffType_CrabWalk):
			return HeroSkillBuffType_CrabWalk, nil
		case int(HeroSkillBuffType_Invulnerable):
			return HeroSkillBuffType_Invulnerable, nil
		case int(HeroSkillBuffType_Split):
			return HeroSkillBuffType_Split, nil
		case int(HeroSkillBuffType_Trigger):
			return HeroSkillBuffType_Trigger, nil
		case int(HeroSkillBuffType_Excavation):
			return HeroSkillBuffType_Excavation, nil
		case int(HeroSkillBuffType_TombStone):
			return HeroSkillBuffType_TombStone, nil
		case int(HeroSkillBuffType_InstantDeath):
			return HeroSkillBuffType_InstantDeath, nil
		case int(HeroSkillBuffType_RangeRampage):
			return HeroSkillBuffType_RangeRampage, nil
		case int(HeroSkillBuffType_Dot_wind):
			return HeroSkillBuffType_Dot_wind, nil
		case int(HeroSkillBuffType_ConditionTrigger):
			return HeroSkillBuffType_ConditionTrigger, nil
		case int(HeroSkillBuffType_Immolate):
			return HeroSkillBuffType_Immolate, nil
		case int(HeroSkillBuffType_HpSwitch):
			return HeroSkillBuffType_HpSwitch, nil
		case int(HeroSkillBuffType_StepingStone):
			return HeroSkillBuffType_StepingStone, nil
		case int(HeroSkillBuffType_EDER):
			return HeroSkillBuffType_EDER, nil
		case int(HeroSkillBuffType_Shippuden):
			return HeroSkillBuffType_Shippuden, nil
		}
	}
	return HeroSkillBuffType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for HeroSkillBuffType", enumStr)
}

func parseHeroSkillDmgType(enumStr string) (HeroSkillDmgType, error) {
	switch enumStr {
	case "伤害值":
		return HeroSkillDmgType_DmgValue, nil
	case "伤害系数":
		return HeroSkillDmgType_DmgRatio, nil
	case "最大生命百分比伤害":
		return HeroSkillDmgType_MaxHpPerDmg, nil
	case "当前生命百分比伤害":
		return HeroSkillDmgType_CurHpPerDmg, nil
	case "损失生命百分比伤害":
		return HeroSkillDmgType_LossHpPerDmg, nil
	case "继承原技能的技能系数":
		return HeroSkillDmgType_InheritedSkillRatio, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(HeroSkillDmgType_DmgValue):
			return HeroSkillDmgType_DmgValue, nil
		case int(HeroSkillDmgType_DmgRatio):
			return HeroSkillDmgType_DmgRatio, nil
		case int(HeroSkillDmgType_MaxHpPerDmg):
			return HeroSkillDmgType_MaxHpPerDmg, nil
		case int(HeroSkillDmgType_CurHpPerDmg):
			return HeroSkillDmgType_CurHpPerDmg, nil
		case int(HeroSkillDmgType_LossHpPerDmg):
			return HeroSkillDmgType_LossHpPerDmg, nil
		case int(HeroSkillDmgType_InheritedSkillRatio):
			return HeroSkillDmgType_InheritedSkillRatio, nil
		}
	}
	return HeroSkillDmgType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for HeroSkillDmgType", enumStr)
}

func parseHeroSkillEffectType(enumStr string) (HeroSkillEffectType, error) {
	switch enumStr {
	case "Buff":
		return HeroSkillEffectType_Buff, nil
	case "玉米加特林":
		return HeroSkillEffectType_GuideLaser, nil
	case "火箭":
		return HeroSkillEffectType_BulletFire, nil
	case "冰霜玫瑰":
		return HeroSkillEffectType_IceRose, nil
	case "空气刀":
		return HeroSkillEffectType_BulletWind, nil
	case "激光":
		return HeroSkillEffectType_Laser, nil
	case "电刺":
		return HeroSkillEffectType_ElectricFierce, nil
	case "空投":
		return HeroSkillEffectType_Airdrop, nil
	case "电弧":
		return HeroSkillEffectType_ElectricArc, nil
	case "冰弹":
		return HeroSkillEffectType_BulletIce, nil
	case "龙焰":
		return HeroSkillEffectType_DragonFlame, nil
	case "遥控车":
		return HeroSkillEffectType_Car, nil
	case "霰弹":
		return HeroSkillEffectType_Shrapnel, nil
	case "旋风":
		return HeroSkillEffectType_Cyclone, nil
	case "豌豆弹":
		return HeroSkillEffectType_BulletPea, nil
	case "手里剑":
		return HeroSkillEffectType_HandSword, nil
	case "导弹":
		return HeroSkillEffectType_Missile, nil
	case "近战攻击":
		return HeroSkillEffectType_MeleeAtk, nil
	case "召唤怪物":
		return HeroSkillEffectType_SummonMonster, nil
	case "范围伤害":
		return HeroSkillEffectType_Aoe, nil
	case "解锁选项卡等级限制":
		return HeroSkillEffectType_UnlockTabLevelLimit, nil
	case "直接获得选项卡":
		return HeroSkillEffectType_GetTab, nil
	case "选项卡直接进卡池":
		return HeroSkillEffectType_UnlockTab, nil
	case "祖国人激光":
		return HeroSkillEffectType_HomelanderLaser, nil
	case "替换选项卡":
		return HeroSkillEffectType_ReplacedTab, nil
	case "Boss耳朵激光":
		return HeroSkillEffectType_BossEarLaser, nil
	case "Boss嘴巴激光":
		return HeroSkillEffectType_BossMouseLaser, nil
	case "Boss2":
		return HeroSkillEffectType_Boss2, nil
	case "多段近战攻击":
		return HeroSkillEffectType_MultipleMeleeAtk, nil
	case "自爆":
		return HeroSkillEffectType_SuicideBombing, nil
	case "Phase2Boss1":
		return HeroSkillEffectType_Phase2Boss1, nil
	case "Phase2Boss4":
		return HeroSkillEffectType_Phase2Boss6, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(HeroSkillEffectType_Buff):
			return HeroSkillEffectType_Buff, nil
		case int(HeroSkillEffectType_GuideLaser):
			return HeroSkillEffectType_GuideLaser, nil
		case int(HeroSkillEffectType_BulletFire):
			return HeroSkillEffectType_BulletFire, nil
		case int(HeroSkillEffectType_IceRose):
			return HeroSkillEffectType_IceRose, nil
		case int(HeroSkillEffectType_BulletWind):
			return HeroSkillEffectType_BulletWind, nil
		case int(HeroSkillEffectType_Laser):
			return HeroSkillEffectType_Laser, nil
		case int(HeroSkillEffectType_ElectricFierce):
			return HeroSkillEffectType_ElectricFierce, nil
		case int(HeroSkillEffectType_Airdrop):
			return HeroSkillEffectType_Airdrop, nil
		case int(HeroSkillEffectType_ElectricArc):
			return HeroSkillEffectType_ElectricArc, nil
		case int(HeroSkillEffectType_BulletIce):
			return HeroSkillEffectType_BulletIce, nil
		case int(HeroSkillEffectType_DragonFlame):
			return HeroSkillEffectType_DragonFlame, nil
		case int(HeroSkillEffectType_Car):
			return HeroSkillEffectType_Car, nil
		case int(HeroSkillEffectType_Shrapnel):
			return HeroSkillEffectType_Shrapnel, nil
		case int(HeroSkillEffectType_Cyclone):
			return HeroSkillEffectType_Cyclone, nil
		case int(HeroSkillEffectType_BulletPea):
			return HeroSkillEffectType_BulletPea, nil
		case int(HeroSkillEffectType_HandSword):
			return HeroSkillEffectType_HandSword, nil
		case int(HeroSkillEffectType_Missile):
			return HeroSkillEffectType_Missile, nil
		case int(HeroSkillEffectType_MeleeAtk):
			return HeroSkillEffectType_MeleeAtk, nil
		case int(HeroSkillEffectType_SummonMonster):
			return HeroSkillEffectType_SummonMonster, nil
		case int(HeroSkillEffectType_Aoe):
			return HeroSkillEffectType_Aoe, nil
		case int(HeroSkillEffectType_UnlockTabLevelLimit):
			return HeroSkillEffectType_UnlockTabLevelLimit, nil
		case int(HeroSkillEffectType_GetTab):
			return HeroSkillEffectType_GetTab, nil
		case int(HeroSkillEffectType_UnlockTab):
			return HeroSkillEffectType_UnlockTab, nil
		case int(HeroSkillEffectType_HomelanderLaser):
			return HeroSkillEffectType_HomelanderLaser, nil
		case int(HeroSkillEffectType_ReplacedTab):
			return HeroSkillEffectType_ReplacedTab, nil
		case int(HeroSkillEffectType_BossEarLaser):
			return HeroSkillEffectType_BossEarLaser, nil
		case int(HeroSkillEffectType_BossMouseLaser):
			return HeroSkillEffectType_BossMouseLaser, nil
		case int(HeroSkillEffectType_Boss2):
			return HeroSkillEffectType_Boss2, nil
		case int(HeroSkillEffectType_MultipleMeleeAtk):
			return HeroSkillEffectType_MultipleMeleeAtk, nil
		case int(HeroSkillEffectType_SuicideBombing):
			return HeroSkillEffectType_SuicideBombing, nil
		case int(HeroSkillEffectType_Phase2Boss1):
			return HeroSkillEffectType_Phase2Boss1, nil
		case int(HeroSkillEffectType_Phase2Boss6):
			return HeroSkillEffectType_Phase2Boss6, nil
		}
	}
	return HeroSkillEffectType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for HeroSkillEffectType", enumStr)
}

func parseHeroSkillHpRecoveryType(enumStr string) (HeroSkillHpRecoveryType, error) {
	switch enumStr {
	case "回血值":
		return HeroSkillHpRecoveryType_HpRecoveryValue, nil
	case "最大生命百分比回血":
		return HeroSkillHpRecoveryType_MaxHpPerRecovery, nil
	case "当前生命百分比回血":
		return HeroSkillHpRecoveryType_CurHpPerRecovery, nil
	case "损失生命百分比回血":
		return HeroSkillHpRecoveryType_LossHpPerRecovery, nil
	case "造成伤害量百分比回血":
		return HeroSkillHpRecoveryType_DmgPerRecovery, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(HeroSkillHpRecoveryType_HpRecoveryValue):
			return HeroSkillHpRecoveryType_HpRecoveryValue, nil
		case int(HeroSkillHpRecoveryType_MaxHpPerRecovery):
			return HeroSkillHpRecoveryType_MaxHpPerRecovery, nil
		case int(HeroSkillHpRecoveryType_CurHpPerRecovery):
			return HeroSkillHpRecoveryType_CurHpPerRecovery, nil
		case int(HeroSkillHpRecoveryType_LossHpPerRecovery):
			return HeroSkillHpRecoveryType_LossHpPerRecovery, nil
		case int(HeroSkillHpRecoveryType_DmgPerRecovery):
			return HeroSkillHpRecoveryType_DmgPerRecovery, nil
		}
	}
	return HeroSkillHpRecoveryType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for HeroSkillHpRecoveryType", enumStr)
}

func parseHeroSkillRangePolygon(enumStr string) (HeroSkillRangePolygon, error) {
	switch enumStr {
	case "矩形":
		return HeroSkillRangePolygon_Rectangle, nil
	case "圆形":
		return HeroSkillRangePolygon_Circular, nil
	case "扇形":
		return HeroSkillRangePolygon_Sector, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(HeroSkillRangePolygon_Rectangle):
			return HeroSkillRangePolygon_Rectangle, nil
		case int(HeroSkillRangePolygon_Circular):
			return HeroSkillRangePolygon_Circular, nil
		case int(HeroSkillRangePolygon_Sector):
			return HeroSkillRangePolygon_Sector, nil
		}
	}
	return HeroSkillRangePolygon(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for HeroSkillRangePolygon", enumStr)
}

func parseHeroSkillType(enumStr string) (HeroSkillType, error) {
	switch enumStr {
	case "主动":
		return HeroSkillType_HitSkill, nil
	case "被动":
		return HeroSkillType_NegativeSkill, nil
	case "天赋":
		return HeroSkillType_GiftSkill, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(HeroSkillType_HitSkill):
			return HeroSkillType_HitSkill, nil
		case int(HeroSkillType_NegativeSkill):
			return HeroSkillType_NegativeSkill, nil
		case int(HeroSkillType_GiftSkill):
			return HeroSkillType_GiftSkill, nil
		}
	}
	return HeroSkillType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for HeroSkillType", enumStr)
}

func parseHeroStarUpPlan(enumStr string) (HeroStarUpPlan, error) {
	switch enumStr {
	case "ssr升星方案":
		return HeroStarUpPlan_HeroStarPlanLegendary, nil
	case "sr升星方案":
		return HeroStarUpPlan_HeroStarPlanEpic, nil
	case "r升星方案":
		return HeroStarUpPlan_HeroStarPlanRare, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(HeroStarUpPlan_HeroStarPlanLegendary):
			return HeroStarUpPlan_HeroStarPlanLegendary, nil
		case int(HeroStarUpPlan_HeroStarPlanEpic):
			return HeroStarUpPlan_HeroStarPlanEpic, nil
		case int(HeroStarUpPlan_HeroStarPlanRare):
			return HeroStarUpPlan_HeroStarPlanRare, nil
		}
	}
	return HeroStarUpPlan(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for HeroStarUpPlan", enumStr)
}

func parseHeroType(enumStr string) (HeroType, error) {
	switch enumStr {
	case "魔法":
		return HeroType_Magic, nil
	case "异能":
		return HeroType_SuperPowers, nil
	case "科技":
		return HeroType_Tech, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(HeroType_Magic):
			return HeroType_Magic, nil
		case int(HeroType_SuperPowers):
			return HeroType_SuperPowers, nil
		case int(HeroType_Tech):
			return HeroType_Tech, nil
		}
	}
	return HeroType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for HeroType", enumStr)
}

func parseIapBoothType(enumStr string) (IapBoothType, error) {
	switch enumStr {
	case "钻石商店":
		return IapBoothType_DiamondShop, nil
	case "每日特惠":
		return IapBoothType_DailySale, nil
	case "常规礼包":
		return IapBoothType_RegularPack, nil
	case "常规BP":
		return IapBoothType_RegularBp, nil
	case "免广告特权":
		return IapBoothType_NoAds, nil
	case "加速特权":
		return IapBoothType_2X, nil
	case "月卡":
		return IapBoothType_MonthCard, nil
	case "成长基金":
		return IapBoothType_Fund, nil
	case "登录好礼":
		return IapBoothType_Sign, nil
	case "英雄转盘":
		return IapBoothType_TurnTable, nil
	case "7日签到":
		return IapBoothType_Sign7, nil
	case "终身卡":
		return IapBoothType_Life, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(IapBoothType_DiamondShop):
			return IapBoothType_DiamondShop, nil
		case int(IapBoothType_DailySale):
			return IapBoothType_DailySale, nil
		case int(IapBoothType_RegularPack):
			return IapBoothType_RegularPack, nil
		case int(IapBoothType_RegularBp):
			return IapBoothType_RegularBp, nil
		case int(IapBoothType_NoAds):
			return IapBoothType_NoAds, nil
		case int(IapBoothType_2X):
			return IapBoothType_2X, nil
		case int(IapBoothType_MonthCard):
			return IapBoothType_MonthCard, nil
		case int(IapBoothType_Fund):
			return IapBoothType_Fund, nil
		case int(IapBoothType_Sign):
			return IapBoothType_Sign, nil
		case int(IapBoothType_TurnTable):
			return IapBoothType_TurnTable, nil
		case int(IapBoothType_Sign7):
			return IapBoothType_Sign7, nil
		case int(IapBoothType_Life):
			return IapBoothType_Life, nil
		}
	}
	return IapBoothType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for IapBoothType", enumStr)
}

func parseIapPackageType(enumStr string) (IapPackageType, error) {
	switch enumStr {
	case "钻石商店":
		return IapPackageType_Diamond, nil
	case "首充":
		return IapPackageType_First, nil
	case "月卡":
		return IapPackageType_MonthCard, nil
	case "成长基金":
		return IapPackageType_Fund, nil
	case "常规礼包":
		return IapPackageType_Regular, nil
	case "每日特惠":
		return IapPackageType_DailySale, nil
	case "免广告卡":
		return IapPackageType_AdFree, nil
	case "终身卡":
		return IapPackageType_Life, nil
	case "登录好礼":
		return IapPackageType_SignBp, nil
	case "转盘活动":
		return IapPackageType_TurnActivity, nil
	case "触发礼包":
		return IapPackageType_Trigger, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(IapPackageType_Diamond):
			return IapPackageType_Diamond, nil
		case int(IapPackageType_First):
			return IapPackageType_First, nil
		case int(IapPackageType_MonthCard):
			return IapPackageType_MonthCard, nil
		case int(IapPackageType_Fund):
			return IapPackageType_Fund, nil
		case int(IapPackageType_Regular):
			return IapPackageType_Regular, nil
		case int(IapPackageType_DailySale):
			return IapPackageType_DailySale, nil
		case int(IapPackageType_AdFree):
			return IapPackageType_AdFree, nil
		case int(IapPackageType_Life):
			return IapPackageType_Life, nil
		case int(IapPackageType_SignBp):
			return IapPackageType_SignBp, nil
		case int(IapPackageType_TurnActivity):
			return IapPackageType_TurnActivity, nil
		case int(IapPackageType_Trigger):
			return IapPackageType_Trigger, nil
		}
	}
	return IapPackageType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for IapPackageType", enumStr)
}

func parseItemType(enumStr string) (ItemType, error) {
	switch enumStr {
	case "宝箱":
		return ItemType_Chest, nil
	case "自选箱":
		return ItemType_ChestSelfSelect, nil
	case "钻石":
		return ItemType_Diamond, nil
	case "甜甜圈":
		return ItemType_Doughnut, nil
	case "阳光":
		return ItemType_SunShine, nil
	case "招募卡":
		return ItemType_SummonCard, nil
	case "普通种子袋":
		return ItemType_SeedBagCommon, nil
	case "稀有种子袋":
		return ItemType_SeedBagRare, nil
	case "史诗种子袋":
		return ItemType_SeedBagEpic, nil
	case "传说种子袋":
		return ItemType_SeedBagLegendary, nil
	case "神话种子袋":
		return ItemType_SeedBagMyth, nil
	case "传说技能书":
		return ItemType_LegendarySkillBook, nil
	case "史诗技能书":
		return ItemType_EpicSkillBook, nil
	case "稀有技能书":
		return ItemType_RareSkillBook, nil
	case "通用传说英雄碎片":
		return ItemType_UniversalLegendaryHeroFragment, nil
	case "通用史诗英雄碎片":
		return ItemType_UniversalEpicHeroFragment, nil
	case "通用稀有英雄碎片":
		return ItemType_UniversalRareHeroFragment, nil
	case "随机传说英雄碎片":
		return ItemType_RandomLegendaryHeroFragment, nil
	case "随机史诗英雄碎片":
		return ItemType_RandomEpicHeroFragment, nil
	case "随机稀有英雄碎片":
		return ItemType_RandomRareHeroFragment, nil
	case "英雄":
		return ItemType_Hero, nil
	case "英雄碎片":
		return ItemType_HeroFragment, nil
	case "头像":
		return ItemType_Avatar, nil
	case "头像框":
		return ItemType_AvatarFrame, nil
	case "肉鸽经验":
		return ItemType_RougeExp, nil
	case "技能书":
		return ItemType_SkillBook, nil
	case "体力":
		return ItemType_Energy, nil
	case "英雄基因碎片":
		return ItemType_HeroGeneFragment, nil
	case "金币":
		return ItemType_Coin, nil
	case "英雄通用基因碎片":
		return ItemType_HeroGeneralGeneFragment, nil
	case "随机宝石":
		return ItemType_GemRandom, nil
	case "宝石":
		return ItemType_Gem, nil
	case "宝石洗练药剂":
		return ItemType_GemReforge, nil
	case "装备图纸":
		return ItemType_LordEquipManual, nil
	case "随机装备图纸":
		return ItemType_LordEquipRandomManual, nil
	case "宝箱钥匙":
		return ItemType_GemDraw, nil
	case "公会经验":
		return ItemType_GuildExp, nil
	case "公会币":
		return ItemType_GuildCoin, nil
	case "竞技币":
		return ItemType_ArenaCoin, nil
	case "爬塔钥匙":
		return ItemType_TowerKey, nil
	case "金币副本钥匙":
		return ItemType_CoinDungeonKey, nil
	case "基因副本钥匙":
		return ItemType_GeneDungeonKey, nil
	case "领主装备副本钥匙":
		return ItemType_LordEquipDungenKey, nil
	case "装备进阶图纸":
		return ItemType_LordEquipGradeUpManual, nil
	case "常规BP钥匙1":
		return ItemType_RegularBpKey1, nil
	case "免广告特权":
		return ItemType_FreeAd, nil
	case "2倍速特权":
		return ItemType_2X, nil
	case "签到钥匙1":
		return ItemType_SignKey1, nil
	case "基金钥匙1":
		return ItemType_FundKey1, nil
	case "月卡1":
		return ItemType_MonthCard1, nil
	case "月卡2":
		return ItemType_MonthCard2, nil
	case "转盘币":
		return ItemType_Turntablecoin, nil
	case "装备材料":
		return ItemType_LordEquipMaterial, nil
	case "英雄升品材料":
		return ItemType_HeroQualityUp, nil
	case "7日任务积分":
		return ItemType_SevenDayTasksScore, nil
	case "英雄通用升星材料":
		return ItemType_StarUpCommonItem, nil
	case "阳光副本钥匙":
		return ItemType_SunShineDungenKey, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(ItemType_Chest):
			return ItemType_Chest, nil
		case int(ItemType_ChestSelfSelect):
			return ItemType_ChestSelfSelect, nil
		case int(ItemType_Diamond):
			return ItemType_Diamond, nil
		case int(ItemType_Doughnut):
			return ItemType_Doughnut, nil
		case int(ItemType_SunShine):
			return ItemType_SunShine, nil
		case int(ItemType_SummonCard):
			return ItemType_SummonCard, nil
		case int(ItemType_SeedBagCommon):
			return ItemType_SeedBagCommon, nil
		case int(ItemType_SeedBagRare):
			return ItemType_SeedBagRare, nil
		case int(ItemType_SeedBagEpic):
			return ItemType_SeedBagEpic, nil
		case int(ItemType_SeedBagLegendary):
			return ItemType_SeedBagLegendary, nil
		case int(ItemType_SeedBagMyth):
			return ItemType_SeedBagMyth, nil
		case int(ItemType_LegendarySkillBook):
			return ItemType_LegendarySkillBook, nil
		case int(ItemType_EpicSkillBook):
			return ItemType_EpicSkillBook, nil
		case int(ItemType_RareSkillBook):
			return ItemType_RareSkillBook, nil
		case int(ItemType_UniversalLegendaryHeroFragment):
			return ItemType_UniversalLegendaryHeroFragment, nil
		case int(ItemType_UniversalEpicHeroFragment):
			return ItemType_UniversalEpicHeroFragment, nil
		case int(ItemType_UniversalRareHeroFragment):
			return ItemType_UniversalRareHeroFragment, nil
		case int(ItemType_RandomLegendaryHeroFragment):
			return ItemType_RandomLegendaryHeroFragment, nil
		case int(ItemType_RandomEpicHeroFragment):
			return ItemType_RandomEpicHeroFragment, nil
		case int(ItemType_RandomRareHeroFragment):
			return ItemType_RandomRareHeroFragment, nil
		case int(ItemType_Hero):
			return ItemType_Hero, nil
		case int(ItemType_HeroFragment):
			return ItemType_HeroFragment, nil
		case int(ItemType_Avatar):
			return ItemType_Avatar, nil
		case int(ItemType_AvatarFrame):
			return ItemType_AvatarFrame, nil
		case int(ItemType_RougeExp):
			return ItemType_RougeExp, nil
		case int(ItemType_SkillBook):
			return ItemType_SkillBook, nil
		case int(ItemType_Energy):
			return ItemType_Energy, nil
		case int(ItemType_HeroGeneFragment):
			return ItemType_HeroGeneFragment, nil
		case int(ItemType_Coin):
			return ItemType_Coin, nil
		case int(ItemType_HeroGeneralGeneFragment):
			return ItemType_HeroGeneralGeneFragment, nil
		case int(ItemType_GemRandom):
			return ItemType_GemRandom, nil
		case int(ItemType_Gem):
			return ItemType_Gem, nil
		case int(ItemType_GemReforge):
			return ItemType_GemReforge, nil
		case int(ItemType_LordEquipManual):
			return ItemType_LordEquipManual, nil
		case int(ItemType_LordEquipRandomManual):
			return ItemType_LordEquipRandomManual, nil
		case int(ItemType_GemDraw):
			return ItemType_GemDraw, nil
		case int(ItemType_GuildExp):
			return ItemType_GuildExp, nil
		case int(ItemType_GuildCoin):
			return ItemType_GuildCoin, nil
		case int(ItemType_ArenaCoin):
			return ItemType_ArenaCoin, nil
		case int(ItemType_TowerKey):
			return ItemType_TowerKey, nil
		case int(ItemType_CoinDungeonKey):
			return ItemType_CoinDungeonKey, nil
		case int(ItemType_GeneDungeonKey):
			return ItemType_GeneDungeonKey, nil
		case int(ItemType_LordEquipDungenKey):
			return ItemType_LordEquipDungenKey, nil
		case int(ItemType_LordEquipGradeUpManual):
			return ItemType_LordEquipGradeUpManual, nil
		case int(ItemType_RegularBpKey1):
			return ItemType_RegularBpKey1, nil
		case int(ItemType_FreeAd):
			return ItemType_FreeAd, nil
		case int(ItemType_2X):
			return ItemType_2X, nil
		case int(ItemType_SignKey1):
			return ItemType_SignKey1, nil
		case int(ItemType_FundKey1):
			return ItemType_FundKey1, nil
		case int(ItemType_MonthCard1):
			return ItemType_MonthCard1, nil
		case int(ItemType_MonthCard2):
			return ItemType_MonthCard2, nil
		case int(ItemType_Turntablecoin):
			return ItemType_Turntablecoin, nil
		case int(ItemType_LordEquipMaterial):
			return ItemType_LordEquipMaterial, nil
		case int(ItemType_HeroQualityUp):
			return ItemType_HeroQualityUp, nil
		case int(ItemType_SevenDayTasksScore):
			return ItemType_SevenDayTasksScore, nil
		case int(ItemType_StarUpCommonItem):
			return ItemType_StarUpCommonItem, nil
		case int(ItemType_SunShineDungenKey):
			return ItemType_SunShineDungenKey, nil
		}
	}
	return ItemType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for ItemType", enumStr)
}

func parseLaserTarget(enumStr string) (LaserTarget, error) {
	switch enumStr {
	case "自动攻击目标":
		return LaserTarget_Opposite, nil
	case "对方防御型英雄":
		return LaserTarget_OppositeSideHeroDefense, nil
	case "对方远程型英雄":
		return LaserTarget_OppositeSideHeroRanged, nil
	case "对方功能型英雄":
		return LaserTarget_OppositeSideHeroSupport, nil
	case "对方前排英雄":
		return LaserTarget_OppoSideHeroFront, nil
	case "对方后排英雄":
		return LaserTarget_OppoSideHeroBehind, nil
	case "对方生命值最低英雄（同值随机）":
		return LaserTarget_OppositeSideHeroHpLowest, nil
	case "前一技能段影响目标":
		return LaserTarget_PreSkillEffectTarget, nil
	case "前一技能段影响目标（除boss）":
		return LaserTarget_PreSkillEffectTargetElseBoss, nil
	case "boss":
		return LaserTarget_Boss, nil
	case "自己前方":
		return LaserTarget_OwnForward, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(LaserTarget_Opposite):
			return LaserTarget_Opposite, nil
		case int(LaserTarget_OppositeSideHeroDefense):
			return LaserTarget_OppositeSideHeroDefense, nil
		case int(LaserTarget_OppositeSideHeroRanged):
			return LaserTarget_OppositeSideHeroRanged, nil
		case int(LaserTarget_OppositeSideHeroSupport):
			return LaserTarget_OppositeSideHeroSupport, nil
		case int(LaserTarget_OppoSideHeroFront):
			return LaserTarget_OppoSideHeroFront, nil
		case int(LaserTarget_OppoSideHeroBehind):
			return LaserTarget_OppoSideHeroBehind, nil
		case int(LaserTarget_OppositeSideHeroHpLowest):
			return LaserTarget_OppositeSideHeroHpLowest, nil
		case int(LaserTarget_PreSkillEffectTarget):
			return LaserTarget_PreSkillEffectTarget, nil
		case int(LaserTarget_PreSkillEffectTargetElseBoss):
			return LaserTarget_PreSkillEffectTargetElseBoss, nil
		case int(LaserTarget_Boss):
			return LaserTarget_Boss, nil
		case int(LaserTarget_OwnForward):
			return LaserTarget_OwnForward, nil
		}
	}
	return LaserTarget(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for LaserTarget", enumStr)
}

func parseLevelType(enumStr string) (LevelType, error) {
	switch enumStr {
	case "塔防":
		return LevelType_TowerDefense, nil
	case "跑酷":
		return LevelType_ParkOur, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(LevelType_TowerDefense):
			return LevelType_TowerDefense, nil
		case int(LevelType_ParkOur):
			return LevelType_ParkOur, nil
		}
	}
	return LevelType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for LevelType", enumStr)
}

func parseLogicType(enumStr string) (LogicType, error) {
	switch enumStr {
	case "数字":
		return LogicType_And, nil
	case "模型":
		return LogicType_Or, nil
	case "Buff":
		return LogicType_Invert, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(LogicType_And):
			return LogicType_And, nil
		case int(LogicType_Or):
			return LogicType_Or, nil
		case int(LogicType_Invert):
			return LogicType_Invert, nil
		}
	}
	return LogicType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for LogicType", enumStr)
}

func parseLordEquipGradeType(enumStr string) (LordEquipGradeType, error) {
	switch enumStr {
	case "破损":
		return LordEquipGradeType_LordEquipGrade1, nil
	case "普通":
		return LordEquipGradeType_LordEquipGrade2, nil
	case "优秀":
		return LordEquipGradeType_LordEquipGrade3, nil
	case "史诗":
		return LordEquipGradeType_LordEquipGrade4, nil
	case "传说":
		return LordEquipGradeType_LordEquipGrade5, nil
	case "神话":
		return LordEquipGradeType_LordEquipGrade6, nil
	case "至尊":
		return LordEquipGradeType_LordEquipGrade7, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(LordEquipGradeType_LordEquipGrade1):
			return LordEquipGradeType_LordEquipGrade1, nil
		case int(LordEquipGradeType_LordEquipGrade2):
			return LordEquipGradeType_LordEquipGrade2, nil
		case int(LordEquipGradeType_LordEquipGrade3):
			return LordEquipGradeType_LordEquipGrade3, nil
		case int(LordEquipGradeType_LordEquipGrade4):
			return LordEquipGradeType_LordEquipGrade4, nil
		case int(LordEquipGradeType_LordEquipGrade5):
			return LordEquipGradeType_LordEquipGrade5, nil
		case int(LordEquipGradeType_LordEquipGrade6):
			return LordEquipGradeType_LordEquipGrade6, nil
		case int(LordEquipGradeType_LordEquipGrade7):
			return LordEquipGradeType_LordEquipGrade7, nil
		}
	}
	return LordEquipGradeType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for LordEquipGradeType", enumStr)
}

func parseLordEquipType(enumStr string) (LordEquipType, error) {
	switch enumStr {
	case "头盔":
		return LordEquipType_LordEquipType1, nil
	case "护臂":
		return LordEquipType_LordEquipType2, nil
	case "衣服":
		return LordEquipType_LordEquipType3, nil
	case "裤子":
		return LordEquipType_LordEquipType4, nil
	case "足具":
		return LordEquipType_LordEquipType5, nil
	case "手套":
		return LordEquipType_LordEquipType6, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(LordEquipType_LordEquipType1):
			return LordEquipType_LordEquipType1, nil
		case int(LordEquipType_LordEquipType2):
			return LordEquipType_LordEquipType2, nil
		case int(LordEquipType_LordEquipType3):
			return LordEquipType_LordEquipType3, nil
		case int(LordEquipType_LordEquipType4):
			return LordEquipType_LordEquipType4, nil
		case int(LordEquipType_LordEquipType5):
			return LordEquipType_LordEquipType5, nil
		case int(LordEquipType_LordEquipType6):
			return LordEquipType_LordEquipType6, nil
		}
	}
	return LordEquipType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for LordEquipType", enumStr)
}

func parseMapEventType(enumStr string) (MapEventType, error) {
	switch enumStr {
	case "怪物":
		return MapEventType_Monster, nil
	case "道具":
		return MapEventType_Prop, nil
	case "BUff":
		return MapEventType_Buff, nil
	case "障碍":
		return MapEventType_Obstacle, nil
	case "奖励":
		return MapEventType_Reward, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(MapEventType_Monster):
			return MapEventType_Monster, nil
		case int(MapEventType_Prop):
			return MapEventType_Prop, nil
		case int(MapEventType_Buff):
			return MapEventType_Buff, nil
		case int(MapEventType_Obstacle):
			return MapEventType_Obstacle, nil
		case int(MapEventType_Reward):
			return MapEventType_Reward, nil
		}
	}
	return MapEventType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for MapEventType", enumStr)
}

func parseMissileTarget(enumStr string) (MissileTarget, error) {
	switch enumStr {
	case "自动攻击目标":
		return MissileTarget_Opposite, nil
	case "对方防御型英雄":
		return MissileTarget_OppositeSideHeroDefense, nil
	case "对方远程型英雄":
		return MissileTarget_OppositeSideHeroRanged, nil
	case "对方功能型英雄":
		return MissileTarget_OppositeSideHeroSupport, nil
	case "对方前排英雄":
		return MissileTarget_OppoSideHeroFront, nil
	case "对方后排英雄":
		return MissileTarget_OppoSideHeroBehind, nil
	case "对方生命值最低英雄（同值随机）":
		return MissileTarget_OppositeSideHeroHpLowest, nil
	case "前一技能段影响目标":
		return MissileTarget_PreSkillEffectTarget, nil
	case "前一技能段影响目标（除boss）":
		return MissileTarget_PreSkillEffectTargetElseBoss, nil
	case "boss":
		return MissileTarget_Boss, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(MissileTarget_Opposite):
			return MissileTarget_Opposite, nil
		case int(MissileTarget_OppositeSideHeroDefense):
			return MissileTarget_OppositeSideHeroDefense, nil
		case int(MissileTarget_OppositeSideHeroRanged):
			return MissileTarget_OppositeSideHeroRanged, nil
		case int(MissileTarget_OppositeSideHeroSupport):
			return MissileTarget_OppositeSideHeroSupport, nil
		case int(MissileTarget_OppoSideHeroFront):
			return MissileTarget_OppoSideHeroFront, nil
		case int(MissileTarget_OppoSideHeroBehind):
			return MissileTarget_OppoSideHeroBehind, nil
		case int(MissileTarget_OppositeSideHeroHpLowest):
			return MissileTarget_OppositeSideHeroHpLowest, nil
		case int(MissileTarget_PreSkillEffectTarget):
			return MissileTarget_PreSkillEffectTarget, nil
		case int(MissileTarget_PreSkillEffectTargetElseBoss):
			return MissileTarget_PreSkillEffectTargetElseBoss, nil
		case int(MissileTarget_Boss):
			return MissileTarget_Boss, nil
		}
	}
	return MissileTarget(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for MissileTarget", enumStr)
}

func parseMonsterCareerType(enumStr string) (MonsterCareerType, error) {
	switch enumStr {
	case "近战":
		return MonsterCareerType_Melee, nil
	case "射手":
		return MonsterCareerType_Ranger, nil
	case "坦克":
		return MonsterCareerType_Tank, nil
	case "狂奔":
		return MonsterCareerType_Assassin, nil
	case "浮空近战":
		return MonsterCareerType_AirMelee, nil
	case "浮空射手":
		return MonsterCareerType_AirRanger, nil
	case "自爆":
		return MonsterCareerType_Suicide, nil
	case "远射":
		return MonsterCareerType_LongRanger, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(MonsterCareerType_Melee):
			return MonsterCareerType_Melee, nil
		case int(MonsterCareerType_Ranger):
			return MonsterCareerType_Ranger, nil
		case int(MonsterCareerType_Tank):
			return MonsterCareerType_Tank, nil
		case int(MonsterCareerType_Assassin):
			return MonsterCareerType_Assassin, nil
		case int(MonsterCareerType_AirMelee):
			return MonsterCareerType_AirMelee, nil
		case int(MonsterCareerType_AirRanger):
			return MonsterCareerType_AirRanger, nil
		case int(MonsterCareerType_Suicide):
			return MonsterCareerType_Suicide, nil
		case int(MonsterCareerType_LongRanger):
			return MonsterCareerType_LongRanger, nil
		}
	}
	return MonsterCareerType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for MonsterCareerType", enumStr)
}

func parseMonsterGrade(enumStr string) (MonsterGrade, error) {
	switch enumStr {
	case "小怪":
		return MonsterGrade_Common, nil
	case "精英":
		return MonsterGrade_Elite, nil
	case "首领":
		return MonsterGrade_Boss, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(MonsterGrade_Common):
			return MonsterGrade_Common, nil
		case int(MonsterGrade_Elite):
			return MonsterGrade_Elite, nil
		case int(MonsterGrade_Boss):
			return MonsterGrade_Boss, nil
		}
	}
	return MonsterGrade(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for MonsterGrade", enumStr)
}

func parseMonsterPosType(enumStr string) (MonsterPosType, error) {
	switch enumStr {
	case "地面":
		return MonsterPosType_Ground, nil
	case "空中":
		return MonsterPosType_Air, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(MonsterPosType_Ground):
			return MonsterPosType_Ground, nil
		case int(MonsterPosType_Air):
			return MonsterPosType_Air, nil
		}
	}
	return MonsterPosType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for MonsterPosType", enumStr)
}

func parseMonsterRefreshType(enumStr string) (MonsterRefreshType, error) {
	switch enumStr {
	case "开局刷新":
		return MonsterRefreshType_InitialRefresh, nil
	case "延时刷新":
		return MonsterRefreshType_DelayRefresh, nil
	case "上次怪死亡x只后刷新":
		return MonsterRefreshType_UpstreamDeathCntRefresh, nil
	case "玩家路过地块x秒后刷新":
		return MonsterRefreshType_PassPlotsRefresh, nil
	case "上波刷完x秒后刷新下一波":
		return MonsterRefreshType_AfterRefreshing, nil
	case "游戏开始x秒后刷新":
		return MonsterRefreshType_AfterTheGameStarts, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(MonsterRefreshType_InitialRefresh):
			return MonsterRefreshType_InitialRefresh, nil
		case int(MonsterRefreshType_DelayRefresh):
			return MonsterRefreshType_DelayRefresh, nil
		case int(MonsterRefreshType_UpstreamDeathCntRefresh):
			return MonsterRefreshType_UpstreamDeathCntRefresh, nil
		case int(MonsterRefreshType_PassPlotsRefresh):
			return MonsterRefreshType_PassPlotsRefresh, nil
		case int(MonsterRefreshType_AfterRefreshing):
			return MonsterRefreshType_AfterRefreshing, nil
		case int(MonsterRefreshType_AfterTheGameStarts):
			return MonsterRefreshType_AfterTheGameStarts, nil
		}
	}
	return MonsterRefreshType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for MonsterRefreshType", enumStr)
}

func parsePurchaseLimitType(enumStr string) (PurchaseLimitType, error) {
	switch enumStr {
	case "每日限购":
		return PurchaseLimitType_DailyLimit, nil
	case "每周限购":
		return PurchaseLimitType_WeeklyLimit, nil
	case "每月限购":
		return PurchaseLimitType_MonthlyLimit, nil
	case "终身限购":
		return PurchaseLimitType_LifeLimit, nil
	case "无限制":
		return PurchaseLimitType_UnLimit, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(PurchaseLimitType_DailyLimit):
			return PurchaseLimitType_DailyLimit, nil
		case int(PurchaseLimitType_WeeklyLimit):
			return PurchaseLimitType_WeeklyLimit, nil
		case int(PurchaseLimitType_MonthlyLimit):
			return PurchaseLimitType_MonthlyLimit, nil
		case int(PurchaseLimitType_LifeLimit):
			return PurchaseLimitType_LifeLimit, nil
		case int(PurchaseLimitType_UnLimit):
			return PurchaseLimitType_UnLimit, nil
		}
	}
	return PurchaseLimitType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for PurchaseLimitType", enumStr)
}

func parseRougeTabType(enumStr string) (RougeTabType, error) {
	switch enumStr {
	case "效果卡-对应效果":
		return RougeTabType_EffectTab, nil
	case "解锁卡-将指定卡放入卡池":
		return RougeTabType_UnlockTab, nil
	case "上阵卡":
		return RougeTabType_ConfigTab, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(RougeTabType_EffectTab):
			return RougeTabType_EffectTab, nil
		case int(RougeTabType_UnlockTab):
			return RougeTabType_UnlockTab, nil
		case int(RougeTabType_ConfigTab):
			return RougeTabType_ConfigTab, nil
		}
	}
	return RougeTabType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for RougeTabType", enumStr)
}

func parseShopType(enumStr string) (ShopType, error) {
	switch enumStr {
	case "公会商店":
		return ShopType_GuildShop, nil
	case "竞技商店":
		return ShopType_ArenaShop, nil
	case "常规商店":
		return ShopType_BlackShop, nil
	case "关卡商店":
		return ShopType_LevelShop, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(ShopType_GuildShop):
			return ShopType_GuildShop, nil
		case int(ShopType_ArenaShop):
			return ShopType_ArenaShop, nil
		case int(ShopType_BlackShop):
			return ShopType_BlackShop, nil
		case int(ShopType_LevelShop):
			return ShopType_LevelShop, nil
		}
	}
	return ShopType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for ShopType", enumStr)
}

func parseSkillAttrOverlyingType(enumStr string) (SkillAttrOverlyingType, error) {
	switch enumStr {
	case "加法叠加":
		return SkillAttrOverlyingType_AddOverlying, nil
	case "乘法叠加":
		return SkillAttrOverlyingType_MulOverlying, nil
	case "枚举叠加":
		return SkillAttrOverlyingType_EnumOverlying, nil
	case "不可叠加":
		return SkillAttrOverlyingType_NoOverlying, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(SkillAttrOverlyingType_AddOverlying):
			return SkillAttrOverlyingType_AddOverlying, nil
		case int(SkillAttrOverlyingType_MulOverlying):
			return SkillAttrOverlyingType_MulOverlying, nil
		case int(SkillAttrOverlyingType_EnumOverlying):
			return SkillAttrOverlyingType_EnumOverlying, nil
		case int(SkillAttrOverlyingType_NoOverlying):
			return SkillAttrOverlyingType_NoOverlying, nil
		}
	}
	return SkillAttrOverlyingType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for SkillAttrOverlyingType", enumStr)
}

func parseSkillDmgType(enumStr string) (SkillDmgType, error) {
	switch enumStr {
	case "电系":
		return SkillDmgType_Electrical, nil
	case "风系":
		return SkillDmgType_Wind, nil
	case "光系":
		return SkillDmgType_Light, nil
	case "火系":
		return SkillDmgType_Fire, nil
	case "冰系":
		return SkillDmgType_Ice, nil
	case "物理系":
		return SkillDmgType_Physical, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(SkillDmgType_Electrical):
			return SkillDmgType_Electrical, nil
		case int(SkillDmgType_Wind):
			return SkillDmgType_Wind, nil
		case int(SkillDmgType_Light):
			return SkillDmgType_Light, nil
		case int(SkillDmgType_Fire):
			return SkillDmgType_Fire, nil
		case int(SkillDmgType_Ice):
			return SkillDmgType_Ice, nil
		case int(SkillDmgType_Physical):
			return SkillDmgType_Physical, nil
		}
	}
	return SkillDmgType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for SkillDmgType", enumStr)
}

func parseSkillType(enumStr string) (SkillType, error) {
	switch enumStr {
	case "主动技能":
		return SkillType_ActiveSkill, nil
	case "被动技能":
		return SkillType_PassiveSkill, nil
	case "光环技能":
		return SkillType_AuraSkill, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(SkillType_ActiveSkill):
			return SkillType_ActiveSkill, nil
		case int(SkillType_PassiveSkill):
			return SkillType_PassiveSkill, nil
		case int(SkillType_AuraSkill):
			return SkillType_AuraSkill, nil
		}
	}
	return SkillType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for SkillType", enumStr)
}

func parseTaskCounterType(enumStr string) (TaskCounterType, error) {
	switch enumStr {
	case "重置":
		return TaskCounterType_Reset, nil
	case "累计":
		return TaskCounterType_Total, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(TaskCounterType_Reset):
			return TaskCounterType_Reset, nil
		case int(TaskCounterType_Total):
			return TaskCounterType_Total, nil
		}
	}
	return TaskCounterType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for TaskCounterType", enumStr)
}

func parseTaskType(enumStr string) (TaskType, error) {
	switch enumStr {
	case "每日登录":
		return TaskType_Login, nil
	case "累计登录":
		return TaskType_TotalLogin, nil
	case "关卡挑战":
		return TaskType_LevelBegin, nil
	case "通关":
		return TaskType_LevelPass, nil
	case "通过指定关卡":
		return TaskType_LevelPassTo, nil
	case "消耗道具":
		return TaskType_ItemBurn, nil
	case "累计消耗道具":
		return TaskType_TotalItemBurn, nil
	case "英雄升级":
		return TaskType_HeroLevelUp, nil
	case "英雄升至等级":
		return TaskType_HeroLevelUpTo, nil
	case "英雄升星":
		return TaskType_HeroStarUp, nil
	case "英雄升至星级":
		return TaskType_HeroStarUpTo, nil
	case "英雄技能升级":
		return TaskType_HeroSkillUp, nil
	case "英雄技能升至等级":
		return TaskType_HeroSkillUpTo, nil
	case "英雄天赋升级":
		return TaskType_HeroGeneUp, nil
	case "英雄天赋升至等级":
		return TaskType_HeroGeneUpTo, nil
	case "击杀怪物":
		return TaskType_KillMonster, nil
	case "累计击杀怪物":
		return TaskType_TotalKillMonster, nil
	case "领取挂机奖励":
		return TaskType_Claim_idle_reward, nil
	case "领取通关奖励":
		return TaskType_Claim_pass_level_reward, nil
	case "聊天":
		return TaskType_Chat, nil
	case "好友":
		return TaskType_Nigger, nil
	case "改名":
		return TaskType_Rename, nil
	case "换头像":
		return TaskType_Avatar, nil
	case "加入公会":
		return TaskType_JoinGuild, nil
	case "挂机扫荡":
		return TaskType_Sweep, nil
	case "英雄招募":
		return TaskType_HeroSummon, nil
	case "英雄上阵":
		return TaskType_HeroConfig, nil
	case "培养仓强化次数":
		return TaskType_LordEquipLvlUp, nil
	case "培养仓强化到":
		return TaskType_LordEquipLvlUpTo, nil
	case "突变基因合成":
		return TaskType_GemCraft, nil
	case "提炼基因":
		return TaskType_GemSummon, nil
	case "商店购物任务":
		return TaskType_Shopping, nil
	case "副本挑战任务":
		return TaskType_DungeonChallenge, nil
	case "副本扫荡任务":
		return TaskType_DungeonSweep, nil
	case "竞技场挑战任务":
		return TaskType_ArenaChallenge, nil
	case "累计激活英雄":
		return TaskType_TotalActivateHero, nil
	case "拥有指定品质英雄":
		return TaskType_TotalQualityHero, nil
	case "主线总星":
		return TaskType_TotalMainStar, nil
	case "领取每日宝箱":
		return TaskType_DailyScore, nil
	case "领取每周宝箱":
		return TaskType_WeeklyScore, nil
	case "资源本难度":
		return TaskType_DungeonLevel, nil
	case "突变基因品质":
		return TaskType_GemQuality, nil
	case "培养仓升品次数":
		return TaskType_LordEquipGrade, nil
	case "培养仓升品到":
		return TaskType_LordEquipGradeTo, nil
	case "光伏领体力":
		return TaskType_EnergyFactory, nil
	case "累计英雄升级":
		return TaskType_TotalHeroLevelUp, nil
	case "累计英雄升星":
		return TaskType_TotalHeroStarUp, nil
	case "累计技能升级":
		return TaskType_TotalHeroSkillUp, nil
	case "累计天赋升级":
		return TaskType_TotalHeroGeneUp, nil
	case "累计英雄招募":
		return TaskType_TotalHeroSummon, nil
	case "累计提炼基因":
		return TaskType_TotalGemSummon, nil
	case "累计突变基因合成":
		return TaskType_TotalGemCraft, nil
	case "累计培养仓强化":
		return TaskType_TotalLordEquipLvlUp, nil
	case "累计培养仓升品":
		return TaskType_TotalLordEquipGrade, nil
	case "完成子任务":
		return TaskType_CompleteTask, nil
	case "累计提炼普通基因":
		return TaskType_TotalGemSummon_1, nil
	case "累计提炼高级基因":
		return TaskType_TotalGemSummon_2, nil
	case "完成指定章节任务":
		return TaskType_ChapterTaskComplete, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(TaskType_Login):
			return TaskType_Login, nil
		case int(TaskType_TotalLogin):
			return TaskType_TotalLogin, nil
		case int(TaskType_LevelBegin):
			return TaskType_LevelBegin, nil
		case int(TaskType_LevelPass):
			return TaskType_LevelPass, nil
		case int(TaskType_LevelPassTo):
			return TaskType_LevelPassTo, nil
		case int(TaskType_ItemBurn):
			return TaskType_ItemBurn, nil
		case int(TaskType_TotalItemBurn):
			return TaskType_TotalItemBurn, nil
		case int(TaskType_HeroLevelUp):
			return TaskType_HeroLevelUp, nil
		case int(TaskType_HeroLevelUpTo):
			return TaskType_HeroLevelUpTo, nil
		case int(TaskType_HeroStarUp):
			return TaskType_HeroStarUp, nil
		case int(TaskType_HeroStarUpTo):
			return TaskType_HeroStarUpTo, nil
		case int(TaskType_HeroSkillUp):
			return TaskType_HeroSkillUp, nil
		case int(TaskType_HeroSkillUpTo):
			return TaskType_HeroSkillUpTo, nil
		case int(TaskType_HeroGeneUp):
			return TaskType_HeroGeneUp, nil
		case int(TaskType_HeroGeneUpTo):
			return TaskType_HeroGeneUpTo, nil
		case int(TaskType_KillMonster):
			return TaskType_KillMonster, nil
		case int(TaskType_TotalKillMonster):
			return TaskType_TotalKillMonster, nil
		case int(TaskType_Claim_idle_reward):
			return TaskType_Claim_idle_reward, nil
		case int(TaskType_Claim_pass_level_reward):
			return TaskType_Claim_pass_level_reward, nil
		case int(TaskType_Chat):
			return TaskType_Chat, nil
		case int(TaskType_Nigger):
			return TaskType_Nigger, nil
		case int(TaskType_Rename):
			return TaskType_Rename, nil
		case int(TaskType_Avatar):
			return TaskType_Avatar, nil
		case int(TaskType_JoinGuild):
			return TaskType_JoinGuild, nil
		case int(TaskType_Sweep):
			return TaskType_Sweep, nil
		case int(TaskType_HeroSummon):
			return TaskType_HeroSummon, nil
		case int(TaskType_HeroConfig):
			return TaskType_HeroConfig, nil
		case int(TaskType_LordEquipLvlUp):
			return TaskType_LordEquipLvlUp, nil
		case int(TaskType_LordEquipLvlUpTo):
			return TaskType_LordEquipLvlUpTo, nil
		case int(TaskType_GemCraft):
			return TaskType_GemCraft, nil
		case int(TaskType_GemSummon):
			return TaskType_GemSummon, nil
		case int(TaskType_Shopping):
			return TaskType_Shopping, nil
		case int(TaskType_DungeonChallenge):
			return TaskType_DungeonChallenge, nil
		case int(TaskType_DungeonSweep):
			return TaskType_DungeonSweep, nil
		case int(TaskType_ArenaChallenge):
			return TaskType_ArenaChallenge, nil
		case int(TaskType_TotalActivateHero):
			return TaskType_TotalActivateHero, nil
		case int(TaskType_TotalQualityHero):
			return TaskType_TotalQualityHero, nil
		case int(TaskType_TotalMainStar):
			return TaskType_TotalMainStar, nil
		case int(TaskType_DailyScore):
			return TaskType_DailyScore, nil
		case int(TaskType_WeeklyScore):
			return TaskType_WeeklyScore, nil
		case int(TaskType_DungeonLevel):
			return TaskType_DungeonLevel, nil
		case int(TaskType_GemQuality):
			return TaskType_GemQuality, nil
		case int(TaskType_LordEquipGrade):
			return TaskType_LordEquipGrade, nil
		case int(TaskType_LordEquipGradeTo):
			return TaskType_LordEquipGradeTo, nil
		case int(TaskType_EnergyFactory):
			return TaskType_EnergyFactory, nil
		case int(TaskType_TotalHeroLevelUp):
			return TaskType_TotalHeroLevelUp, nil
		case int(TaskType_TotalHeroStarUp):
			return TaskType_TotalHeroStarUp, nil
		case int(TaskType_TotalHeroSkillUp):
			return TaskType_TotalHeroSkillUp, nil
		case int(TaskType_TotalHeroGeneUp):
			return TaskType_TotalHeroGeneUp, nil
		case int(TaskType_TotalHeroSummon):
			return TaskType_TotalHeroSummon, nil
		case int(TaskType_TotalGemSummon):
			return TaskType_TotalGemSummon, nil
		case int(TaskType_TotalGemCraft):
			return TaskType_TotalGemCraft, nil
		case int(TaskType_TotalLordEquipLvlUp):
			return TaskType_TotalLordEquipLvlUp, nil
		case int(TaskType_TotalLordEquipGrade):
			return TaskType_TotalLordEquipGrade, nil
		case int(TaskType_CompleteTask):
			return TaskType_CompleteTask, nil
		case int(TaskType_TotalGemSummon_1):
			return TaskType_TotalGemSummon_1, nil
		case int(TaskType_TotalGemSummon_2):
			return TaskType_TotalGemSummon_2, nil
		case int(TaskType_ChapterTaskComplete):
			return TaskType_ChapterTaskComplete, nil
		}
	}
	return TaskType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for TaskType", enumStr)
}

func parseTriggerPackType(enumStr string) (TriggerPackType, error) {
	switch enumStr {
	case "主线通关纪念":
		return TriggerPackType_LevelPass, nil
	case "宝石抽取":
		return TriggerPackType_GemDraw, nil
	case "英雄招募":
		return TriggerPackType_HeroSummon, nil
	}
	v, err := strconv.Atoi(enumStr)
	if err == nil {
		switch v {
		case int(TriggerPackType_LevelPass):
			return TriggerPackType_LevelPass, nil
		case int(TriggerPackType_GemDraw):
			return TriggerPackType_GemDraw, nil
		case int(TriggerPackType_HeroSummon):
			return TriggerPackType_HeroSummon, nil
		}
	}
	return TriggerPackType(enumDefaultValue), fmt.Errorf("invalid enum str [%s] for TriggerPackType", enumStr)
}
