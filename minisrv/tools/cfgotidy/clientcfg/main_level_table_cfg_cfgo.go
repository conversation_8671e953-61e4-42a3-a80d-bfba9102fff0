// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"text/template"

	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var (
	_ json.Encoder
)

func (t MainLevelTable) saveJson(dir string) error {
	return t.SaveJsonWithSuffix(dir, "", t.Filter<PERSON>(func(v *MainLevelTableCfg) bool {
		return true
	}))
}

func (t MainLevelTable) SaveJsonWithSuffix(dir string, suffix string, cfgs []*MainLevelTableCfg) error {
	jsonPath := filepath.Join(dir, "MainLevelTable"+suffix+".json")
	f, err := os.OpenFile(jsonPath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[MainLevelTable]open json failed, path=%s, err=[%w]", jsonPath, err)
	}
	defer f.Close()
	// sort
	s := cfgoMainLevelTableSlice{}
	for _, cfgoCfg := range cfgs {
		if _, ok := t.localIds[cfgoCfg.Id]; !ok {
			continue
		}
		s = append(s, cfgoCfg)
	}
	sort.Sort(s)

	w := bufio.NewWriter(f)
	w.WriteString("{\n")
	w.WriteString("\t\"Cells\": ")
	bytes, err := json.MarshalIndent(s, "\t", "\t")
	if err != nil {
		return err
	}
	w.Write(bytes)
	w.WriteString("\n}\n")

	if err = w.Flush(); err != nil {
		return err
	}
	return nil
}

type cfgoMainLevelTableSlice []*MainLevelTableCfg

func (x cfgoMainLevelTableSlice) Len() int           { return len(x) }
func (x cfgoMainLevelTableSlice) Less(i, j int) bool { return x[i].Id < x[j].Id }
func (x cfgoMainLevelTableSlice) Swap(i, j int)      { x[i], x[j] = x[j], x[i] }

func (t *MainLevelTable) LoadJsonByPath(jsonPath string, configs *Configs) error {
	bytes, err := os.ReadFile(jsonPath)
	if err != nil {
		return fmt.Errorf("[MainLevelTable]read json failed, path=%s, err=[%w]", jsonPath, err)
	}
	records := make(map[int32]*MainLevelTableCfg)
	if err = json.Unmarshal(bytes, &records); err != nil {
		return fmt.Errorf("[MainLevelTable]unmarshal json failed, path=%s, err=[%w]", jsonPath, err)
	}
	for k, v := range records {
		if err = t.Put(k, v); err != nil {
			return fmt.Errorf("[MainLevelTable]put record failed, path=%s, err=[%w]", jsonPath, err)
		}
	}
	if err = t.setupIndexes(); err != nil {
		return fmt.Errorf("[MainLevelTable]setup indexes failed, path=%s, err=[%w]", jsonPath, err)
	}
	return nil
}

func (t MainLevelTable) saveMeta(dir string) error {
	return t.SaveMetaWithSuffix(dir, "")
}

func (t MainLevelTable) SaveMetaWithSuffix(dir string, suffix string) error {
	fbPlus := false
	td := &TableData{}
	if err := json.Unmarshal(string2Bytes(MainLevelTableJsonContent), td); err != nil {
		return fmt.Errorf("[client]unmarshal json failed, err=[%w]", err)
	}
	if len(suffix) > 0 {
		td.FileName = td.FileName + suffix
		td.TableName = td.TableName + suffix
	}
	path := filepath.Join(dir, td.FileName+".fbs")
	f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	defer f.Close()

	tplStr := FlatBufferTableTpl
	if fbPlus {
		tplStr = FlatBufferPlusTableTpl
	}
	tpl, err := template.New("table_schema").Parse(tplStr)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}

	if err = tpl.Execute(f, td); err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	return nil
}

var MainLevelTableJsonContent string = `{
		"FileName": "MainLevelTable",
		"ConstTable": false,
		"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
		"TableName": "MainLevelTable",
		"Key": {
			"FieldName": "Id",
			"FieldType": "int32"
		},
		"Fields": [
			{
				"FieldName": "StringId",
				"FieldType": "string"
			},
			{
				"FieldName": "IsMaxLevel",
				"FieldType": "bool"
			},
			{
				"FieldName": "IsRewardLevel",
				"FieldType": "bool"
			},
			{
				"FieldName": "ChapterLevel",
				"FieldType": "int32"
			},
			{
				"FieldName": "Chapter",
				"FieldType": "int32"
			},
			{
				"FieldName": "Level",
				"FieldType": "int32"
			},
			{
				"FieldName": "EliteMonsterAtkRatio",
				"FieldType": "float"
			},
			{
				"FieldName": "EliteMonsterDefRatio",
				"FieldType": "float"
			},
			{
				"FieldName": "EliteMonsterHpRatio",
				"FieldType": "float"
			},
			{
				"FieldName": "LevelScene",
				"FieldType": "string"
			},
			{
				"FieldName": "MapImage",
				"FieldType": "string"
			},
			{
				"FieldName": "LevelType",
				"FieldType": "LevelType = TowerDefense"
			},
			{
				"FieldName": "LevelPrefab",
				"FieldType": "string"
			},
			{
				"FieldName": "MonsterPreview",
				"FieldType": "[int32]"
			},
			{
				"FieldName": "MonsterPreviewPos",
				"FieldType": "[int32]"
			},
			{
				"FieldName": "MonsterPreviewScheme",
				"FieldType": "int32"
			},
			{
				"FieldName": "KillRewardRougeTabCntScheme",
				"FieldType": "int32"
			},
			{
				"FieldName": "CommonChallengeReward",
				"FieldType": "int32"
			},
			{
				"FieldName": "EliteChallengeReward",
				"FieldType": "int32"
			},
			{
				"FieldName": "OneStarReward",
				"FieldType": "int32"
			},
			{
				"FieldName": "TwoStarReward",
				"FieldType": "int32"
			},
			{
				"FieldName": "ThreeStarReward",
				"FieldType": "int32"
			},
			{
				"FieldName": "ExtraReward",
				"FieldType": "[RewardKVS]"
			},
			{
				"FieldName": "Name",
				"FieldType": "string"
			}
		]
	}`
