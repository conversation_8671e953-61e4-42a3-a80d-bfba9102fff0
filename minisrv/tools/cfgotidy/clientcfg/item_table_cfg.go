// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type ItemTableCfg struct {
	Id                  int32                     `json:"Id"`                  // Id
	StringId            string                    `json:"StringId"`            // StringId
	Priority            int32                     `json:"Priority"`            // 展示优先级（小在前）
	Type                ItemType                  `json:"Type"`                // 类型
	Value               int32                     `json:"Value"`               // 值
	Benefits            []int32                   `json:"Benefits"`            // 对应增益
	BenefitsRef         []*BenefitsTableCfg       `json:"-"`                   // 对应增益
	BenefitsValue       []float32                 `json:"BenefitsValue"`       // 增益值
	Image               string                    `json:"Image"`               // 图片
	Name                string                    `json:"Name"`                // 名字
	Desc                string                    `json:"Desc"`                // 描述
	Param               []string                  `json:"Param"`               // 参数
	Quality             int32                     `json:"Quality"`             // 道具品质
	QualityRef          *ItemQualityTableCfg      `json:"-"`                   // 道具品质
	AutoUse             bool                      `json:"AutoUse"`             // 获得自动使用
	RepeatAutoTransform bool                      `json:"RepeatAutoTransform"` // 重复自动转化
	ChestDropGroup      int32                     `json:"ChestDropGroup"`      // 随机箱对应的掉落组
	ChestDropGroupRef   *DropGroupTableCfg        `json:"-"`                   // 随机箱对应的掉落组
	ChestSelect         int32                     `json:"ChestSelect"`         // 自选箱
	ChestSelectRef      *SelectChestGroupTableCfg `json:"-"`                   // 自选箱
	MaxUseCnt           int32                     `json:"MaxUseCnt"`           // 单次最大使用数量
	IsDiamond           bool                      `json:"IsDiamond"`           // 是否直接跳转商城
	DiamondExchange     bool                      `json:"DiamondExchange"`     // 是否可以用钻石兑换
	DiamondCnt          int32                     `json:"DiamondCnt"`          // 钻石数量
	ShowSource          bool                      `json:"ShowSource"`          // 是否显示道具获取来源
	ItemSource          []int32                   `json:"ItemSource"`          // 道具来源
	ItemSourceRef       []*ItemSourceTableCfg     `json:"-"`                   // 道具来源
	InBag               bool                      `json:"InBag"`               // 是否进背包
	BagType             BagType                   `json:"BagType"`             // 背包类型
}

func NewItemTableCfg() *ItemTableCfg {
	return &ItemTableCfg{
		Id:                  0,
		StringId:            "",
		Priority:            0,
		Type:                ItemType(enumDefaultValue),
		Value:               0,
		Benefits:            []int32{},
		BenefitsRef:         []*BenefitsTableCfg{},
		BenefitsValue:       []float32{},
		Image:               "",
		Name:                "",
		Desc:                "",
		Param:               []string{},
		Quality:             0,
		QualityRef:          nil,
		AutoUse:             false,
		RepeatAutoTransform: false,
		ChestDropGroup:      0,
		ChestDropGroupRef:   nil,
		ChestSelect:         0,
		ChestSelectRef:      nil,
		MaxUseCnt:           0,
		IsDiamond:           false,
		DiamondExchange:     false,
		DiamondCnt:          0,
		ShowSource:          false,
		ItemSource:          []int32{},
		ItemSourceRef:       []*ItemSourceTableCfg{},
		InBag:               false,
		BagType:             BagType(enumDefaultValue),
	}
}

func NewMockItemTableCfg() *ItemTableCfg {
	return &ItemTableCfg{
		Id:                  0,
		StringId:            "",
		Priority:            0,
		Type:                ItemType(enumDefaultValue),
		Value:               0,
		Benefits:            []int32{0},
		BenefitsRef:         []*BenefitsTableCfg{},
		BenefitsValue:       []float32{0.0},
		Image:               "",
		Name:                "",
		Desc:                "",
		Param:               []string{""},
		Quality:             0,
		QualityRef:          nil,
		AutoUse:             false,
		RepeatAutoTransform: false,
		ChestDropGroup:      0,
		ChestDropGroupRef:   nil,
		ChestSelect:         0,
		ChestSelectRef:      nil,
		MaxUseCnt:           0,
		IsDiamond:           false,
		DiamondExchange:     false,
		DiamondCnt:          0,
		ShowSource:          false,
		ItemSource:          []int32{0},
		ItemSourceRef:       []*ItemSourceTableCfg{},
		InBag:               false,
		BagType:             BagType(enumDefaultValue),
	}
}

type ItemTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*ItemTableCfg
	localIds         map[int32]struct{}
}

func NewItemTable(configs *Configs) *ItemTable {
	return &ItemTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*ItemTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *ItemTable) Get(key int32) *ItemTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *ItemTable) GetAll() map[int32]*ItemTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *ItemTable) put(key int32, value *ItemTableCfg, local bool) *ItemTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *ItemTable) putFromInheritedTable(key int32, value *ItemTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[ItemTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *ItemTable) Put(key int32, value *ItemTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[ItemTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *ItemTable) PutAll(m map[int32]*ItemTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *ItemTable) Range(f func(v *ItemTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *ItemTable) Filter(filterFuncs ...func(v *ItemTableCfg) bool) map[int32]*ItemTableCfg {
	filtered := map[int32]*ItemTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *ItemTable) FilterSlice(filterFuncs ...func(v *ItemTableCfg) bool) []*ItemTableCfg {
	filtered := []*ItemTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *ItemTable) FilterKeys(filterFuncs ...func(v *ItemTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *ItemTable) satisfied(v *ItemTableCfg, filterFuncs ...func(v *ItemTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *ItemTable) setupIndexes() error {
	return nil
}

func (t *ItemTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *ItemTableCfg) bindRefs(c *Configs) {
	for _, e := range r.Benefits {
		cfgoRefRecord := c.BenefitsTable.Get(e)
		r.BenefitsRef = append(r.BenefitsRef, cfgoRefRecord)
	}
	r.QualityRef = c.ItemQualityTable.Get(r.Quality)
	r.ChestDropGroupRef = c.DropGroupTable.Get(r.ChestDropGroup)
	r.ChestSelectRef = c.SelectChestGroupTable.Get(r.ChestSelect)
	for _, e := range r.ItemSource {
		cfgoRefRecord := c.ItemSourceTable.Get(e)
		r.ItemSourceRef = append(r.ItemSourceRef, cfgoRefRecord)
	}
}

func (t *ItemTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[ItemTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewItemTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[ItemTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// StringId
		{
			recordCfg.StringId = strings.TrimSpace(record[t.getIndexInCsv("StringId")])
		}
		// Priority
		{
			if record[t.getIndexInCsv("Priority")] == "" {
				recordCfg.Priority = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Priority")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, varName=Priority, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Priority")], err)
					} else {
						return fmt.Errorf("[ItemTable]unmarshal csv record failed, varName=Priority, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Priority")], err)
					}
				}
				recordCfg.Priority = int32(cfgoInt)
			}
		}
		// Type
		{
			if record[t.getIndexInCsv("Type")] == "" {
				recordCfg.Type = ItemType(enumDefaultValue)
			} else {
				cfgoEnum, err := parseItemType(record[t.getIndexInCsv("Type")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, varName=Type, type=enum@ItemType, value=%s, err:[%s]\n", record[t.getIndexInCsv("Type")], err)
					} else {
						return fmt.Errorf("[ItemTable]unmarshal csv record failed, varName=Type, type=enum@ItemType, value=%s, err:[%s]", record[t.getIndexInCsv("Type")], err)
					}
				}
				recordCfg.Type = cfgoEnum
			}
		}
		// Value
		{
			if record[t.getIndexInCsv("Value")] == "" {
				recordCfg.Value = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Value")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, varName=Value, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Value")], err)
					} else {
						return fmt.Errorf("[ItemTable]unmarshal csv record failed, varName=Value, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Value")], err)
					}
				}
				recordCfg.Value = int32(cfgoInt)
			}
		}
		// Benefits
		{
			if record[t.getIndexInCsv("Benefits")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Benefits")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfBenefits int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfBenefits = 0
					} else {
						var err error
						cfgoElemOfBenefits, err = configs.BenefitsTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [ItemTable]unmarshal record failed, cannot parse ref@BenefitsTable in vector, varName=Benefits, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[ItemTable]unmarshal record failed, cannot parse ref@BenefitsTable in vector, varName=Benefits, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.Benefits = append(recordCfg.Benefits, cfgoElemOfBenefits)
				}
			}
		}
		// BenefitsValue
		{
			if record[t.getIndexInCsv("BenefitsValue")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("BenefitsValue")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfBenefitsValue float32 = 0.0
					if cfgoSplitStr == "" {
						cfgoElemOfBenefitsValue = 0
					} else {
						cfgoFloat, err := strconv.ParseFloat(cfgoSplitStr, 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [ItemTable]unmarshal record failed, cannot parse float in vector, varName=BenefitsValue, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[ItemTable]unmarshal record failed, cannot parse float in vector, varName=BenefitsValue, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfBenefitsValue = float32(cfgoFloat)
					}

					recordCfg.BenefitsValue = append(recordCfg.BenefitsValue, cfgoElemOfBenefitsValue)
				}
			}
		}
		// Image
		{
			recordCfg.Image = strings.TrimSpace(record[t.getIndexInCsv("Image")])
		}
		// Name
		{
			recordCfg.Name = strings.TrimSpace(record[t.getIndexInCsv("Name")])
		}
		// Desc
		{
			recordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Desc")])
		}
		// Param
		{
			if record[t.getIndexInCsv("Param")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Param")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfParam string = ""
					cfgoElemOfParam = strings.TrimSpace(cfgoSplitStr)

					recordCfg.Param = append(recordCfg.Param, cfgoElemOfParam)
				}
			}
		}
		// Quality
		if record[t.getIndexInCsv("Quality")] == "" {
			recordCfg.Quality = 0
		} else {
			var err error
			recordCfg.Quality, err = configs.ItemQualityTable.getIdByRef(record[t.getIndexInCsv("Quality")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, varName=Quality, type=ref@ItemQualityTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("Quality")], err)
				} else {
					return fmt.Errorf("[ItemTable]unmarshal csv record failed, varName=Quality, type=ref@ItemQualityTable, value=%s, err:[%s]", record[t.getIndexInCsv("Quality")], err)
				}
			}
		}
		// AutoUse
		{
			if record[t.getIndexInCsv("AutoUse")] == "" {
				recordCfg.AutoUse = false
			} else {
				var err error
				recordCfg.AutoUse, err = strconv.ParseBool(record[t.getIndexInCsv("AutoUse")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, varName=AutoUse, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("AutoUse")], err)
					} else {
						return fmt.Errorf("[ItemTable]unmarshal csv record failed, varName=AutoUse, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("AutoUse")], err)
					}
				}
			}
		}
		// RepeatAutoTransform
		{
			if record[t.getIndexInCsv("RepeatAutoTransform")] == "" {
				recordCfg.RepeatAutoTransform = false
			} else {
				var err error
				recordCfg.RepeatAutoTransform, err = strconv.ParseBool(record[t.getIndexInCsv("RepeatAutoTransform")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, varName=RepeatAutoTransform, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("RepeatAutoTransform")], err)
					} else {
						return fmt.Errorf("[ItemTable]unmarshal csv record failed, varName=RepeatAutoTransform, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("RepeatAutoTransform")], err)
					}
				}
			}
		}
		// ChestDropGroup
		if record[t.getIndexInCsv("ChestDropGroup")] == "" {
			recordCfg.ChestDropGroup = 0
		} else {
			var err error
			recordCfg.ChestDropGroup, err = configs.DropGroupTable.getIdByRef(record[t.getIndexInCsv("ChestDropGroup")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, varName=ChestDropGroup, type=ref@DropGroupTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("ChestDropGroup")], err)
				} else {
					return fmt.Errorf("[ItemTable]unmarshal csv record failed, varName=ChestDropGroup, type=ref@DropGroupTable, value=%s, err:[%s]", record[t.getIndexInCsv("ChestDropGroup")], err)
				}
			}
		}
		// ChestSelect
		if record[t.getIndexInCsv("ChestSelect")] == "" {
			recordCfg.ChestSelect = 0
		} else {
			var err error
			recordCfg.ChestSelect, err = configs.SelectChestGroupTable.getIdByRef(record[t.getIndexInCsv("ChestSelect")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, varName=ChestSelect, type=ref@SelectChestGroupTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("ChestSelect")], err)
				} else {
					return fmt.Errorf("[ItemTable]unmarshal csv record failed, varName=ChestSelect, type=ref@SelectChestGroupTable, value=%s, err:[%s]", record[t.getIndexInCsv("ChestSelect")], err)
				}
			}
		}
		// MaxUseCnt
		{
			if record[t.getIndexInCsv("MaxUseCnt")] == "" {
				recordCfg.MaxUseCnt = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("MaxUseCnt")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, varName=MaxUseCnt, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("MaxUseCnt")], err)
					} else {
						return fmt.Errorf("[ItemTable]unmarshal csv record failed, varName=MaxUseCnt, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("MaxUseCnt")], err)
					}
				}
				recordCfg.MaxUseCnt = int32(cfgoInt)
			}
		}
		// IsDiamond
		{
			if record[t.getIndexInCsv("IsDiamond")] == "" {
				recordCfg.IsDiamond = false
			} else {
				var err error
				recordCfg.IsDiamond, err = strconv.ParseBool(record[t.getIndexInCsv("IsDiamond")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, varName=IsDiamond, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("IsDiamond")], err)
					} else {
						return fmt.Errorf("[ItemTable]unmarshal csv record failed, varName=IsDiamond, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("IsDiamond")], err)
					}
				}
			}
		}
		// DiamondExchange
		{
			if record[t.getIndexInCsv("DiamondExchange")] == "" {
				recordCfg.DiamondExchange = false
			} else {
				var err error
				recordCfg.DiamondExchange, err = strconv.ParseBool(record[t.getIndexInCsv("DiamondExchange")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, varName=DiamondExchange, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("DiamondExchange")], err)
					} else {
						return fmt.Errorf("[ItemTable]unmarshal csv record failed, varName=DiamondExchange, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("DiamondExchange")], err)
					}
				}
			}
		}
		// DiamondCnt
		{
			if record[t.getIndexInCsv("DiamondCnt")] == "" {
				recordCfg.DiamondCnt = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("DiamondCnt")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, varName=DiamondCnt, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("DiamondCnt")], err)
					} else {
						return fmt.Errorf("[ItemTable]unmarshal csv record failed, varName=DiamondCnt, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("DiamondCnt")], err)
					}
				}
				recordCfg.DiamondCnt = int32(cfgoInt)
			}
		}
		// ShowSource
		{
			if record[t.getIndexInCsv("ShowSource")] == "" {
				recordCfg.ShowSource = false
			} else {
				var err error
				recordCfg.ShowSource, err = strconv.ParseBool(record[t.getIndexInCsv("ShowSource")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, varName=ShowSource, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("ShowSource")], err)
					} else {
						return fmt.Errorf("[ItemTable]unmarshal csv record failed, varName=ShowSource, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("ShowSource")], err)
					}
				}
			}
		}
		// ItemSource
		{
			if record[t.getIndexInCsv("ItemSource")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("ItemSource")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfItemSource int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfItemSource = 0
					} else {
						var err error
						cfgoElemOfItemSource, err = configs.ItemSourceTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [ItemTable]unmarshal record failed, cannot parse ref@ItemSourceTable in vector, varName=ItemSource, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[ItemTable]unmarshal record failed, cannot parse ref@ItemSourceTable in vector, varName=ItemSource, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.ItemSource = append(recordCfg.ItemSource, cfgoElemOfItemSource)
				}
			}
		}
		// InBag
		{
			if record[t.getIndexInCsv("InBag")] == "" {
				recordCfg.InBag = false
			} else {
				var err error
				recordCfg.InBag, err = strconv.ParseBool(record[t.getIndexInCsv("InBag")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, varName=InBag, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("InBag")], err)
					} else {
						return fmt.Errorf("[ItemTable]unmarshal csv record failed, varName=InBag, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("InBag")], err)
					}
				}
			}
		}
		// BagType
		{
			if record[t.getIndexInCsv("BagType")] == "" {
				recordCfg.BagType = BagType(enumDefaultValue)
			} else {
				cfgoEnum, err := parseBagType(record[t.getIndexInCsv("BagType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, varName=BagType, type=enum@BagType, value=%s, err:[%s]\n", record[t.getIndexInCsv("BagType")], err)
					} else {
						return fmt.Errorf("[ItemTable]unmarshal csv record failed, varName=BagType, type=enum@BagType, value=%s, err:[%s]", record[t.getIndexInCsv("BagType")], err)
					}
				}
				recordCfg.BagType = cfgoEnum
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [ItemTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[ItemTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *ItemTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "ItemTable.csv") && (!strings.HasPrefix(fileName, "ItemTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for ItemTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[ItemTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[ItemTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[ItemTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[ItemTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[ItemTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[ItemTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[ItemTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[ItemTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [ItemTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *ItemTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[ItemTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [ItemTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *ItemTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[ItemTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *ItemTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[ItemTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[ItemTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
