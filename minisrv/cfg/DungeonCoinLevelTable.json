{"1": {"Id": 1, "Dungeon": 4, "Level": 1, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 2000}]}, "2": {"Id": 2, "Dungeon": 4, "Level": 2, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 2400}]}, "3": {"Id": 3, "Dungeon": 4, "Level": 3, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 2800}]}, "4": {"Id": 4, "Dungeon": 4, "Level": 4, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 3200}]}, "5": {"Id": 5, "Dungeon": 4, "Level": 5, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 3600}]}, "6": {"Id": 6, "Dungeon": 4, "Level": 6, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 3900}]}, "7": {"Id": 7, "Dungeon": 4, "Level": 7, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 4200}]}, "8": {"Id": 8, "Dungeon": 4, "Level": 8, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 4500}]}, "9": {"Id": 9, "Dungeon": 4, "Level": 9, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 4800}]}, "10": {"Id": 10, "Dungeon": 4, "Level": 10, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 5100}]}, "11": {"Id": 11, "Dungeon": 4, "Level": 11, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 5300}]}, "12": {"Id": 12, "Dungeon": 4, "Level": 12, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 5500}]}, "13": {"Id": 13, "Dungeon": 4, "Level": 13, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 5700}]}, "14": {"Id": 14, "Dungeon": 4, "Level": 14, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 5900}]}, "15": {"Id": 15, "Dungeon": 4, "Level": 15, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 6100}]}, "16": {"Id": 16, "Dungeon": 4, "Level": 16, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 6300}]}, "17": {"Id": 17, "Dungeon": 4, "Level": 17, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 6500}]}, "18": {"Id": 18, "Dungeon": 4, "Level": 18, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 6700}]}, "19": {"Id": 19, "Dungeon": 4, "Level": 19, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 6900}]}, "20": {"Id": 20, "Dungeon": 4, "Level": 20, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 7100}]}, "21": {"Id": 21, "Dungeon": 4, "Level": 21, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 7300}]}, "22": {"Id": 22, "Dungeon": 4, "Level": 22, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 7500}]}, "23": {"Id": 23, "Dungeon": 4, "Level": 23, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 7700}]}, "24": {"Id": 24, "Dungeon": 4, "Level": 24, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 7900}]}, "25": {"Id": 25, "Dungeon": 4, "Level": 25, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 8100}]}, "26": {"Id": 26, "Dungeon": 4, "Level": 26, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 8300}]}, "27": {"Id": 27, "Dungeon": 4, "Level": 27, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 8500}]}, "28": {"Id": 28, "Dungeon": 4, "Level": 28, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 8700}]}, "29": {"Id": 29, "Dungeon": 4, "Level": 29, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 8900}]}, "30": {"Id": 30, "Dungeon": 4, "Level": 30, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 9100}]}, "31": {"Id": 31, "Dungeon": 4, "Level": 31, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 9300}]}, "32": {"Id": 32, "Dungeon": 4, "Level": 32, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 9500}]}, "33": {"Id": 33, "Dungeon": 4, "Level": 33, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 9700}]}, "34": {"Id": 34, "Dungeon": 4, "Level": 34, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 9900}]}, "35": {"Id": 35, "Dungeon": 4, "Level": 35, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 10100}]}, "36": {"Id": 36, "Dungeon": 4, "Level": 36, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 10300}]}, "37": {"Id": 37, "Dungeon": 4, "Level": 37, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 10500}]}, "38": {"Id": 38, "Dungeon": 4, "Level": 38, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 10700}]}, "39": {"Id": 39, "Dungeon": 4, "Level": 39, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 10900}]}, "40": {"Id": 40, "Dungeon": 4, "Level": 40, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 11100}]}, "41": {"Id": 41, "Dungeon": 4, "Level": 41, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 11200}]}, "42": {"Id": 42, "Dungeon": 4, "Level": 42, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 11300}]}, "43": {"Id": 43, "Dungeon": 4, "Level": 43, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 11400}]}, "44": {"Id": 44, "Dungeon": 4, "Level": 44, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 11500}]}, "45": {"Id": 45, "Dungeon": 4, "Level": 45, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 11600}]}, "46": {"Id": 46, "Dungeon": 4, "Level": 46, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 11700}]}, "47": {"Id": 47, "Dungeon": 4, "Level": 47, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 11800}]}, "48": {"Id": 48, "Dungeon": 4, "Level": 48, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 11900}]}, "49": {"Id": 49, "Dungeon": 4, "Level": 49, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 12000}]}, "50": {"Id": 50, "Dungeon": 4, "Level": 50, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 12100}]}, "51": {"Id": 51, "Dungeon": 4, "Level": 51, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 12200}]}, "52": {"Id": 52, "Dungeon": 4, "Level": 52, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 12300}]}, "53": {"Id": 53, "Dungeon": 4, "Level": 53, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 12400}]}, "54": {"Id": 54, "Dungeon": 4, "Level": 54, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 12500}]}, "55": {"Id": 55, "Dungeon": 4, "Level": 55, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 12600}]}, "56": {"Id": 56, "Dungeon": 4, "Level": 56, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 12700}]}, "57": {"Id": 57, "Dungeon": 4, "Level": 57, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 12800}]}, "58": {"Id": 58, "Dungeon": 4, "Level": 58, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 12900}]}, "59": {"Id": 59, "Dungeon": 4, "Level": 59, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 13000}]}, "60": {"Id": 60, "Dungeon": 4, "Level": 60, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 13100}]}, "61": {"Id": 61, "Dungeon": 4, "Level": 61, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 13200}]}, "62": {"Id": 62, "Dungeon": 4, "Level": 62, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 13300}]}, "63": {"Id": 63, "Dungeon": 4, "Level": 63, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 13400}]}, "64": {"Id": 64, "Dungeon": 4, "Level": 64, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 13500}]}, "65": {"Id": 65, "Dungeon": 4, "Level": 65, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 13600}]}, "66": {"Id": 66, "Dungeon": 4, "Level": 66, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 13700}]}, "67": {"Id": 67, "Dungeon": 4, "Level": 67, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 13800}]}, "68": {"Id": 68, "Dungeon": 4, "Level": 68, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 13900}]}, "69": {"Id": 69, "Dungeon": 4, "Level": 69, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 14000}]}, "70": {"Id": 70, "Dungeon": 4, "Level": 70, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 14100}]}, "71": {"Id": 71, "Dungeon": 4, "Level": 71, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 14200}]}, "72": {"Id": 72, "Dungeon": 4, "Level": 72, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 14300}]}, "73": {"Id": 73, "Dungeon": 4, "Level": 73, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 14400}]}, "74": {"Id": 74, "Dungeon": 4, "Level": 74, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 14500}]}, "75": {"Id": 75, "Dungeon": 4, "Level": 75, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 14600}]}, "76": {"Id": 76, "Dungeon": 4, "Level": 76, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 14700}]}, "77": {"Id": 77, "Dungeon": 4, "Level": 77, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 14800}]}, "78": {"Id": 78, "Dungeon": 4, "Level": 78, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 14900}]}, "79": {"Id": 79, "Dungeon": 4, "Level": 79, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 15000}]}, "80": {"Id": 80, "Dungeon": 4, "Level": 80, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 15100}]}, "81": {"Id": 81, "Dungeon": 4, "Level": 81, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 15200}]}, "82": {"Id": 82, "Dungeon": 4, "Level": 82, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 15300}]}, "83": {"Id": 83, "Dungeon": 4, "Level": 83, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 15400}]}, "84": {"Id": 84, "Dungeon": 4, "Level": 84, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 15500}]}, "85": {"Id": 85, "Dungeon": 4, "Level": 85, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 15600}]}, "86": {"Id": 86, "Dungeon": 4, "Level": 86, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 15700}]}, "87": {"Id": 87, "Dungeon": 4, "Level": 87, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 15800}]}, "88": {"Id": 88, "Dungeon": 4, "Level": 88, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 15900}]}, "89": {"Id": 89, "Dungeon": 4, "Level": 89, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 16000}]}, "90": {"Id": 90, "Dungeon": 4, "Level": 90, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 16100}]}, "91": {"Id": 91, "Dungeon": 4, "Level": 91, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 16200}]}, "92": {"Id": 92, "Dungeon": 4, "Level": 92, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 16300}]}, "93": {"Id": 93, "Dungeon": 4, "Level": 93, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 16400}]}, "94": {"Id": 94, "Dungeon": 4, "Level": 94, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 16500}]}, "95": {"Id": 95, "Dungeon": 4, "Level": 95, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 16600}]}, "96": {"Id": 96, "Dungeon": 4, "Level": 96, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 16700}]}, "97": {"Id": 97, "Dungeon": 4, "Level": 97, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 16800}]}, "98": {"Id": 98, "Dungeon": 4, "Level": 98, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 16900}]}, "99": {"Id": 99, "Dungeon": 4, "Level": 99, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 17000}]}, "100": {"Id": 100, "Dungeon": 4, "Level": 100, "ChooseNum": 5, "IsMax": true, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 2, "RewardValue": 17100}]}}