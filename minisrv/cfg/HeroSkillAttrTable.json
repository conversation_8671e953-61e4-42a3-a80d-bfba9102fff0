{"1001": {"Id": 1001, "StringId": "hero_1_0_hit_skill_attr_dmg_ratio", "Remark": "霰弹基础伤害系数", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1002": {"Id": 1002, "StringId": "hero_1_0_hit_skill_attr_dmg_type", "Remark": "霰弹技能伤害类型：电", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1003": {"Id": 1003, "StringId": "hero_1_0_hit_skill_attr_dmg_add_ratio", "Remark": "霰弹伤害加法修正系数", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1004": {"Id": 1004, "StringId": "hero_1_0_hit_skill_attr_dmg_mul_ratio", "Remark": "霰弹伤害乘法修正系数", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 2, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1005": {"Id": 1005, "StringId": "hero_1_0_hit_skill_attr_rapidfire_cnt", "Remark": "霰弹发射次数", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1006": {"Id": 1006, "StringId": "hero_1_0_hit_skill_attr_rapidfire_interval", "Remark": "霰弹发射间隔，单位：秒", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1007": {"Id": 1007, "StringId": "hero_1_0_hit_skill_attr_rapidfire_dmg_add_ratio", "Remark": "霰弹连发伤害加法修正系数", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1008": {"Id": 1008, "StringId": "hero_1_0_hit_skill_attr_repel_buff", "Remark": "霰弹击退bufff", "SkillEffectType": 13, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 2, "Effect": 0}, "1009": {"Id": 1009, "StringId": "hero_1_0_hit_skill_attr_repel_distance", "Remark": "霰弹击退距离，单位：米", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1010": {"Id": 1010, "StringId": "hero_1_0_hit_skill_attr_repel_time", "Remark": "霰弹击退时间，单位：秒", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1011": {"Id": 1011, "StringId": "hero_1_0_hit_skill_attr_repel_distance_add_ratio", "Remark": "霰弹击退距离加法修正系数", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1012": {"Id": 1012, "StringId": "hero_1_0_hit_skill_bullet_radius", "Remark": "霰弹子弹半径，单位：米", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 18, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1013": {"Id": 1013, "StringId": "hero_1_0_hit_skill_bullet_angle", "Remark": "霰弹子弹角度，单位：度", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 30, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1014": {"Id": 1014, "StringId": "hero_1_0_hit_skill_bullet_radius_add_ratio", "Remark": "霰弹子弹半径加法修正系数", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1015": {"Id": 1015, "StringId": "hero_1_0_hit_skill_bullet_angle_add_ratio", "Remark": "霰弹子弹角度加法修正系数", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1016": {"Id": 1016, "StringId": "hero_1_0_hit_skill_attr_paralysis_boolean", "Remark": "霰弹命中是否附加麻痹debuff：0，否；1，是", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1017": {"Id": 1017, "StringId": "hero_1_0_hit_skill_attr_paralysis_chance", "Remark": "霰弹命中附加麻痹debuff概率", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1018": {"Id": 1018, "StringId": "hero_1_0_hit_skill_attr_paralysis_buff_type", "Remark": "霰弹命中附加麻痹debuff类型", "SkillEffectType": 13, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 4, "Effect": 0}, "1019": {"Id": 1019, "StringId": "hero_1_0_hit_skill_attr_paralysis_time", "Remark": "霰弹命中附加麻痹debuff时间，单位：秒,-1=一直", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1020": {"Id": 1020, "StringId": "hero_1_0_hit_skill_attr_dmg_up_boolean", "Remark": "霰弹命中后是否对精英和首领增伤：0，否；1，是", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1021": {"Id": 1021, "StringId": "hero_1_0_hit_skill_attr_dmg_up_ratio", "Remark": "霰弹命中后对精英和首领增伤比例", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1022": {"Id": 1022, "StringId": "hero_1_0_hit_skill_attr_light_debuff_explode_boolean", "Remark": "霰弹命中点燃debuff怪物时是否发生爆炸", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1023": {"Id": 1023, "StringId": "hero_1_0_hit_skill_attr_light_debuff_explode_dmg_ratio", "Remark": "霰弹命中点燃debuff怪物时爆炸伤害占当前伤害比例", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1024": {"Id": 1024, "StringId": "hero_1_0_hit_skill_attr_light_debuff_explode_radius", "Remark": "霰弹命中点燃debuff怪物时爆炸半径", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.8, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1025": {"Id": 1025, "StringId": "hero_1_0_hit_skill_attr_neighbor_dmg_boolean", "Remark": "电磁网命中的每个怪物是否都对周围小范围造成伤害：0，否；1，是", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1026": {"Id": 1026, "StringId": "hero_1_0_hit_skill_attr_neighbor_dmg_ratio", "Remark": "小范围伤害占基础伤害比例", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1027": {"Id": 1027, "StringId": "hero_1_0_hit_skill_attr_neighbor_dmg_radius", "Remark": "小范围伤害半径", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1028": {"Id": 1028, "StringId": "hero_1_0_hit_skill_attr_neighbor_pull_type", "Remark": "对周围小范围怪牵引类型", "SkillEffectType": 13, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 3, "Effect": 0}, "1029": {"Id": 1029, "StringId": "hero_1_0_hit_skill_attr_neighbor_pull_distance", "Remark": "对周围小范围怪牵引距离：单位，米", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1030": {"Id": 1030, "StringId": "hero_1_0_hit_skill_attr_neighbor_pull_time", "Remark": "对周围小范围怪牵引时间：单位，秒", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1031": {"Id": 1031, "StringId": "hero_1_0_hit_skill_attr_kill_trigger_boolean", "Remark": "霰弹击杀怪物后是否触发引雷：0，否；1，是", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1032": {"Id": 1032, "StringId": "hero_1_0_hit_skill_attr_kill_trigger_radius", "Remark": "引雷半径：单位，米", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 40, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1033": {"Id": 1033, "StringId": "hero_1_0_hit_skill_attr_kill_trigger_dmg_ratio", "Remark": "引雷半径占当前伤害比例", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1034": {"Id": 1034, "StringId": "hero_1_0_hit_skill_attr_kill_trigger_model", "Remark": "引雷模型（通用电击伤害）", "SkillEffectType": 13, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 1007, "BuffType": 0, "Effect": 0}, "1035": {"Id": 1035, "StringId": "hero_1_0_hit_skill_bullet_model", "Remark": "霰弹子弹模型", "SkillEffectType": 13, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 1002, "BuffType": 0, "Effect": 0}, "1036": {"Id": 1036, "StringId": "hero_1_0_hit_skill_self_se", "Remark": "发射霰弹自身特效", "SkillEffectType": 13, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 1001, "BuffType": 0, "Effect": 0}, "1037": {"Id": 1037, "StringId": "hero_1_0_hit_skill_explode_model", "Remark": "霰弹爆炸模型", "SkillEffectType": 13, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 1006, "BuffType": 0, "Effect": 0}, "1038": {"Id": 1038, "StringId": "hero_1_0_hit_skill_attr_thunder_strike_boolean", "Remark": "霰弹命中怪物时是否触发无强化雷击", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1039": {"Id": 1039, "StringId": "hero_1_0_hit_skill_attr_thunder_strike_dmg_ratio", "Remark": "霰弹命中怪物时触发无强化雷击的伤害占比", "SkillEffectType": 13, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1101": {"Id": 1101, "StringId": "hero_1_1_hit_skill_attr_dmg_ratio", "Remark": "茄子忍者基础伤害系数", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1102": {"Id": 1102, "StringId": "hero_1_1_hit_skill_attr_dmg_type", "Remark": "茄子忍者技能伤害类型：物理", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 6, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1103": {"Id": 1103, "StringId": "hero_1_1_hit_skill_attr_dmg_add_ratio", "Remark": "茄子忍者基础伤害加法修正系数", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1104": {"Id": 1104, "StringId": "hero_1_1_hit_skill_attr_dmg_mul_ratio", "Remark": "茄子忍者基础伤害乘法修正系数", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 2, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1105": {"Id": 1105, "StringId": "hero_1_1_hit_skill_attr_strike_dmg_add_ratio", "Remark": "茄子忍者基础直击伤害加法修正系数", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1106": {"Id": 1106, "StringId": "hero_1_1_hit_skill_attr_time", "Remark": "手里剑持续时间：单位，秒", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 8, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1107": {"Id": 1107, "StringId": "hero_1_1_hit_skill_attr_time_add_ratio", "Remark": "手里剑持续时间加法修正系数", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1108": {"Id": 1108, "StringId": "hero_1_1_hit_skill_attr_fly_speed", "Remark": "手里剑飞行速度：单位，米/秒", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 15, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1109": {"Id": 1109, "StringId": "hero_1_1_hit_skill_attr_fly_speed_add_ratio", "Remark": "手里剑飞行速度加法修正系数", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1110": {"Id": 1110, "StringId": "hero_1_1_hit_skill_attr_atk_fly_speed_ratio", "Remark": "手里剑攻击怪物时飞行速度占当前速度比例", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1111": {"Id": 1111, "StringId": "hero_1_1_hit_skill_attr_dmg_interval", "Remark": "手里剑伤害间隔：单位，秒", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1112": {"Id": 1112, "StringId": "hero_1_1_hit_skill_attr_radius", "Remark": "手里剑半径：单位，米", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1113": {"Id": 1113, "StringId": "hero_1_1_hit_skill_attr_radius_add_ratio", "Remark": "手里剑半径加法修正系数", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1114": {"Id": 1114, "StringId": "hero_1_1_hit_skill_attr_stun_boolean", "Remark": "手里剑每次造成伤害时是否眩晕敌人：0，否；1,是", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1115": {"Id": 1115, "StringId": "hero_1_1_hit_skill_attr_stun_debuff_time", "Remark": "手里剑每次造成伤害时眩晕时间：单位，秒", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1116": {"Id": 1116, "StringId": "hero_1_1_hit_skill_attr_stun_debuff_type", "Remark": "手里剑每次造成伤害时眩晕debuff类型", "SkillEffectType": 16, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 6, "Effect": 0}, "1117": {"Id": 1117, "StringId": "hero_1_1_hit_skill_attr_light_boolean", "Remark": "手里剑每次造成伤害时是否点燃敌人：0，否；1,是", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1118": {"Id": 1118, "StringId": "hero_1_1_hit_skill_attr_light_debuff_type", "Remark": "手里剑点燃debuff类型", "SkillEffectType": 16, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 37, "Effect": 0}, "1119": {"Id": 1119, "StringId": "hero_1_1_hit_skill_attr_light_debuff_time", "Remark": "手里剑点燃debuff时间：单位，秒", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1120": {"Id": 1120, "StringId": "hero_1_1_hit_skill_attr_speed_down_boolean", "Remark": "手里剑每次造成伤害时是否减速敌人：0，否；1,是", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1121": {"Id": 1121, "StringId": "hero_1_1_hit_skill_attr_speed_down_debuff_type", "Remark": "手里剑每次造成伤害时附加减速debuff类型", "SkillEffectType": 16, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 14, "Effect": 0}, "1122": {"Id": 1122, "StringId": "hero_1_1_hit_skill_attr_speed_down_debuff_time", "Remark": "手里剑每次造成伤害时附加减速debuff时间：单位，秒", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1123": {"Id": 1123, "StringId": "hero_1_1_hit_skill_attr_sword_qi_boolean", "Remark": "手里剑是否持续发射剑气：0，否；1,是（自动索敌，随机方向）", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1124": {"Id": 1124, "StringId": "hero_1_1_hit_skill_attr_sword_qi_dmg_ratio", "Remark": "手里剑是否持续发射剑气伤害占强化前基础伤害系数比例（不受强化影响）", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1125": {"Id": 1125, "StringId": "hero_1_1_hit_skill_attr_sword_qi_long", "Remark": "手里剑剑气长：单位，米", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1126": {"Id": 1126, "StringId": "hero_1_1_hit_skill_attr_sword_qi_wide", "Remark": "手里剑剑气宽：单位，米", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1127": {"Id": 1127, "StringId": "hero_1_1_hit_skill_attr_sword_qi_interval", "Remark": "手里剑剑气释放间隔：单位，米/秒", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1128": {"Id": 1128, "StringId": "hero_1_1_hit_skill_attr_sword_qi_fly_speed", "Remark": "手里剑剑气飞行速度：单位，米/秒", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 15, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1129": {"Id": 1129, "StringId": "hero_1_1_hit_skill_attr_sword_qi_fly_range", "Remark": "手里剑剑气飞行最大距离：单位，米/秒", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 40, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1130": {"Id": 1130, "StringId": "hero_1_1_hit_skill_attr_end_reserve_boolean", "Remark": "手里剑结束后是否原地保存：0，否；1,是", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1131": {"Id": 1131, "StringId": "hero_1_1_hit_skill_attr_end_reserve_time", "Remark": "手里剑结束后原地保存时间：单位，秒", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1132": {"Id": 1132, "StringId": "hero_1_1_hit_skill_attr_cnt", "Remark": "手里剑数量", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1133": {"Id": 1133, "StringId": "hero_1_1_hit_skill_attr_catapult_speed_down_boolean", "Remark": "手里剑弹射后是否加速：0，否；1,是", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1134": {"Id": 1134, "StringId": "hero_1_1_hit_skill_attr_catapult_speed_up_ratio", "Remark": "手里剑每次弹射后加速比例", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1135": {"Id": 1135, "StringId": "hero_1_1_hit_skill_attr_division_boolean", "Remark": "手里剑在结束后是否分裂出两个飞行利刃", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1136": {"Id": 1136, "StringId": "hero_1_1_hit_skill_attr_division_cnt", "Remark": "分裂出的飞行利刃数量", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1137": {"Id": 1137, "StringId": "hero_1_1_hit_skill_attr_division_dmg_ratio", "Remark": "飞行利刃伤害占当前伤害比例", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.8, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1138": {"Id": 1138, "StringId": "hero_1_1_hit_skill_attr_division_dmg_time", "Remark": "飞行利刃时间：单位，秒", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1139": {"Id": 1139, "StringId": "hero_1_1_hit_skill_attr_division_dmg_interval", "Remark": "飞行利刃伤害间隔：单位，秒", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1140": {"Id": 1140, "StringId": "hero_1_1_hit_skill_attr_division_radius_ratio", "Remark": "飞行利刃半径占当前半径比例", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.8, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1141": {"Id": 1141, "StringId": "hero_1_1_hit_skill_attr_division_radius_add_ratio", "Remark": "飞行利刃半径加法修正系数", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1142": {"Id": 1142, "StringId": "hero_1_1_hit_skill_attr_division_fly_speed", "Remark": "飞行利刃飞行速度：单位，米/秒", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 40, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1143": {"Id": 1143, "StringId": "hero_1_1_hit_skill_attr_dmg_up_debuff_boolean", "Remark": "飞行利刃首次命中的怪物是否赋予易伤debuff：0，否；1,是", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1144": {"Id": 1144, "StringId": "hero_1_1_hit_skill_attr_dmg_up_debuff_type", "Remark": "飞行利刃首次命中的怪物是否赋予易伤debuff类型", "SkillEffectType": 16, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 20, "Effect": 0}, "1145": {"Id": 1145, "StringId": "hero_1_1_hit_skill_attr_dmg_up_debuff_value", "Remark": "飞行利刃首次命中的怪物是否赋予易伤debuff值", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1146": {"Id": 1146, "StringId": "hero_1_1_hit_skill_attr_dmg_up_debuff_time", "Remark": "飞行利刃首次命中的怪物是否赋予易伤debuff时间", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "1147": {"Id": 1147, "StringId": "hero_1_1_hit_skill_model", "Remark": "手里剑模型", "SkillEffectType": 16, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 1101, "BuffType": 0, "Effect": 0}, "1148": {"Id": 1148, "StringId": "hero_1_1_hit_skill_self_se", "Remark": "发射手里剑自身特效", "SkillEffectType": 16, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 1109, "BuffType": 0, "Effect": 0}, "1149": {"Id": 1149, "StringId": "hero_1_1_hit_skill_division_model", "Remark": "飞行利刃模型", "SkillEffectType": 16, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 1110, "BuffType": 0, "Effect": 0}, "1150": {"Id": 1150, "StringId": "hero_1_1_hit_skill_sword_qi_model", "Remark": "剑气模型", "SkillEffectType": 16, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 1111, "BuffType": 0, "Effect": 0}, "1151": {"Id": 1151, "StringId": "hero_1_1_hit_skill_fire_model", "Remark": "烈焰手里剑模型", "SkillEffectType": 16, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 1105, "BuffType": 0, "Effect": 0}, "1152": {"Id": 1152, "StringId": "hero_1_1_hit_skill_attr_atk_fly_speed", "Remark": "手里剑攻击怪物时的飞行速度：单位，米/秒", "SkillEffectType": 16, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 7.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2001": {"Id": 2001, "StringId": "hero_2_0_hit_skill_attr_dmg_ratio", "Remark": "豌豆弹基础伤害系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2002": {"Id": 2002, "StringId": "hero_2_0_hit_skill_attr_dmg_type", "Remark": "豌豆弹技能伤害类型:物理", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 6, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2003": {"Id": 2003, "StringId": "hero_2_0_hit_skill_bullet_radius", "Remark": "豌豆弹半径，单位：米", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2004": {"Id": 2004, "StringId": "hero_2_0_hit_skill_attr_dmg_add_ratio", "Remark": "豌豆弹伤害加法修正系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2005": {"Id": 2005, "StringId": "hero_2_0_hit_skill_attr_dmg_mul_ratio", "Remark": "豌豆弹伤害乘法修正系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 2, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2006": {"Id": 2006, "StringId": "hero_2_0_hit_skill_attr_strike_dmg_add_ratio", "Remark": "碗豆弹直击伤害加法修正系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2007": {"Id": 2007, "StringId": "hero_2_0_hit_skill_attr_fly_speed", "Remark": "豌豆弹飞行速度,单位:米/秒", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 30, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2008": {"Id": 2008, "StringId": "hero_2_0_hit_skill_attr_ballistic_cnt", "Remark": "豌豆弹弹道数量", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2009": {"Id": 2009, "StringId": "hero_2_0_hit_skill_attr_ballistic_angle", "Remark": "豌豆弹弹道夹角", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2010": {"Id": 2010, "StringId": "hero_2_0_hit_skill_attr_rapidfire_cnt", "Remark": "豌豆弹发射次数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2011": {"Id": 2011, "StringId": "hero_2_0_hit_skill_attr_rapidfire_interval", "Remark": "豌豆弹发射间隔，单位：秒", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.15, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2012": {"Id": 2012, "StringId": "hero_2_0_hit_skill_attr_ballistic_dmg_add_ratio", "Remark": "豌豆弹多发直击伤害加法修正系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2013": {"Id": 2013, "StringId": "hero_2_0_hit_skill_attr_rapidfire_dmg_add_ratio", "Remark": "豌豆弹连发直击伤害加法修正系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2014": {"Id": 2014, "StringId": "hero_2_0_hit_skill_attr_explode_boolean", "Remark": "豌豆弹命中怪物后是否爆炸：0，否；1，是", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2015": {"Id": 2015, "StringId": "hero_2_0_hit_skill_explode_radius", "Remark": "豌豆弹爆炸半径，单位：米", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.75, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2016": {"Id": 2016, "StringId": "hero_2_0_hit_skill_attr_explode_dmg_ratio", "Remark": "豌豆弹爆炸伤害占当前技能系数比例", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.8, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2017": {"Id": 2017, "StringId": "hero_2_0_hit_skill_attr_explode_dmg_add_ratio", "Remark": "豌豆弹爆炸伤害加法修正系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2018": {"Id": 2018, "StringId": "hero_2_0_hit_skill_attr_explode_dmg_mul_ratio", "Remark": "豌豆弹爆炸伤害乘法修正系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 2, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2019": {"Id": 2019, "StringId": "hero_2_0_hit_skill_attr_explode_radius_add_ratio", "Remark": "豌豆弹爆炸半径加法修正系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2020": {"Id": 2020, "StringId": "hero_2_0_hit_skill_attr_division_boolean", "Remark": "豌豆弹命中后是否分裂：0，否；1，是", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2021": {"Id": 2021, "StringId": "hero_2_0_hit_skill_attr_division_fly_speed", "Remark": "分裂小豌豆飞行速度，单位：米/秒", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 20, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2022": {"Id": 2022, "StringId": "hero_2_0_hit_skill_attr_division_fly_range", "Remark": "分裂小豌豆最大飞行距离，单位：米", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 40, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2023": {"Id": 2023, "StringId": "hero_2_0_hit_skill_attr_division_cnt", "Remark": "豌豆弹命中后分裂小豌豆数量", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2024": {"Id": 2024, "StringId": "hero_2_0_hit_skill_division_radius", "Remark": "分裂小豌豆半径，单位：米", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.15, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2025": {"Id": 2025, "StringId": "hero_2_0_hit_skill_attr_division_dmg_ratio", "Remark": "分裂小豌豆伤害占当前技能系数比例", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2026": {"Id": 2026, "StringId": "hero_2_0_hit_skill_attr_division_dmg_add_ratio", "Remark": "分裂小豌豆伤害加法修正系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2027": {"Id": 2027, "StringId": "hero_2_0_hit_skill_attr_division_dmg_mul_ratio", "Remark": "分裂小豌豆伤害乘法修正系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 2, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2028": {"Id": 2028, "StringId": "hero_2_0_hit_skill_attr_kill_division_boolean", "Remark": "豌豆弹击杀怪物后是否分裂：0，否；1，是", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2029": {"Id": 2029, "StringId": "hero_2_0_hit_skill_attr_kill_division_cnt", "Remark": "豌豆弹击杀怪物后分裂小豌豆数量", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2030": {"Id": 2030, "StringId": "hero_2_0_hit_skill_attr_division_angle", "Remark": "分裂小豌豆分叉角度", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 30, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2031": {"Id": 2031, "StringId": "hero_2_0_hit_skill_attr_division_explode_boolean", "Remark": "分裂小豌豆命中怪物是否爆炸：0，否；1，是", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2032": {"Id": 2032, "StringId": "hero_2_0_hit_skill_division_explode_radius", "Remark": "分裂小豌豆爆炸半径，单位：米", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2033": {"Id": 2033, "StringId": "hero_2_0_hit_skill_attr_division_explode_dmg_ratio", "Remark": "分裂小豌豆爆炸伤害占当前技能系数比例", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.24, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2034": {"Id": 2034, "StringId": "hero_2_0_hit_skill_attr_division_explode_dmg_add_ratio", "Remark": "分裂小豌豆爆炸伤害加法修正系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2035": {"Id": 2035, "StringId": "hero_2_0_hit_skill_attr_division_explode_dmg_mul_ratio", "Remark": "分裂小豌豆爆炸伤害乘法修正系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 2, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2036": {"Id": 2036, "StringId": "hero_2_0_hit_skill_attr_division_explode_radius_add_ratio", "Remark": "分裂小豌豆爆炸半径加法修正系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2037": {"Id": 2037, "StringId": "hero_2_0_hit_skill_attr_dmg_up_to_debuff_boolean", "Remark": "豌豆弹对处于debuff状态的怪物是否增伤：0，否；1，是", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2038": {"Id": 2038, "StringId": "hero_2_0_hit_skill_attr_division_dmg_up_to_debuff_boolean", "Remark": "小豌豆对处于debuff状态的怪物是否增伤：0，否；1，是", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2039": {"Id": 2039, "StringId": "hero_2_0_hit_skill_attr_dmg_ratio_to_debuff", "Remark": "豌豆弹对处于debuff状态的怪物伤害系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2040": {"Id": 2040, "StringId": "hero_2_0_hit_skill_attr_division_dmg_ratio_to_debuff", "Remark": "小豌豆对处于debuff状态的怪物伤害系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2041": {"Id": 2041, "StringId": "hero_2_0_hit_skill_attr_fierce_cnt", "Remark": "豌豆弹穿透数量（填0表示没有穿透）", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2042": {"Id": 2042, "StringId": "hero_2_0_hit_skill_attr_catapult_cnt", "Remark": "豌豆弹弹射次数（撞到四面屏幕后镜面反射）", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2043": {"Id": 2043, "StringId": "hero_2_0_hit_skill_attr_fire_bullet_debuff_type", "Remark": "火焰子弹命中怪物附加的点燃debuff类型", "SkillEffectType": 15, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 34, "Effect": 0}, "2044": {"Id": 2044, "StringId": "hero_2_0_hit_skill_attr_fire_bullet_debuff_time", "Remark": "火焰子弹命中怪物附加的点燃debuff时间，单位：秒", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2045": {"Id": 2045, "StringId": "hero_2_0_hit_skill_attr_electric_bullet_debuff_type", "Remark": "电系子弹命中怪物附加的麻痹debuff类型", "SkillEffectType": 15, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 4, "Effect": 0}, "2046": {"Id": 2046, "StringId": "hero_2_0_hit_skill_attr_electric_bullet_debuff_time", "Remark": "电系子弹命中怪物附加的麻痹debuff时间，单位：秒", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2047": {"Id": 2047, "StringId": "hero_2_0_hit_skill_attr_frozen_bullet_debuff_type", "Remark": "急冻子弹命中怪物附加的冻结debuff类型", "SkillEffectType": 15, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 5, "Effect": 0}, "2048": {"Id": 2048, "StringId": "hero_2_0_hit_skill_attr_frozen_bullet_debuff_time", "Remark": "急冻子弹命中怪物附加的冻结debuff时间，单位：秒", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1.1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2049": {"Id": 2049, "StringId": "hero_2_0_hit_skill_attr_frozen_bullet_division_boolean", "Remark": "急冻子弹命中冻结的怪物后是否分裂小冰片", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2050": {"Id": 2050, "StringId": "hero_2_0_hit_skill_attr_frozen_bullet_division_fly_speed", "Remark": "小冰片飞行速度：单位，米/秒", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 20, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2051": {"Id": 2051, "StringId": "hero_2_0_hit_skill_attr_frozen_bullet_division_fly_range", "Remark": "小冰片飞行距离：单位，米", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 40, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2052": {"Id": 2052, "StringId": "hero_2_0_hit_skill_attr_frozen_bullet_division_cnt", "Remark": "小冰片数量", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2053": {"Id": 2053, "StringId": "hero_2_0_hit_skill_attr_frozen_bullet_division_dmg_ratio", "Remark": "小冰片伤害占当前伤害比例", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2054": {"Id": 2054, "StringId": "hero_2_0_hit_skill_attr_frozen_bullet_division_dmg_add_ratio", "Remark": "小冰片伤害加法修正系数", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2055": {"Id": 2055, "StringId": "hero_2_0_hit_skill_attr_frozen_bullet_division_radius", "Remark": "小冰片半径：单位，米", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.15, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2056": {"Id": 2056, "StringId": "hero_2_0_hit_skill_attr_frozen_bullet_division_angle", "Remark": "小冰片分裂角度", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2057": {"Id": 2057, "StringId": "hero_2_0_hit_skill_frozen_bullet_division_model", "Remark": "小冰片模型", "SkillEffectType": 15, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2006, "BuffType": 0, "Effect": 0}, "2058": {"Id": 2058, "StringId": "hero_2_0_hit_skill_bullet_model", "Remark": "豌豆弹模型", "SkillEffectType": 15, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2001, "BuffType": 0, "Effect": 0}, "2059": {"Id": 2059, "StringId": "hero_2_0_hit_skill_explode_model", "Remark": "豌豆弹爆炸模型", "SkillEffectType": 15, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2002, "BuffType": 0, "Effect": 0}, "2060": {"Id": 2060, "StringId": "hero_2_0_hit_skill_self_se", "Remark": "发射豌豆弹自身特效", "SkillEffectType": 15, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2003, "BuffType": 0, "Effect": 0}, "2061": {"Id": 2061, "StringId": "hero_2_0_hit_skill_affect_se", "Remark": "豌豆弹受击特效", "SkillEffectType": 15, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2004, "BuffType": 0, "Effect": 0}, "2062": {"Id": 2062, "StringId": "hero_2_0_hit_skill_division_model", "Remark": "分裂小豌豆模型", "SkillEffectType": 15, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2005, "BuffType": 0, "Effect": 0}, "2063": {"Id": 2063, "StringId": "hero_2_0_hit_skill_attr_burn_chance", "Remark": "火焰子弹命中时触发点燃概率", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2064": {"Id": 2064, "StringId": "hero_2_0_hit_skill_attr_froze_chance", "Remark": "急冻子弹命中时触发冰冻概率", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2065": {"Id": 2065, "StringId": "hero_2_0_hit_skill_attr_recovery_down_debuff_boolean", "Remark": "豌豆弹攻击是否附加重伤debuff：0，否；1，是", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2066": {"Id": 2066, "StringId": "hero_2_0_hit_skill_attr_recovery_down_debuff_type", "Remark": "豌豆弹攻击附加重伤debuff类型", "SkillEffectType": 15, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 40, "Effect": 0}, "2067": {"Id": 2067, "StringId": "hero_2_0_hit_skill_attr_recovery_down_debuff_chance", "Remark": "豌豆弹攻击附加重伤debuff概率", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2068": {"Id": 2068, "StringId": "hero_2_0_hit_skill_attr_recovery_down_debuff_time", "Remark": "豌豆弹攻击附加重伤debuff时间，单位：秒", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2069": {"Id": 2069, "StringId": "hero_2_0_hit_skill_attr_frozen_bullet_division_debuff_type", "Remark": "分裂冰片命中怪物附加的冻结debuff类型", "SkillEffectType": 15, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 5, "Effect": 0}, "2070": {"Id": 2070, "StringId": "hero_2_0_hit_skill_attr_frozen_bullet_division_debuff_time", "Remark": "分裂冰片命中怪物附加的冻结debuff时间，单位：秒", "SkillEffectType": 15, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2101": {"Id": 2101, "StringId": "hero_2_1_hit_skill_attr_dmg_ratio", "Remark": "冰弹基础伤害系数", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2102": {"Id": 2102, "StringId": "hero_2_1_hit_skill_attr_dmg_type", "Remark": "冰弹技能伤害类型:冰", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2103": {"Id": 2103, "StringId": "hero_2_1_hit_skill_attr_dmg_add_ratio", "Remark": "冰弹伤害加法修正系数", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2104": {"Id": 2104, "StringId": "hero_2_1_hit_skill_attr_dmg_mul_ratio", "Remark": "冰弹伤害乘法修正系数", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 2, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2105": {"Id": 2105, "StringId": "hero_2_1_hit_skill_attr_strike_dmg_add_ratio", "Remark": "冰弹直击伤害加法修正系数", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2106": {"Id": 2106, "StringId": "hero_2_1_hit_skill_attr_fly_speed", "Remark": "冰弹飞行速度,单位:米/秒", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 20, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2107": {"Id": 2107, "StringId": "hero_2_1_hit_skill_attr_repel_buff", "Remark": "冰弹击退buff类型", "SkillEffectType": 10, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 2, "Effect": 0}, "2108": {"Id": 2108, "StringId": "hero_2_1_hit_skill_attr_repel_distance", "Remark": "冰弹击退距离，单位：米", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.25, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2109": {"Id": 2109, "StringId": "hero_2_1_hit_skill_attr_repel_time", "Remark": "冰弹击退时间，单位：秒", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2110": {"Id": 2110, "StringId": "hero_2_1_hit_skill_attr_ballistic_cnt", "Remark": "冰弹弹道数量", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2111": {"Id": 2111, "StringId": "hero_2_1_hit_skill_attr_ballistic_angle", "Remark": "冰弹弹道夹角", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 15, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2112": {"Id": 2112, "StringId": "hero_2_1_hit_skill_attr_rapidfire_cnt", "Remark": "冰弹连发次数", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2113": {"Id": 2113, "StringId": "hero_2_1_hit_skill_attr_rapidfire_interval", "Remark": "冰弹发射间隔，单位：秒", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2114": {"Id": 2114, "StringId": "hero_2_1_hit_skill_attr_ballistic_dmg_add_ratio", "Remark": "冰弹多发直击伤害加法修正系数", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2115": {"Id": 2115, "StringId": "hero_2_1_hit_skill_attr_rapidfire_dmg_add_ratio", "Remark": "冰弹连发直击伤害加法修正系数", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2116": {"Id": 2116, "StringId": "hero_2_1_hit_skill_attr_fierce_cnt", "Remark": "冰弹穿透数量，填0表示只命中1个，-1表示无限", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2117": {"Id": 2117, "StringId": "hero_2_1_hit_skill_attr_fierce_dmg_ratio", "Remark": "冰弹穿透后命中目标伤害占当前伤害比例", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2118": {"Id": 2118, "StringId": "hero_2_1_hit_skill_attr_division_boolean", "Remark": "冰弹弹首次命中后是否分裂：0，否；1，是", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2119": {"Id": 2119, "StringId": "hero_2_1_hit_skill_attr_division_fly_speed", "Remark": "分裂小冰弹飞行速度，单位：米/秒", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 15, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2120": {"Id": 2120, "StringId": "hero_2_1_hit_skill_attr_division_fly_range", "Remark": "分裂小冰弹最大飞行距离，单位：米", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 40, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2121": {"Id": 2121, "StringId": "hero_2_1_hit_skill_attr_division_cnt", "Remark": "冰弹首次命中后分裂小冰弹数量（小冰弹是索敌的）", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2122": {"Id": 2122, "StringId": "hero_2_1_hit_skill_attr_division_dmg_ratio", "Remark": "分裂小冰弹伤害占当前技能系数比例", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2123": {"Id": 2123, "StringId": "hero_2_1_hit_skill_attr_division_dmg_add_ratio", "Remark": "分裂小冰弹伤害加法修正系数", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2124": {"Id": 2124, "StringId": "hero_2_1_hit_skill_attr_division_dmg_mul_ratio", "Remark": "分裂小冰弹伤害乘法修正系数", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 2, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2125": {"Id": 2125, "StringId": "hero_2_1_hit_skill_attr_kill_division_boolean", "Remark": "冰弹击杀怪物后是否分裂：0，否；1，是", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2126": {"Id": 2126, "StringId": "hero_2_1_hit_skill_attr_kill_division_cnt", "Remark": "冰弹击杀怪物后分裂小冰弹数量（小冰弹是索敌的）", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2127": {"Id": 2127, "StringId": "hero_2_1_hit_skill_attr_frozen_boolean", "Remark": "冰弹命中是否附加冻结debuff：0，否；1，是", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2128": {"Id": 2128, "StringId": "hero_2_1_hit_skill_attr_frozen_chance", "Remark": "冰弹命中附加冻结debuff概率", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2129": {"Id": 2129, "StringId": "hero_2_1_hit_skill_attr_frozen_buff_type", "Remark": "冰弹命中附加冻结debuff类型", "SkillEffectType": 10, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 5, "Effect": 0}, "2130": {"Id": 2130, "StringId": "hero_2_1_hit_skill_attr_frozen_time", "Remark": "冰弹命中附件冻结debuff时间，单位：秒,-1=一直", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2131": {"Id": 2131, "StringId": "hero_2_1_hit_skill_attr_dot_frozen_boolean", "Remark": "冰弹命中是否附加冻伤debuff：0，否；1，是", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2132": {"Id": 2132, "StringId": "hero_2_1_hit_skill_attr_dot_frozen_chance", "Remark": "冰弹命中附加冻伤debuff概率", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2133": {"Id": 2133, "StringId": "hero_2_1_hit_skill_attr_dot_frozen_buff_type", "Remark": "冰弹命中附加冻伤debuff类型", "SkillEffectType": 10, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 27, "Effect": 0}, "2134": {"Id": 2134, "StringId": "hero_2_1_hit_skill_attr_dot_frozen_time", "Remark": "冰弹命中附件冻伤debuff时间，单位：秒,-1=一直", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 10, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2135": {"Id": 2135, "StringId": "hero_2_1_hit_skill_attr_division_frozen_boolean", "Remark": "小冰弹命中是否附加冻结debuff：0，否；1，是", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2136": {"Id": 2136, "StringId": "hero_2_1_hit_skill_attr_division_frozen_chance", "Remark": "小冰弹命中附加冻结debuff概率", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2137": {"Id": 2137, "StringId": "hero_2_1_hit_skill_attr_division_frozen_buff_type", "Remark": "小冰弹命中附加冻结debuff类型", "SkillEffectType": 10, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 5, "Effect": 0}, "2138": {"Id": 2138, "StringId": "hero_2_1_hit_skill_attr_division_frozen_time", "Remark": "小冰弹命中附件冻结debuff时间，单位：秒,-1=一直", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2139": {"Id": 2139, "StringId": "hero_2_1_hit_skill_attr_division_dot_frozen_boolean", "Remark": "小冰弹命中是否附加冻伤debuff：0，否；1，是", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2140": {"Id": 2140, "StringId": "hero_2_1_hit_skill_attr_division_dot_frozen_chance", "Remark": "小冰弹命中附加冻伤debuff概率", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2141": {"Id": 2141, "StringId": "hero_2_1_hit_skill_attr_division_dot_frozen_buff_type", "Remark": "小冰弹命中附加冻伤debuff类型", "SkillEffectType": 10, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 27, "Effect": 0}, "2142": {"Id": 2142, "StringId": "hero_2_1_hit_skill_attr_division_dot_frozen_time", "Remark": "小冰弹命中附件冻伤debuff时间，单位：秒,-1=一直", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 10, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2143": {"Id": 2143, "StringId": "hero_2_1_hit_skill_attr_parent_1_boolean", "Remark": "冰弹是否升级为极寒大冰弹（每次命中分裂出附带所有强化效果的冰弹)：0，否；1，是", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2144": {"Id": 2144, "StringId": "hero_2_1_hit_skill_attr_parent_1_dmg_ratio", "Remark": "极寒大冰弹伤害占当前技能系数比例", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1.8, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2145": {"Id": 2145, "StringId": "hero_2_1_hit_skill_attr_paralysis_dmg_add_boolean", "Remark": "寒冰弹对麻痹单位是否增伤：0，否；1，是", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2146": {"Id": 2146, "StringId": "hero_2_1_hit_skill_attr_paralysis_type", "Remark": "麻痹debuff类型", "SkillEffectType": 10, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 4, "Effect": 0}, "2147": {"Id": 2147, "StringId": "hero_2_1_hit_skill_attr_paralysis_dmg_add_ratio", "Remark": "寒冰弹对麻痹单位增伤比例", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2148": {"Id": 2148, "StringId": "hero_2_1_hit_skill_attr_turn_back_boolean", "Remark": "寒冰弹碰到墙壁后是否原路折返：0，否；1，是（1次）", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2149": {"Id": 2149, "StringId": "hero_2_1_hit_skill_attr_trigger_effect_boolean", "Remark": "寒冰弹首次命中怪物时是否触发1个无强化的冰暴发生器：0，否；1，是", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2150": {"Id": 2150, "StringId": "hero_2_1_hit_skill_bullet_long", "Remark": "冰弹模型长，单位：米（按长度等比缩放）", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2151": {"Id": 2151, "StringId": "hero_2_1_hit_skill_bullet_wide", "Remark": "冰弹模型宽，单位：米（实际不生效）", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2152": {"Id": 2152, "StringId": "hero_2_1_hit_skill_division_long", "Remark": "小冰弹模型长，单位：米（按长度等比缩放）", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2153": {"Id": 2153, "StringId": "hero_2_1_hit_skill_division_wide", "Remark": "小冰弹模型宽，单位：米（实际不生效）", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2154": {"Id": 2154, "StringId": "hero_2_1_hit_skill_parent_1_long", "Remark": "极寒大冰弹模型长，单位：米（按长度等比缩放）", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2155": {"Id": 2155, "StringId": "hero_2_1_hit_skill_parent_1_wide", "Remark": "极寒大冰弹模型宽，单位：米（实际不生效）", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2156": {"Id": 2156, "StringId": "hero_2_1_trigger_effect_1_radius", "Remark": "冰暴半径，单位：米", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2157": {"Id": 2157, "StringId": "hero_2_1_trigger_effect_2_radius", "Remark": "冰霜新星半径，单位：米", "SkillEffectType": 10, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2158": {"Id": 2158, "StringId": "hero_2_1_hit_skill_bullet_model", "Remark": "冰弹子弹模型", "SkillEffectType": 10, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2101, "BuffType": 0, "Effect": 0}, "2159": {"Id": 2159, "StringId": "hero_2_1_hit_skill_division_model", "Remark": "小冰弹子弹模型", "SkillEffectType": 10, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2105, "BuffType": 0, "Effect": 0}, "2160": {"Id": 2160, "StringId": "hero_2_1_hit_skill_parent_1_model", "Remark": "极寒大冰弹子弹模型", "SkillEffectType": 10, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2107, "BuffType": 0, "Effect": 0}, "2161": {"Id": 2161, "StringId": "hero_2_1_hit_skill_self_se", "Remark": "发射冰弹自身特效", "SkillEffectType": 10, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2108, "BuffType": 0, "Effect": 0}, "2162": {"Id": 2162, "StringId": "hero_2_1_hit_skill_affect_se", "Remark": "冰弹受击特效", "SkillEffectType": 10, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2109, "BuffType": 0, "Effect": 0}, "2163": {"Id": 2163, "StringId": "hero_2_1_hit_skill_frozen_dot_se", "Remark": "冰弹冻伤特效", "SkillEffectType": 10, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2110, "BuffType": 0, "Effect": 0}, "2164": {"Id": 2164, "StringId": "hero_2_1_hit_skill_frozen_se", "Remark": "冰弹冻结特效", "SkillEffectType": 10, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2111, "BuffType": 0, "Effect": 0}, "2201": {"Id": 2201, "StringId": "hero_2_2_hit_skill_attr_dmg_ratio", "Remark": "龙血剑基础伤害系数", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2202": {"Id": 2202, "StringId": "hero_2_2_hit_skill_attr_dmg_type", "Remark": "龙血剑基础伤害系数：风", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2203": {"Id": 2203, "StringId": "hero_2_2_hit_skill_attr_dmg_add_ratio", "Remark": "龙血剑基础伤害加法修正系数", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2204": {"Id": 2204, "StringId": "hero_2_2_hit_skill_attr_strike_dmg_add_ratio", "Remark": "龙血剑基础直击伤害加法修正系数", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2205": {"Id": 2205, "StringId": "hero_2_2_hit_skill_attr_rapidfire_cnt", "Remark": "龙血剑连发数量", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2206": {"Id": 2206, "StringId": "hero_2_2_hit_skill_attr_rapidfire_dmg_add_ratio", "Remark": "龙血剑连发间隔：单位，秒", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2207": {"Id": 2207, "StringId": "hero_2_2_hit_skill_attr_ballistic_cnt", "Remark": "龙血剑多发数量", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2208": {"Id": 2208, "StringId": "hero_2_2_hit_skill_attr_ballistic_dmg_add_ratio", "Remark": "龙血剑多发角度：单位，度", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 10, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2209": {"Id": 2209, "StringId": "hero_2_2_hit_skill_attr_fly_speed", "Remark": "龙血剑飞行速度：单位，米/秒", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 16, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2210": {"Id": 2210, "StringId": "hero_2_2_hit_skill_attr_fly_speed_add_ratio", "Remark": "龙血剑飞行速度加法修正系数", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2211": {"Id": 2211, "StringId": "hero_2_2_hit_skill_attr_fierce_cnt", "Remark": "龙血剑穿透数量", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2212": {"Id": 2212, "StringId": "hero_2_2_hit_skill_attr_hp_recovery_debuff_time", "Remark": "龙血剑攻击附带生命回复降低debuff时间", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2213": {"Id": 2213, "StringId": "hero_2_2_hit_skill_attr_hp_recovery_debuff_type", "Remark": "龙血剑攻击附带生命回复降低debuff类型", "SkillEffectType": 5, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 21, "Effect": 0}, "2214": {"Id": 2214, "StringId": "hero_2_2_hit_skill_attr_repel_buff", "Remark": "龙血剑击退buff", "SkillEffectType": 5, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 2, "Effect": 0}, "2215": {"Id": 2215, "StringId": "hero_2_2_hit_skill_attr_repel_distance", "Remark": "龙血剑击退距离：单位，米", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2216": {"Id": 2216, "StringId": "hero_2_2_hit_skill_attr_repel_time", "Remark": "龙血剑击退时间：单位，秒", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2217": {"Id": 2217, "StringId": "hero_2_2_hit_skill_attr_repel_distance_add_ratio", "Remark": "龙血剑击退距离加法修正系数", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2218": {"Id": 2218, "StringId": "hero_2_2_hit_skill_attr_wide", "Remark": "龙血剑气刃宽度：单位，米", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2219": {"Id": 2219, "StringId": "hero_2_2_hit_skill_attr_long", "Remark": "龙血剑气刃长度：单位，米", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2220": {"Id": 2220, "StringId": "hero_2_2_hit_skill_attr_wide_add_ratio", "Remark": "龙血剑气刃宽度加法修正系数", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2221": {"Id": 2221, "StringId": "hero_2_2_hit_skill_attr_long_add_ratio", "Remark": "龙血剑气刃长度加法修正系数", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2222": {"Id": 2222, "StringId": "hero_2_2_hit_skill_attr_turbulent_flow_boolean", "Remark": "龙血剑是否触发乱流：0，否；1，是（可以从屏幕任意角度发出）", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2223": {"Id": 2223, "StringId": "hero_2_2_hit_skill_attr_turbulent_flow_half_range", "Remark": "龙血剑乱流半场距离：单位，米", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 16, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2224": {"Id": 2224, "StringId": "hero_2_2_hit_skill_attr_turbulent_flow_half_dmg_add_ratio", "Remark": "龙血剑乱流半场后伤害加成系数", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2225": {"Id": 2225, "StringId": "hero_2_2_hit_skill_attr_turbulent_flow_half_fly_speed_add_ratio", "Remark": "龙血剑乱流半场后速度加成系数", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2226": {"Id": 2226, "StringId": "hero_2_2_hit_skill_attr_cyclone_boolean", "Remark": "龙血剑气刃命中的首个怪物是否形成1个小型旋风：0，否；1，是", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2227": {"Id": 2227, "StringId": "hero_2_2_hit_skill_attr_cyclone_dmg_ratio", "Remark": "小型旋风的伤害占当前伤害比例", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2228": {"Id": 2228, "StringId": "hero_2_2_hit_skill_attr_cyclone_radius", "Remark": "小型旋风的半径：单位，米", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.6, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2229": {"Id": 2229, "StringId": "hero_2_2_hit_skill_attr_cyclone_time", "Remark": "小型旋风的时间：单位，秒", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 3.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2230": {"Id": 2230, "StringId": "hero_2_2_hit_skill_attr_cyclone_dmg_interval", "Remark": "小型旋风的伤害间隔：单位，秒", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2231": {"Id": 2231, "StringId": "hero_2_2_hit_skill_attr_cyclone_pull_time", "Remark": "小型旋风的牵引速度：单位，米/秒", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2232": {"Id": 2232, "StringId": "hero_2_2_hit_skill_attr_disturbance_boolean", "Remark": "龙血剑每命中3个目标，是否在第3个目标处造成1次小范围风波伤害：0，否；1，是", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2233": {"Id": 2233, "StringId": "hero_2_2_hit_skill_attr_disturbance_dmg_ratio", "Remark": "风波伤害占基础伤害比例", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2234": {"Id": 2234, "StringId": "hero_2_2_hit_skill_attr_disturbance_radius", "Remark": "风波半径：单位，米", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2235": {"Id": 2235, "StringId": "hero_2_2_hit_skill_attr_disturbance_pull_buff_type", "Remark": "风波触发牵引buff类型", "SkillEffectType": 5, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 3, "Effect": 0}, "2236": {"Id": 2236, "StringId": "hero_2_2_hit_skill_attr_disturbance_pull_speed", "Remark": "风波牵引速度：单位，米/秒", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2237": {"Id": 2237, "StringId": "hero_2_2_hit_skill_attr_disturbance_pull_time", "Remark": "风波牵引时间：单位，秒", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2238": {"Id": 2238, "StringId": "hero_2_2_hit_skill_attr_disturbance_dmg_add_ratio", "Remark": "风波伤害加法修正系数", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2239": {"Id": 2239, "StringId": "hero_2_2_hit_skill_attr_disturbance_frozen_boolean", "Remark": "风波伤害是否附加冰冻debuff：0，否；1，是", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2240": {"Id": 2240, "StringId": "hero_2_2_hit_skill_attr_disturbance_frozen_debuff_type", "Remark": "风波伤害附加冰冻debuff类型", "SkillEffectType": 5, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 5, "Effect": 0}, "2241": {"Id": 2241, "StringId": "hero_2_2_hit_skill_attr_disturbance_frozen_debuff_time", "Remark": "风波伤害附加冰冻debuff时间：单位，秒", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2242": {"Id": 2242, "StringId": "hero_2_2_hit_skill_attr_frozen_debuff_boolean", "Remark": "龙血剑攻击是否会对怪物附加冻结debuff：0，否；1，是", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2243": {"Id": 2243, "StringId": "hero_2_2_hit_skill_attr_frozen_debuff_type", "Remark": "龙血剑攻击对怪物附加冻结debuff类型", "SkillEffectType": 5, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 5, "Effect": 0}, "2244": {"Id": 2244, "StringId": "hero_2_2_hit_skill_attr_frozen_debuff_chance", "Remark": "龙血剑攻击对怪物附加冻结debuff概率", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2245": {"Id": 2245, "StringId": "hero_2_2_hit_skill_attr_frozen_debuff_time", "Remark": "龙血剑攻击对怪物附加冻结debuff时间：单位，秒", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2246": {"Id": 2246, "StringId": "hero_2_2_hit_skill_attr_repel_add_to_frozen_boolean", "Remark": "龙血剑是否对冰冻怪物附加击退加成：0，否；1，是", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2247": {"Id": 2247, "StringId": "hero_2_2_hit_skill_attr_repel_add_ratio", "Remark": "龙血剑对冰冻怪物附加击退加成系数（伤害*（1+加成系数））", "SkillEffectType": 5, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2248": {"Id": 2248, "StringId": "hero_2_2_hit_skill_bullet_model", "Remark": "龙血剑子弹模型", "SkillEffectType": 5, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2202, "BuffType": 0, "Effect": 0}, "2249": {"Id": 2249, "StringId": "hero_2_2_hit_skill_self_se", "Remark": "龙血剑发射自身特效", "SkillEffectType": 5, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2201, "BuffType": 0, "Effect": 0}, "2250": {"Id": 2250, "StringId": "hero_2_2_hit_skill_cyclone_model", "Remark": "小型旋风模型", "SkillEffectType": 5, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2206, "BuffType": 0, "Effect": 0}, "2251": {"Id": 2251, "StringId": "hero_2_2_hit_skill_disturbance_model", "Remark": "风波模型", "SkillEffectType": 5, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2207, "BuffType": 0, "Effect": 0}, "2301": {"Id": 2301, "StringId": "hero_2_3_hit_skill_attr_dmg_ratio", "Remark": "番茄小子基础伤害系数", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2302": {"Id": 2302, "StringId": "hero_2_3_hit_skill_attr_dmg_type", "Remark": "番茄小子技能伤害类型：光", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2303": {"Id": 2303, "StringId": "hero_2_3_hit_skill_attr_dmg_add_ratio", "Remark": "番茄小子基础伤害加法修正系数", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2304": {"Id": 2304, "StringId": "hero_2_3_hit_skill_attr_strike_dmg_add_ratio", "Remark": "番茄小子基础直击伤害加法修正系数", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2305": {"Id": 2305, "StringId": "hero_2_3_hit_skill_attr_dmg_time", "Remark": "番茄小子伤害时间", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2306": {"Id": 2306, "StringId": "hero_2_3_hit_skill_attr_dmg_interval", "Remark": "番茄小子伤害间隔时间", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2307": {"Id": 2307, "StringId": "hero_2_3_hit_skill_attr_dmg_time_add_ratio", "Remark": "番茄小子伤害时间加法修正系数", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2308": {"Id": 2308, "StringId": "hero_2_3_hit_skill_attr_speed_down_debuff_boolean", "Remark": "番茄小子是否会造成怪物减速：0，否；1，是", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2309": {"Id": 2309, "StringId": "hero_2_3_hit_skill_attr_speed_down_debuff_type", "Remark": "番茄小子造成怪物减速debuff类型", "SkillEffectType": 6, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 14, "Effect": 0}, "2310": {"Id": 2310, "StringId": "hero_2_3_hit_skill_attr_speed_down_debuff_time", "Remark": "番茄小子造成怪物减速debuff时间，单位：秒", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2311": {"Id": 2311, "StringId": "hero_2_3_hit_skill_attr_dmg_add_debuff_boolean", "Remark": "番茄小子攻击的怪物是否获得易伤debuff：0，否；1，是", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2312": {"Id": 2312, "StringId": "hero_2_3_hit_skill_attr_dmg_add_debuff_type", "Remark": "番茄小子攻击的怪物获得易伤debuff类型", "SkillEffectType": 6, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 20, "Effect": 0}, "2313": {"Id": 2313, "StringId": "hero_2_3_hit_skill_attr_dmg_add_debuff_value", "Remark": "番茄小子攻击的怪物获得易伤debuff值", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.25, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2314": {"Id": 2314, "StringId": "hero_2_3_hit_skill_attr_dmg_add_debuff_time", "Remark": "番茄小子攻击的怪物获得易伤debuff时间，单位：秒", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2315": {"Id": 2315, "StringId": "hero_2_3_hit_skill_attr_wide", "Remark": "番茄小子激光宽度，单位：米", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2316": {"Id": 2316, "StringId": "hero_2_3_hit_skill_attr_mul_dmg_add_boolean", "Remark": "番茄小子激光是否对同一目标造成伤害逐渐增加：0，否；1，是", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2317": {"Id": 2317, "StringId": "hero_2_3_hit_skill_attr_mul_dmg_add_ratio", "Remark": "番茄小子激光对同一目标每次伤害增加的比例", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2318": {"Id": 2318, "StringId": "hero_2_3_hit_skill_attr_light_line_boolean", "Remark": "番茄小子释放激光时是否会持续释放无强化光线：0，否；1，是", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2319": {"Id": 2319, "StringId": "hero_2_3_hit_skill_attr_light_line_interval", "Remark": "番茄小子释放无强化光线的间隔：单位，秒", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2320": {"Id": 2320, "StringId": "hero_2_3_hit_skill_attr_light_line_cnt", "Remark": "番茄小子释放激光时持续释放无强化光线数量", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2321": {"Id": 2321, "StringId": "hero_2_3_hit_skill_attr_light_line_wide", "Remark": "番茄小子释放激光时持续释放无强化光线宽度，单位：米", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.15, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2322": {"Id": 2322, "StringId": "hero_2_3_hit_skill_attr_light_line_dmg_ratio", "Remark": "无强化光线伤害占基础伤害比例（不受强化影响）", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2323": {"Id": 2323, "StringId": "hero_2_3_hit_skill_attr_strafe_boolean", "Remark": "番茄小子激光攻击方式是否变为扫荡：0，否；1，是", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2324": {"Id": 2324, "StringId": "hero_2_3_hit_skill_attr_strafe_init_angle", "Remark": "番茄小子扫荡激光初始角度（相对于中线）", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 25, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2325": {"Id": 2325, "StringId": "hero_2_3_hit_skill_attr_strafe_end_angle", "Remark": "番茄小子扫荡激光结束角度（相对于中线）", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": -25, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2326": {"Id": 2326, "StringId": "hero_2_3_hit_skill_attr_strafe_back_boolean", "Remark": "番茄小子激光扫荡是否折返：0，否；1，是", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2327": {"Id": 2327, "StringId": "hero_2_3_hit_skill_attr_kill_division_boolean", "Remark": "番茄小子激光击杀怪物时是否释放一束折射激光：0，否；1，是", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2328": {"Id": 2328, "StringId": "hero_2_3_hit_skill_attr_kill_division_dmg_ratio", "Remark": "番茄小子激光击杀怪物时释放折射激光的伤害占当前伤害比例", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2329": {"Id": 2329, "StringId": "hero_2_3_hit_skill_attr_kill_division_wide", "Remark": "番茄小子激光击杀怪物时释放折射激光的宽度", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.15, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2330": {"Id": 2330, "StringId": "hero_2_3_hit_skill_attr_end_extra_dmg_boolean", "Remark": "番茄小子激光结束时是否追加1次高额伤害：0，否；1，是", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2331": {"Id": 2331, "StringId": "hero_2_3_hit_skill_attr_end_extra_dmg_ratio", "Remark": "番茄小子激光结束时追加的高额伤害占当当前伤害比例", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2332": {"Id": 2332, "StringId": "hero_2_3_hit_skill_attr_light_wave_boolean", "Remark": "番茄小子激光是否升级为高能光波：0，否；1，是", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2333": {"Id": 2333, "StringId": "hero_2_3_hit_skill_attr_light_wave_wide_add_ratio", "Remark": "番茄小子高能光波宽度加法修正系数（当前宽度*（1+加法修正））", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2334": {"Id": 2334, "StringId": "hero_2_3_hit_skill_attr_light_wave_dmg_add_ratio", "Remark": "番茄小子高能光波伤害加法修正系数（当前伤害*（1+加法修正））", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2335": {"Id": 2335, "StringId": "hero_2_3_hit_skill_attr_light_wave_repel_boolean", "Remark": "番茄小子高能光波每次造成伤害时是否击退：0，否；1，是", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2336": {"Id": 2336, "StringId": "hero_2_3_hit_skill_attr_light_wave_repel_buff", "Remark": "番茄小子高能光波每次造成伤害时击退buff", "SkillEffectType": 6, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 2, "Effect": 0}, "2337": {"Id": 2337, "StringId": "hero_2_3_hit_skill_attr_light_wave_repel_range", "Remark": "番茄小子高能光波每次造成伤害时击退距离，单位：米", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.25, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2338": {"Id": 2338, "StringId": "hero_2_3_hit_skill_attr_light_wave_repel_time", "Remark": "番茄小子高能光波每次造成伤害时击退时间，单位：秒", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2339": {"Id": 2339, "StringId": "hero_2_3_hit_skill_attr_kill_no_division_boolean", "Remark": "番茄小子的激光杀死怪物后是否禁止分裂：0，否；1，是", "SkillEffectType": 6, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2340": {"Id": 2340, "StringId": "hero_2_3_hit_skill_model", "Remark": "番茄小子的激光模型", "SkillEffectType": 6, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2302, "BuffType": 0, "Effect": 0}, "2341": {"Id": 2341, "StringId": "hero_2_3_hit_skill_self_se", "Remark": "番茄小子释放技能时的自身特效", "SkillEffectType": 6, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2301, "BuffType": 0, "Effect": 0}, "2342": {"Id": 2342, "StringId": "hero_2_3_hit_skill_light_line_model", "Remark": "番茄小子无强化光线模型", "SkillEffectType": 6, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2303, "BuffType": 0, "Effect": 0}, "2343": {"Id": 2343, "StringId": "hero_2_3_hit_skill_division_model", "Remark": "番茄小子折射激光模型", "SkillEffectType": 6, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2304, "BuffType": 0, "Effect": 0}, "2344": {"Id": 2344, "StringId": "hero_2_3_hit_skill_end_light_model", "Remark": "番茄小子激光结束时追加激光模型", "SkillEffectType": 6, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2305, "BuffType": 0, "Effect": 0}, "2345": {"Id": 2345, "StringId": "hero_2_3_hit_skill_light_wave_model", "Remark": "番茄小子动感光波模型", "SkillEffectType": 6, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2307, "BuffType": 0, "Effect": 0}, "2401": {"Id": 2401, "StringId": "hero_2_4_hit_skill_attr_dmg_ratio", "Remark": "闪电莓基础伤害系数", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2402": {"Id": 2402, "StringId": "hero_2_4_hit_skill_attr_dmg_type", "Remark": "闪电莓技能伤害类型：电", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2403": {"Id": 2403, "StringId": "hero_2_4_hit_skill_attr_dmg_add_ratio", "Remark": "闪电莓基础伤害加法修正系数", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2404": {"Id": 2404, "StringId": "hero_2_4_hit_skill_attr_strike_dmg_ratio", "Remark": "闪电莓直击伤害占当前伤害比例", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2405": {"Id": 2405, "StringId": "hero_2_4_hit_skill_attr_strike_dmg_add_ratio", "Remark": "闪电莓直击伤害加法修正系数", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2406": {"Id": 2406, "StringId": "hero_2_4_hit_skill_attr_aoe_dmg_ratio", "Remark": "闪电莓溅射伤害占当前伤害比例", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2407": {"Id": 2407, "StringId": "hero_2_4_hit_skill_attr_aoe_dmg_add_ratio", "Remark": "闪电莓溅射伤害加法修正系数", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2408": {"Id": 2408, "StringId": "hero_2_4_hit_skill_attr_aoe_dmg_radius", "Remark": "闪电莓溅射伤害半径，单位：米", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2409": {"Id": 2409, "StringId": "hero_2_4_hit_skill_attr_aoe_dmg_radius_add_ratio", "Remark": "闪电莓溅射伤害半径加法修正系数", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2410": {"Id": 2410, "StringId": "hero_2_4_hit_skill_attr_explode_boolean", "Remark": "闪电莓命中怪物是否爆炸：0，否；1，是", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2411": {"Id": 2411, "StringId": "hero_2_4_hit_skill_attr_explode_dmg_ratio", "Remark": "闪电莓爆炸伤害占当前技能系数比例", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2412": {"Id": 2412, "StringId": "hero_2_4_hit_skill_attr_explode_dmg_add_ratio", "Remark": "闪电莓爆炸伤害加法修正系数", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2413": {"Id": 2413, "StringId": "hero_2_4_hit_skill_attr_explode_radius", "Remark": "闪电莓爆炸半径", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.8, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2414": {"Id": 2414, "StringId": "hero_2_4_hit_skill_attr_explode_radius_add_ratio", "Remark": "闪电莓爆炸半径加法修正系数", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2415": {"Id": 2415, "StringId": "hero_2_4_hit_skill_attr_dmg_paralysis_type", "Remark": "闪电莓直击伤害附加的麻痹类型", "SkillEffectType": 7, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 4, "Effect": 0}, "2416": {"Id": 2416, "StringId": "hero_2_4_hit_skill_attr_dmg_paralysis_time", "Remark": "闪电莓直击伤害附加的麻痹时间，单位：秒", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2417": {"Id": 2417, "StringId": "hero_2_4_hit_skill_attr_rapidfire_cnt", "Remark": "闪电莓连发次数", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2418": {"Id": 2418, "StringId": "hero_2_4_hit_skill_attr_rapidfire_interval", "Remark": "闪电莓连发间隔，单位：秒", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2419": {"Id": 2419, "StringId": "hero_2_4_hit_skill_attr_rapidfire_dmg_add_ratio", "Remark": "闪电莓连发伤害加法修正系数", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2420": {"Id": 2420, "StringId": "hero_2_4_hit_skill_attr_kill_rapidfire_boolean", "Remark": "闪电莓直击杀死怪物后是否触发小范围连发：0，否；1，是", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2421": {"Id": 2421, "StringId": "hero_2_4_hit_skill_attr_kill_rapidfire_radius", "Remark": "闪电莓直击杀死怪物后触发连发的小范围半径，单位：米", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 30, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2422": {"Id": 2422, "StringId": "hero_2_4_hit_skill_attr_dmg_aoe_extra_cnt", "Remark": "闪电莓溅射范围伤害额外释放次数", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2423": {"Id": 2423, "StringId": "hero_2_4_hit_skill_attr_initial_rapidfire_boolean", "Remark": "闪电莓直击命中怪物时，是否概率性在小范围内释放1次无强化的雷击：0，否；1，是", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2424": {"Id": 2424, "StringId": "hero_2_4_hit_skill_attr_initial_rapidfire_chance", "Remark": "闪电莓直击命中怪物时，触发1次无强化小范围雷击概率", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2425": {"Id": 2425, "StringId": "hero_2_4_hit_skill_attr_initial_rapidfire_radius", "Remark": "闪电莓直击命中怪物后，释放1次无强化雷击的小范围半径，单位：米", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 30, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2426": {"Id": 2426, "StringId": "hero_2_4_hit_skill_attr_division_boolean", "Remark": "闪电莓直击命中后是否释放小电球：0，否；1，是", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2427": {"Id": 2427, "StringId": "hero_2_4_hit_skill_attr_division_radius", "Remark": "小电球半径，单位：米", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2428": {"Id": 2428, "StringId": "hero_2_4_hit_skill_attr_division_fly_speed", "Remark": "小电球飞行速度，单位：米/秒", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 20, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2429": {"Id": 2429, "StringId": "hero_2_4_hit_skill_attr_division_fly_range", "Remark": "小电球最大飞行距离，单位：米", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 40, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2430": {"Id": 2430, "StringId": "hero_2_4_hit_skill_attr_division_cnt", "Remark": "闪电莓直击命中后分裂小电球数量", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 6, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2431": {"Id": 2431, "StringId": "hero_2_4_hit_skill_attr_division_dmg_ratio", "Remark": "小电球伤害占当前技能系数比例", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2432": {"Id": 2432, "StringId": "hero_2_4_hit_skill_attr_division_dmg_add_ratio", "Remark": "小电球伤害加法修正系数", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2433": {"Id": 2433, "StringId": "hero_2_4_hit_skill_attr_division_dmg_mul_ratio", "Remark": "小电球伤害乘法修正系数", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 2, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2434": {"Id": 2434, "StringId": "hero_2_4_hit_skill_attr_division_angle", "Remark": "闪电莓直击命中后小电球分裂角度，单位：度（角度均分）", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 60, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2435": {"Id": 2435, "StringId": "hero_2_4_hit_skill_attr_division_fierce_cnt", "Remark": "小电球穿透数量", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2436": {"Id": 2436, "StringId": "hero_2_4_hit_skill_attr_division_paralysis_boolean", "Remark": "小电球命中后是否麻痹：0，否；1，是", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2437": {"Id": 2437, "StringId": "hero_2_4_hit_skill_attr_division_paralysis_time", "Remark": "小电球命中后麻痹时间，单位：秒", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2438": {"Id": 2438, "StringId": "hero_2_4_hit_skill_attr_matrix_boolean", "Remark": "闪电莓溅射范围伤害是否触发矩阵：0，否；1，是", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2439": {"Id": 2439, "StringId": "hero_2_4_hit_skill_attr_matrix_dmg_ratio", "Remark": "闪电莓矩阵伤害占当前伤害比例", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2440": {"Id": 2440, "StringId": "hero_2_4_hit_skill_attr_matrix_dmg_interval", "Remark": "闪电莓矩阵伤害间隔，单位：秒", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2441": {"Id": 2441, "StringId": "hero_2_4_hit_skill_attr_matrix_dmg_time", "Remark": "闪电莓矩阵持续时间，单位：秒", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2442": {"Id": 2442, "StringId": "hero_2_4_hit_skill_attr_matrix_dmg_add_ratio", "Remark": "闪电莓矩阵伤害加法修正系数", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2443": {"Id": 2443, "StringId": "hero_2_4_hit_skill_attr_matrix_buff", "Remark": "闪电莓矩阵内的怪物触发减速buff", "SkillEffectType": 7, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 10, "Effect": 0}, "2444": {"Id": 2444, "StringId": "hero_2_4_hit_skill_attr_frozen_division_boolean", "Remark": "闪电莓直击命中冰冻怪物时，是否打破冰冻并散发4颗冰冻子弹：0，否；1，是（自动索敌）", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2445": {"Id": 2445, "StringId": "hero_2_4_hit_skill_attr_frozen_division_long", "Remark": "分裂小冰弹长，单位：米", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2446": {"Id": 2446, "StringId": "hero_2_4_hit_skill_attr_frozen_division_wide", "Remark": "分裂小冰弹宽，单位：米", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2447": {"Id": 2447, "StringId": "hero_2_4_hit_skill_attr_frozen_division_fly_speed", "Remark": "分裂小冰弹飞行速度，单位：米/秒", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 20, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2448": {"Id": 2448, "StringId": "hero_2_4_hit_skill_attr_frozen_division_fly_range", "Remark": "分裂小冰弹最大飞行距离，单位：米", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 40, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2449": {"Id": 2449, "StringId": "hero_2_4_hit_skill_attr_frozen_division_cnt", "Remark": "分裂小冰弹数量", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2450": {"Id": 2450, "StringId": "hero_2_4_hit_skill_attr_frozen_division_dmg_ratio", "Remark": "分裂小冰弹伤害占当前技能系数比例", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2451": {"Id": 2451, "StringId": "hero_2_4_hit_skill_attr_frozen_division_dmg_add_ratio", "Remark": "分裂小冰弹伤害加法修正系数", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2452": {"Id": 2452, "StringId": "hero_2_4_hit_skill_attr_frozen_division_dmg_mul_ratio", "Remark": "分裂小冰弹伤害乘法修正系数", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 2, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2453": {"Id": 2453, "StringId": "hero_2_4_hit_skill_frozen_division_model", "Remark": "分裂小冰弹模型", "SkillEffectType": 7, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2105, "BuffType": 0, "Effect": 0}, "2454": {"Id": 2454, "StringId": "hero_2_4_hit_skill_model", "Remark": "闪电莓直击模型", "SkillEffectType": 7, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2402, "BuffType": 0, "Effect": 0}, "2455": {"Id": 2455, "StringId": "hero_2_4_hit_skill_division_model", "Remark": "小电球模型", "SkillEffectType": 7, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2406, "BuffType": 0, "Effect": 0}, "2456": {"Id": 2456, "StringId": "hero_2_4_hit_skill_matrix_model", "Remark": "闪电莓矩阵模型", "SkillEffectType": 7, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2407, "BuffType": 0, "Effect": 0}, "2457": {"Id": 2457, "StringId": "hero_2_4_hit_skill_self_se", "Remark": "闪电莓发射自身特效", "SkillEffectType": 7, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2401, "BuffType": 0, "Effect": 0}, "2458": {"Id": 2458, "StringId": "hero_2_4_hit_skill_explode_model", "Remark": "闪电莓电击爆炸模型", "SkillEffectType": 7, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2411, "BuffType": 0, "Effect": 0}, "2459": {"Id": 2459, "StringId": "hero_2_4_hit_skill_nuclear_model", "Remark": "核电莓直击模型", "SkillEffectType": 7, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2408, "BuffType": 0, "Effect": 0}, "2460": {"Id": 2460, "StringId": "hero_2_4_hit_skill_nuclear_matrix_model", "Remark": "核电莓矩阵模型", "SkillEffectType": 7, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2409, "BuffType": 0, "Effect": 0}, "2461": {"Id": 2461, "StringId": "hero_2_4_hit_skill_nuclear_division_model", "Remark": "核电莓小电球模型", "SkillEffectType": 7, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2410, "BuffType": 0, "Effect": 0}, "2462": {"Id": 2462, "StringId": "hero_2_4_hit_skill_nuclear_boolean", "Remark": "是否开启核电：0，否；1，是", "SkillEffectType": 7, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2501": {"Id": 2501, "StringId": "hero_2_5_hit_skill_attr_dmg_ratio", "Remark": "火龙果基础伤害系数", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2502": {"Id": 2502, "StringId": "hero_2_5_hit_skill_attr_dmg_type", "Remark": "火龙果技能伤害类型：火", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2503": {"Id": 2503, "StringId": "hero_2_5_hit_skill_attr_dmg_add_ratio", "Remark": "火龙果基础伤害加法修正系数", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2504": {"Id": 2504, "StringId": "hero_2_5_hit_skill_attr_fly_speed", "Remark": "火箭飞行速度,单位:米/秒", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 30, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2505": {"Id": 2505, "StringId": "hero_2_5_hit_skill_attr_rapidfire_cnt", "Remark": "火龙果连发次数", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2506": {"Id": 2506, "StringId": "hero_2_5_hit_skill_attr_rapidfire_interval", "Remark": "火龙果连发间隔：单位，秒", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2507": {"Id": 2507, "StringId": "hero_2_5_hit_skill_attr_radius", "Remark": "火龙果灼烧半径：单位，米", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2508": {"Id": 2508, "StringId": "hero_2_5_hit_skill_attr_radius_add_ratio", "Remark": "火龙果灼烧半径加法修正系数", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2509": {"Id": 2509, "StringId": "hero_2_5_hit_skill_attr_time", "Remark": "火龙果灼烧伤害持续时间：单位，秒", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2510": {"Id": 2510, "StringId": "hero_2_5_hit_skill_attr_burn_dmg_ratio", "Remark": "火龙果灼烧伤害占当前伤害系数比例", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2511": {"Id": 2511, "StringId": "hero_2_5_hit_skill_attr_burn_dmg_add_ratio", "Remark": "火龙果灼烧伤害加法修正系数", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2512": {"Id": 2512, "StringId": "hero_2_5_hit_skill_attr_burn_interval", "Remark": "火龙果灼烧伤害间隔：单位，秒", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2513": {"Id": 2513, "StringId": "hero_2_5_hit_skill_attr_burn_extar_dmg_boolean", "Remark": "火龙果灼烧是否附加最大生命值伤害：0，否；1，是", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2514": {"Id": 2514, "StringId": "hero_2_5_hit_skill_attr_burn_extar_dmg", "Remark": "火龙果每次灼烧附加的最大生命值伤害系数", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.01, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2515": {"Id": 2515, "StringId": "hero_2_5_hit_skill_attr_light_time", "Remark": "火龙果每次灼烧时点燃怪物的时间", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2516": {"Id": 2516, "StringId": "hero_2_5_hit_skill_attr_light_debuff_type", "Remark": "火龙果每次灼烧时点燃怪物的debuff类型", "SkillEffectType": 11, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 39, "Effect": 0}, "2517": {"Id": 2517, "StringId": "hero_2_5_hit_skill_attr_light_dmg_add_ratio", "Remark": "火龙果点燃伤害加法修正系数", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2518": {"Id": 2518, "StringId": "hero_2_5_hit_skill_attr_light_debuff_time_permanent_boolean", "Remark": "火龙果附加的点燃debuff是否持续至目标死亡：0，否；1，是", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2519": {"Id": 2519, "StringId": "hero_2_5_hit_skill_attr_light_death_division_boolean", "Remark": "火龙果点燃的目标死亡时爆炸向四角固定溅射火焰：0，否；1，是", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2520": {"Id": 2520, "StringId": "hero_2_5_hit_skill_attr_light_death_division_dmg_ratio", "Remark": "溅射火焰伤害占基础伤害比例", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2521": {"Id": 2521, "StringId": "hero_2_5_hit_skill_attr_light_death_division_radius", "Remark": "溅射火焰子弹半径：单位，米", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2522": {"Id": 2522, "StringId": "hero_2_5_hit_skill_attr_light_death_division_fly_speed", "Remark": "溅射火焰飞行速度：单位，米/秒", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 10, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2523": {"Id": 2523, "StringId": "hero_2_5_hit_skill_attr_light_death_division_fly_range", "Remark": "溅射火焰最大飞行距离：单位，米", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 40, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2524": {"Id": 2524, "StringId": "hero_2_5_hit_skill_attr_explode_boolean", "Remark": "火龙果结束时是否在区域内造成一次爆炸：0，否；1，是", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2525": {"Id": 2525, "StringId": "hero_2_5_hit_skill_attr_explode_dmg_ratio", "Remark": "火龙果结束时爆炸伤害占当前伤害比例", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2526": {"Id": 2526, "StringId": "hero_2_5_hit_skill_attr_speed_down_debuff_boolean", "Remark": "火龙果每次造成伤害时附加减速debuff：0，否；1，是", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2527": {"Id": 2527, "StringId": "hero_2_5_hit_skill_attr_speed_down_debuff_type", "Remark": "火龙果每次造成伤害时附加减速debuff类型", "SkillEffectType": 11, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 11, "Effect": 0}, "2528": {"Id": 2528, "StringId": "hero_2_5_hit_skill_attr_speed_down_debuff_time", "Remark": "火龙果每次造成伤害时附加减速debuff时间：单位，秒", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.55, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2529": {"Id": 2529, "StringId": "hero_2_5_hit_skill_attr_light_death_burn_boolean", "Remark": "火龙果点燃的怪物死亡时会留下一片无强化的火焰灼烧区域：0，否；1，是", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2530": {"Id": 2530, "StringId": "hero_2_5_hit_skill_attr_light_death_burn_dmg_ratio", "Remark": "无强化的火焰灼烧伤害占无强化的基础伤害比例（强化前）", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2531": {"Id": 2531, "StringId": "hero_2_5_hit_skill_attr_light_death_burn_radius", "Remark": "无强化的火焰灼烧区域半径：单位，米", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2532": {"Id": 2532, "StringId": "hero_2_5_hit_skill_attr_light_death_burn_time", "Remark": "无强化的火焰灼烧时间：单位，秒", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2533": {"Id": 2533, "StringId": "hero_2_5_hit_skill_attr_light_death_burn_interval", "Remark": "无强化的火焰灼烧间隔：单位，秒", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.55, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2534": {"Id": 2534, "StringId": "hero_2_5_hit_skill_attr_light_dmg_up_debuff_boolean", "Remark": "每次点燃敌人同时赋予易伤debuff：0，否；1，是", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2535": {"Id": 2535, "StringId": "hero_2_5_hit_skill_attr_light_dmg_up_debuff_type", "Remark": "每次点燃敌人同时赋予易伤debuff类型", "SkillEffectType": 11, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 20, "Effect": 0}, "2536": {"Id": 2536, "StringId": "hero_2_5_hit_skill_attr_light_dmg_up_debuff_value", "Remark": "每次点燃敌人同时赋予易伤debuff值", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2537": {"Id": 2537, "StringId": "hero_2_5_hit_skill_attr_light_dmg_up_debuff_time", "Remark": "每次点燃敌人同时赋予易伤debuff时间，单位：秒", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.55, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2538": {"Id": 2538, "StringId": "hero_2_5_hit_model", "Remark": "火龙果子弹模型", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2502, "BuffType": 0, "Effect": 0}, "2539": {"Id": 2539, "StringId": "hero_2_5_hit_self_se", "Remark": "火龙果发射自身特效", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2501, "BuffType": 0, "Effect": 0}, "2540": {"Id": 2540, "StringId": "hero_2_5_hit_light_se", "Remark": "火龙果点燃特效", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2505, "BuffType": 0, "Effect": 0}, "2541": {"Id": 2541, "StringId": "hero_2_5_hit_fire_model", "Remark": "火龙果燃烧区域模型", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2506, "BuffType": 0, "Effect": 0}, "2542": {"Id": 2542, "StringId": "hero_2_5_hit_division_model", "Remark": "溅射火焰模型", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2509, "BuffType": 0, "Effect": 0}, "2543": {"Id": 2543, "StringId": "hero_2_5_hit_light_death_model", "Remark": "无强化灼烧模型", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2510, "BuffType": 0, "Effect": 0}, "2544": {"Id": 2544, "StringId": "hero_2_5_hit_end_explode_model", "Remark": "燃油弹结束爆炸模型", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2511, "BuffType": 0, "Effect": 0}, "2545": {"Id": 2545, "StringId": "hero_2_5_hit_sulfur_fire_model", "Remark": "硫化火焰燃烧区域模型", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2513, "BuffType": 0, "Effect": 0}, "2546": {"Id": 2546, "StringId": "hero_2_5_hit_attr_sulfur_fire_boolean", "Remark": "火龙果灼烧区域是否变成硫化火焰", "SkillEffectType": 11, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2547": {"Id": 2547, "StringId": "hero_2_5_hit_slow_fire_model", "Remark": "龙焰凝滞燃烧区域模型", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2512, "BuffType": 0, "Effect": 0}, "2548": {"Id": 2548, "StringId": "hero_2_5_hit_fire_start_model", "Remark": "火龙果燃烧区域起始模型", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2514, "BuffType": 0, "Effect": 0}, "2549": {"Id": 2549, "StringId": "hero_2_5_hit_fire_loop_model", "Remark": "火龙果燃烧区域循环模型", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2515, "BuffType": 0, "Effect": 0}, "2550": {"Id": 2550, "StringId": "hero_2_5_hit_fire_end_model", "Remark": "火龙果燃烧区域结束模型", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2516, "BuffType": 0, "Effect": 0}, "2551": {"Id": 2551, "StringId": "hero_2_5_hit_slow_fire_start_model", "Remark": "龙焰凝滞燃烧区域起始模型", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2523, "BuffType": 0, "Effect": 0}, "2552": {"Id": 2552, "StringId": "hero_2_5_hit_slow_fire_loop_model", "Remark": "龙焰凝滞燃烧区域循环模型", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2524, "BuffType": 0, "Effect": 0}, "2553": {"Id": 2553, "StringId": "hero_2_5_hit_slow_fire_end_model", "Remark": "龙焰凝滞燃烧区域结束模型", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2525, "BuffType": 0, "Effect": 0}, "2554": {"Id": 2554, "StringId": "hero_2_5_hit_sulfur_fire_start_model", "Remark": "硫化火焰燃烧区域起始模型", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2526, "BuffType": 0, "Effect": 0}, "2555": {"Id": 2555, "StringId": "hero_2_5_hit_sulfur_fire_loop_model", "Remark": "硫化火焰燃烧区域循环模型", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2527, "BuffType": 0, "Effect": 0}, "2556": {"Id": 2556, "StringId": "hero_2_5_hit_sulfur_fire_end_model", "Remark": "硫化火焰燃烧区域结束模型", "SkillEffectType": 11, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2528, "BuffType": 0, "Effect": 0}, "2601": {"Id": 2601, "StringId": "hero_2_6_hit_skill_attr_dmg_ratio", "Remark": "遥控车基础伤害系数", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2602": {"Id": 2602, "StringId": "hero_2_6_hit_skill_attr_dmg_type", "Remark": "遥控车技能伤害类型：物理", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 6, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2603": {"Id": 2603, "StringId": "hero_2_6_hit_skill_attr_dmg_add_ratio", "Remark": "遥控车基础伤害加法修正系数", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2604": {"Id": 2604, "StringId": "hero_2_6_hit_skill_attr_strike_dmg_add_ratio", "Remark": "遥控车基础直击伤害加法修正系数", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2605": {"Id": 2605, "StringId": "hero_2_6_hit_skill_attr_dmg_interval", "Remark": "遥控车伤害间隔，单位：秒", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2606": {"Id": 2606, "StringId": "hero_2_6_hit_skill_attr_rapidfire_cnt", "Remark": "遥控车连发数量", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2607": {"Id": 2607, "StringId": "hero_2_6_hit_skill_attr_rapidfire_interval", "Remark": "遥控车发射间隔，单位：秒", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.8, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2608": {"Id": 2608, "StringId": "hero_2_6_hit_skill_attr_move_speed", "Remark": "遥控车移动速度，单位：米/秒", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2609": {"Id": 2609, "StringId": "hero_2_6_hit_skill_attr_move_speed_add_ratio", "Remark": "遥控车移动速度加法修正系数", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2610": {"Id": 2610, "StringId": "hero_2_6_hit_skill_attr_long", "Remark": "遥控车长，单位:米", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2611": {"Id": 2611, "StringId": "hero_2_6_hit_skill_attr_wide", "Remark": "遥控车宽，单位:米", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2612": {"Id": 2612, "StringId": "hero_2_6_hit_skill_attr_long_add_ratio", "Remark": "遥控车长加法修正系数", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2613": {"Id": 2613, "StringId": "hero_2_6_hit_skill_attr_wide_add_ratio", "Remark": "遥控车宽加法修正系数", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2614": {"Id": 2614, "StringId": "hero_2_6_hit_skill_attr_speed_down_buff", "Remark": "遥控车击中怪物的减速buff", "SkillEffectType": 12, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 8, "Effect": 0}, "2615": {"Id": 2615, "StringId": "hero_2_6_hit_skill_attr_speed_down_time", "Remark": "遥控车击中怪物的减速时间，单位：秒", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2616": {"Id": 2616, "StringId": "hero_2_6_hit_skill_attr_repel_buff", "Remark": "遥控车每次伤害时击退buff", "SkillEffectType": 12, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 2, "Effect": 0}, "2617": {"Id": 2617, "StringId": "hero_2_6_hit_skill_attr_repel_distance", "Remark": "遥控车每次伤害时击退距离，单位：米", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2618": {"Id": 2618, "StringId": "hero_2_6_hit_skill_attr_repel_time", "Remark": "遥控车每次伤害时击退时间，单位：秒", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2619": {"Id": 2619, "StringId": "hero_2_6_hit_skill_attr_repel_distance_add_ratio", "Remark": "遥控车每次伤害时击退距离加法修正系数", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2620": {"Id": 2620, "StringId": "hero_2_6_hit_skill_attr_stun_boolean", "Remark": "遥控车命中时是否触发眩晕：0，否；1，是", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2621": {"Id": 2621, "StringId": "hero_2_6_hit_skill_attr_stun_chance", "Remark": "遥控车命中时触发眩晕概率", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2622": {"Id": 2622, "StringId": "hero_2_6_hit_skill_attr_stun_buff_type", "Remark": "遥控车命中时触发眩晕buff类型", "SkillEffectType": 12, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 6, "Effect": 0}, "2623": {"Id": 2623, "StringId": "hero_2_6_hit_skill_attr_stun_buff_time", "Remark": "遥控车命中时触发眩晕buff时间，单位：秒", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2624": {"Id": 2624, "StringId": "hero_2_6_hit_skill_attr_debuff_boolean", "Remark": "遥控车命中的怪物是否在6秒内受到的伤害+20%：0，否；1，是", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2625": {"Id": 2625, "StringId": "hero_2_6_hit_skill_attr_debuff_type", "Remark": "遥控车命中的怪物触发debuff类型", "SkillEffectType": 12, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 20, "Effect": 0}, "2626": {"Id": 2626, "StringId": "hero_2_6_hit_skill_attr_debuff_value", "Remark": "遥控车命中的怪物触发debuff值", "SkillEffectType": 12, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2627": {"Id": 2627, "StringId": "hero_2_6_hit_skill_attr_debuff_time", "Remark": "遥控车命中的怪物触发debuff时间，单位：秒", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 6, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2628": {"Id": 2628, "StringId": "hero_2_6_hit_skill_attr_stun_2_boolean", "Remark": "遥控车命中时是否触发眩晕2：0，否；1，是", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2629": {"Id": 2629, "StringId": "hero_2_6_hit_skill_attr_stun_2_chance", "Remark": "遥控车命中时触发眩晕2概率", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.05, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2630": {"Id": 2630, "StringId": "hero_2_6_hit_skill_attr_stun_2_buff_type", "Remark": "遥控车命中时触发眩晕2buff类型", "SkillEffectType": 12, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 6, "Effect": 0}, "2631": {"Id": 2631, "StringId": "hero_2_6_hit_skill_attr_stun_2_buff_time", "Remark": "遥控车命中时触发眩晕2buff时间，单位：秒", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2632": {"Id": 2632, "StringId": "hero_2_6_hit_skill_attr_dot_fire_boolean", "Remark": "遥控车命中时是否触发引燃：0，否；1，是", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2633": {"Id": 2633, "StringId": "hero_2_6_hit_skill_attr_dot_fire_buff_type", "Remark": "遥控车命中时触发引燃类型", "SkillEffectType": 12, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 36, "Effect": 0}, "2634": {"Id": 2634, "StringId": "hero_2_6_hit_skill_attr_dot_fire_buff_time", "Remark": "遥控车命中时触发引燃时间，单位：秒", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2635": {"Id": 2635, "StringId": "hero_2_6_hit_skill_attr_dot_fire_extra_dmg_boolean", "Remark": "遥控车命中时是否额外追加目标最大生命值3%的伤害：0，否；1，是", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2636": {"Id": 2636, "StringId": "hero_2_6_hit_skill_attr_dot_fire_extra_dmg", "Remark": "遥控车命中时额外追加目标最大生命值3%的伤害", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.03, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2637": {"Id": 2637, "StringId": "hero_2_6_hit_skill_attr_explode_boolean", "Remark": "遥控车命中首个怪物时是否产生小范围爆炸：0，否；1，是", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2638": {"Id": 2638, "StringId": "hero_2_6_hit_skill_attr_explode_radius", "Remark": "遥控车命中首个怪物时是产生小范围爆炸半径，单位：米", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2639": {"Id": 2639, "StringId": "hero_2_6_hit_skill_attr_explode_dmg_ratio", "Remark": "遥控车命中首个怪物时小范围伤害占当前伤害系数的比例", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2640": {"Id": 2640, "StringId": "hero_2_6_hit_skill_attr_frozen_dmg_add_boolean", "Remark": "遥控车对冰冻单位是否增伤：0，否；1，是", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2641": {"Id": 2641, "StringId": "hero_2_6_hit_skill_attr_frozen_dmg_add_ratio", "Remark": "遥控车对冰冻单位增伤比例", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2642": {"Id": 2642, "StringId": "hero_2_6_hit_skill_attr_tank_group_boolean", "Remark": "是否触发钢铁洪流：0，否；1，是（从右向左，从左向右，间隔发射一共7个坦克）", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2643": {"Id": 2643, "StringId": "hero_2_6_hit_skill_attr_tank_group_cnt", "Remark": "钢铁洪流坦克数量", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2644": {"Id": 2644, "StringId": "hero_2_6_hit_skill_attr_tank_bombard_boolean", "Remark": "坦克是否进行炮击：0，否；1，是", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2645": {"Id": 2645, "StringId": "hero_2_6_hit_skill_attr_tank_bombard_interval", "Remark": "坦克炮击间隔时间，单位：秒", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.8, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2646": {"Id": 2646, "StringId": "hero_2_6_hit_skill_attr_tank_bombard_dmg_ratio", "Remark": "坦克炮击爆炸伤害占当前伤害比例", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2647": {"Id": 2647, "StringId": "hero_2_6_hit_skill_attr_tank_bombard_radius", "Remark": "坦克炮半径，单位：米", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2648": {"Id": 2648, "StringId": "hero_2_6_hit_skill_attr_tank_bombard_fly_speed", "Remark": "坦克炮飞行速度，单位：米/秒", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 20, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2649": {"Id": 2649, "StringId": "hero_2_6_hit_skill_attr_tank_bombard_range", "Remark": "坦克炮最大攻击范围，单位：米", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 40, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2650": {"Id": 2650, "StringId": "hero_2_6_hit_skill_attr_tank_bombard_explode_radius", "Remark": "坦克炮爆炸攻击半径，单位：米", "SkillEffectType": 12, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "2651": {"Id": 2651, "StringId": "hero_2_6_hit_skill_model", "Remark": "遥控车模型", "SkillEffectType": 12, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2602, "BuffType": 0, "Effect": 0}, "2652": {"Id": 2652, "StringId": "hero_2_6_hit_skill_self_se", "Remark": "遥控车发射自身特效", "SkillEffectType": 12, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2601, "BuffType": 0, "Effect": 0}, "2653": {"Id": 2653, "StringId": "hero_2_6_hit_skill_explode_model", "Remark": "遥控车命中首个怪物时发生爆炸的模型", "SkillEffectType": 12, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2614, "BuffType": 0, "Effect": 0}, "2654": {"Id": 2654, "StringId": "hero_2_6_hit_skill_tank_shell_model", "Remark": "坦克炮弹模型", "SkillEffectType": 12, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2615, "BuffType": 0, "Effect": 0}, "2655": {"Id": 2655, "StringId": "hero_2_6_hit_skill_tank_bombard_model", "Remark": "坦克炮弹爆炸模型", "SkillEffectType": 12, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2616, "BuffType": 0, "Effect": 0}, "3001": {"Id": 3001, "StringId": "hero_3_0_hit_skill_attr_dmg_ratio", "Remark": "制导激光基础伤害系数", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3002": {"Id": 3002, "StringId": "hero_3_0_hit_skill_dmg_type", "Remark": "制导激光技能伤害类型：光", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3003": {"Id": 3003, "StringId": "hero_3_0_hit_skill_attr_dmg_interval", "Remark": "制导激光伤害间隔,单位:秒", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3004": {"Id": 3004, "StringId": "hero_3_0_hit_skill_attr_dmg_cnt", "Remark": "制导激光伤害次数", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 21, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3005": {"Id": 3005, "StringId": "hero_3_0_hit_skill_attr_dmg_add_ratio", "Remark": "制导激光基础伤害加法修正系数", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3006": {"Id": 3006, "StringId": "hero_3_0_hit_skill_attr_dmg_mul_ratio", "Remark": "制导激光基础伤害乘法修正系数", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 2, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3007": {"Id": 3007, "StringId": "hero_3_0_hit_skill_attr_focus_dmg_add_ratio", "Remark": "制导激光主目标伤害加法修正系数", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3008": {"Id": 3008, "StringId": "hero_3_0_hit_skill_attr_move_speed", "Remark": "制导激光目标切换速度，单位：度/秒", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 180, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3009": {"Id": 3009, "StringId": "hero_3_0_hit_skill_attr_explode_boolean", "Remark": "制导激光命中主目标是否爆炸：0，否；1，是", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3010": {"Id": 3010, "StringId": "hero_3_0_hit_skill_attr_explode_dmg_ratio", "Remark": "制导激光爆炸伤害占当前技能系数比例", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3011": {"Id": 3011, "StringId": "hero_3_0_hit_skill_attr_explode_dmg_add_ratio", "Remark": "制导激光爆炸伤害加法修正系数", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3012": {"Id": 3012, "StringId": "hero_3_0_hit_skill_attr_explode_dmg_mul_ratio", "Remark": "制导激光爆炸伤害乘法修正系数", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 2, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3013": {"Id": 3013, "StringId": "hero_3_0_hit_skill_attr_explode_radius", "Remark": "制导激光爆炸半径", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.6, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3014": {"Id": 3014, "StringId": "hero_3_0_hit_skill_attr_explode_radius_add_ratio", "Remark": "制导激光爆炸半径加法修正系数", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3015": {"Id": 3015, "StringId": "hero_3_0_hit_skill_attr_width", "Remark": "制导激光宽，单位：米", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3016": {"Id": 3016, "StringId": "hero_3_0_hit_skill_attr_width_add_ratio", "Remark": "制导激光宽加法修正系数", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3017": {"Id": 3017, "StringId": "hero_3_0_hit_skill_attr_refraction_boolean", "Remark": "制导激光是否折射：0,否；1，是（主激光每次伤害时造成折射，攻击随机目标）", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3018": {"Id": 3018, "StringId": "hero_3_0_hit_skill_attr_refraction_cnt", "Remark": "制导激光折射数量", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3019": {"Id": 3019, "StringId": "hero_3_0_hit_skill_attr_refraction_dmg_ratio", "Remark": "制导激光折射伤害占当前技能系数比例", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3020": {"Id": 3020, "StringId": "hero_3_0_hit_skill_attr_refraction_dmg_add_ratio", "Remark": "制导激光折射伤害加法修正系数", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3021": {"Id": 3021, "StringId": "hero_3_0_hit_skill_attr_refraction_dmg_mul_ratio", "Remark": "制导激光折射伤害乘法修正系数", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 2, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3022": {"Id": 3022, "StringId": "hero_3_0_hit_skill_attr_refraction_width", "Remark": "制导激光折射激光宽度，单位：米", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.15, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3023": {"Id": 3023, "StringId": "hero_3_0_hit_skill_attr_refraction_width_add_ratio", "Remark": "制导激光折光激光宽度加法修正系数", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3024": {"Id": 3024, "StringId": "hero_3_0_hit_skill_attr_repel_boolean", "Remark": "制导激光每次造成伤害是否附带击退：0，否；1，是", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3025": {"Id": 3025, "StringId": "hero_3_0_hit_skill_attr_repel_distance", "Remark": "制导激光击退距离，单位：米", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.25, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3026": {"Id": 3026, "StringId": "hero_3_0_hit_skill_attr_repel_time", "Remark": "制导激光击退时间，单位：秒", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3027": {"Id": 3027, "StringId": "hero_3_0_hit_skill_attr_repel_buff", "Remark": "制导激光击退debuff类型", "SkillEffectType": 2, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 2, "Effect": 0}, "3028": {"Id": 3028, "StringId": "hero_3_0_hit_skill_attr_dmg_up_to_frozen_boolean", "Remark": "制导激光对冰冻怪物伤害是否增加：0，否；1，是", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3029": {"Id": 3029, "StringId": "hero_3_0_hit_skill_attr_dmg_up_to_frozen_ratio", "Remark": "制导激光对冰冻怪物伤害增加百分比", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3030": {"Id": 3030, "StringId": "hero_3_0_hit_skill_attr_focus_debuff_boolean", "Remark": "制导激光是否会使主目标获得易伤debuff：0，否；1，是", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3031": {"Id": 3031, "StringId": "hero_3_0_hit_skill_attr_focus_debuff_type", "Remark": "制导激光附加的易伤debuff类型（每次伤害都附加）", "SkillEffectType": 2, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 20, "Effect": 0}, "3032": {"Id": 3032, "StringId": "hero_3_0_hit_skill_attr_focus_debuff_value", "Remark": "制导激光附加的易伤debuff值", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3033": {"Id": 3033, "StringId": "hero_3_0_hit_skill_attr_focus_debuff_time", "Remark": "制导激光附加的易伤debuff时间", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.25, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3034": {"Id": 3034, "StringId": "hero_3_0_hit_skill_attr_light_debuff_boolean", "Remark": "制导激光是否能将伤害的怪物引燃（包括折射）：0，否；1，是", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3035": {"Id": 3035, "StringId": "hero_3_0_hit_skill_attr_light_debuff_type", "Remark": "制导激光将伤害的怪物引燃debuff类型", "SkillEffectType": 2, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 32, "Effect": 0}, "3036": {"Id": 3036, "StringId": "hero_3_0_hit_skill_attr_light_debuff_time", "Remark": "制导激光将伤害的怪物引燃debuff时间", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3037": {"Id": 3037, "StringId": "hero_3_0_hit_skill_attr_end_dmg_boolean", "Remark": "制导激光是否在结束时追加一次伤害：0，否；1，是", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3038": {"Id": 3038, "StringId": "hero_3_0_hit_skill_attr_end_dmg_ratio", "Remark": "制导激光结束追加伤害占当前伤害比例", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3039": {"Id": 3039, "StringId": "hero_3_0_hit_skill_attr_electric_dmg_boolean", "Remark": "制导激光是否对主目标持续释放电磁穿刺：0，否；1，是（继承所有电磁穿刺的效果）", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3040": {"Id": 3040, "StringId": "hero_3_0_hit_skill_attr_electric_dmg_interval", "Remark": "制导激光对主目标持续释放电磁穿刺间隔时间：单位，秒", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3041": {"Id": 3041, "StringId": "hero_3_0_hit_skill_explode_model", "Remark": "制导激光主目标爆炸模型", "SkillEffectType": 2, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3008, "BuffType": 0, "Effect": 0}, "3042": {"Id": 3042, "StringId": "hero_3_0_hit_skill_target_model", "Remark": "制导激光模型", "SkillEffectType": 2, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3004, "BuffType": 0, "Effect": 0}, "3043": {"Id": 3043, "StringId": "hero_3_0_hit_skill_self_model", "Remark": "发射制导激光自身特效", "SkillEffectType": 2, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3001, "BuffType": 0, "Effect": 0}, "3044": {"Id": 3044, "StringId": "hero_3_0_hit_skill_affect_model", "Remark": "制导激光受击特效", "SkillEffectType": 2, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3002, "BuffType": 0, "Effect": 0}, "3045": {"Id": 3045, "StringId": "hero_3_0_hit_skill_attr_range_refraction_boolean", "Remark": "制导激光命中敌人是否会造成范围内折射：0,否；1，是（主激光每次伤害时造成折射，攻击随机目标）", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3046": {"Id": 3046, "StringId": "hero_3_0_hit_skill_attr_range_refraction_cnt", "Remark": "制导激光范围内折射数量", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3047": {"Id": 3047, "StringId": "hero_3_0_hit_skill_attr_range_refraction_dmg_ratio", "Remark": "制导激光范围内折射伤害占主激光伤害比例", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3048": {"Id": 3048, "StringId": "hero_3_0_hit_skill_attr_mul_boolean", "Remark": "制导激光是否会额外发射1条无强化的激光：0,否；1，是", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3049": {"Id": 3049, "StringId": "hero_3_0_hit_skill_attr_mul_chance", "Remark": "制导激光是否会额外发射无强化激光概率", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3050": {"Id": 3050, "StringId": "hero_3_0_hit_skill_attr_mul_cnt", "Remark": "制导激光是否会额外发射无强化激光数量", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3051": {"Id": 3051, "StringId": "hero_3_0_hit_skill_attr_dmg_time_add_ratio", "Remark": "制导激光持续时间加法修正系数", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3052": {"Id": 3052, "StringId": "hero_3_0_hit_skill_attr_frozen_refraction_boolean", "Remark": "制导激光命中冰冻敌人是否会造成折射：0,否；1，是（主激光每次伤害时造成折射，攻击随机目标）", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3053": {"Id": 3053, "StringId": "hero_3_0_hit_skill_attr_frozen_refraction_chance", "Remark": "制导激光命中冰冻敌人折射概率", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3054": {"Id": 3054, "StringId": "hero_3_0_hit_skill_attr_frozen_refraction_dmg_ratio", "Remark": "制导激光命中冰冻敌人造成折射伤害占主激光伤害比例", "SkillEffectType": 2, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3055": {"Id": 3055, "StringId": "hero_3_0_hit_skill_attr_electric_model", "Remark": "制导激光电磁模型", "SkillEffectType": 2, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3009, "BuffType": 0, "Effect": 0}, "3101": {"Id": 3101, "StringId": "hero_3_1_hit_skill_attr_dmg_ratio", "Remark": "火箭基础伤害系数", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3102": {"Id": 3102, "StringId": "hero_3_1_hit_skill_dmg_type", "Remark": "火箭技能伤害类型：火", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3103": {"Id": 3103, "StringId": "hero_3_1_hit_skill_attr_bullet_long", "Remark": "火箭长，单位：米", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.9, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3104": {"Id": 3104, "StringId": "hero_3_1_hit_skill_attr_bullet_wide", "Remark": "火箭宽，单位：米", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3105": {"Id": 3105, "StringId": "hero_3_1_hit_skill_attr_dmg_add_ratio", "Remark": "火箭伤害加法修正系数", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3106": {"Id": 3106, "StringId": "hero_3_1_hit_skill_attr_dmg_mul_ratio", "Remark": "火箭伤害乘法修正系数", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 2, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3107": {"Id": 3107, "StringId": "hero_3_1_hit_skill_attr_explode_boolean", "Remark": "火箭首次命中是否爆炸：0，否；1，是", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3108": {"Id": 3108, "StringId": "hero_3_1_hit_skill_explode_radius", "Remark": "火箭爆炸半径，单位：米", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3109": {"Id": 3109, "StringId": "hero_3_1_hit_skill_attr_explode_radius_add_ratio", "Remark": "火箭爆炸半径加法修正系数", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3110": {"Id": 3110, "StringId": "hero_3_1_hit_skill_attr_strike_dmg_ratio", "Remark": "火箭直击伤害占当前技能系数比例", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3111": {"Id": 3111, "StringId": "hero_3_1_hit_skill_attr_explode_dmg_ratio", "Remark": "火箭爆炸伤害占当前技能系数比例", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3112": {"Id": 3112, "StringId": "hero_3_1_hit_skill_attr_strike_dmg_add_ratio", "Remark": "火箭直击伤害加法修正系数", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3113": {"Id": 3113, "StringId": "hero_3_1_hit_skill_attr_explode_dmg_add_ratio", "Remark": "火箭爆炸伤害加法修正系数", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3114": {"Id": 3114, "StringId": "hero_3_1_hit_skill_attr_explode_dmg_mul_ratio", "Remark": "火箭爆炸伤害乘法修正系数", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 2, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3115": {"Id": 3115, "StringId": "hero_3_1_hit_skill_attr_fly_speed", "Remark": "火箭飞行速度,单位:米/秒", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 20, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3116": {"Id": 3116, "StringId": "hero_3_1_hit_skill_attr_repel_buff", "Remark": "火箭击退buff", "SkillEffectType": 3, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 2, "Effect": 0}, "3117": {"Id": 3117, "StringId": "hero_3_1_hit_skill_attr_repel_distance", "Remark": "火箭击退距离，单位：米", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3118": {"Id": 3118, "StringId": "hero_3_1_hit_skill_attr_repel_time", "Remark": "火箭击退时间，单位：秒", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3119": {"Id": 3119, "StringId": "hero_3_1_hit_skill_attr_repel_distance_add_ratio", "Remark": "火箭击退距离加法修正系数", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3120": {"Id": 3120, "StringId": "hero_3_1_hit_skill_attr_ballistic_cnt", "Remark": "火箭弹道数量", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3121": {"Id": 3121, "StringId": "hero_3_1_hit_skill_attr_ballistic_angle", "Remark": "火箭弹道夹角，度", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 20, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3122": {"Id": 3122, "StringId": "hero_3_1_hit_skill_attr_rapidfire_cnt", "Remark": "火箭发射次数", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3123": {"Id": 3123, "StringId": "hero_3_1_hit_skill_attr_rapidfire_interval", "Remark": "火箭发射间隔，单位：秒", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3124": {"Id": 3124, "StringId": "hero_3_1_hit_skill_attr_ballistic_dmg_add_ratio", "Remark": "火箭多发伤害加法修正系数", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3125": {"Id": 3125, "StringId": "hero_3_1_hit_skill_attr_rapidfire_dmg_add_ratio", "Remark": "火箭连发伤害加法修正系数", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3126": {"Id": 3126, "StringId": "hero_3_1_hit_skill_attr_explode_always_boolean", "Remark": "火箭每次命中都会爆炸：0，否；1，是", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3127": {"Id": 3127, "StringId": "hero_3_1_hit_skill_attr_division_boolean", "Remark": "火箭爆炸后（以前是首次命中）是否分裂3个火花：0，否；1，是", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3128": {"Id": 3128, "StringId": "hero_3_1_hit_skill_attr_division_radius", "Remark": "火箭分裂火花半径，单位：米", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3129": {"Id": 3129, "StringId": "hero_3_1_hit_skill_attr_division_fly_speed", "Remark": "火箭分裂火花飞行速度，单位：米/秒", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 15, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3130": {"Id": 3130, "StringId": "hero_3_1_hit_skill_attr_division_fly_range", "Remark": "火箭分裂火花最大飞行距离，单位：米", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 40, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3131": {"Id": 3131, "StringId": "hero_3_1_hit_skill_attr_division_cnt", "Remark": "火箭分裂火花数量", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3132": {"Id": 3132, "StringId": "hero_3_1_hit_skill_attr_division_dmg_ratio", "Remark": "火箭分裂火花伤害占当前技能系数比例", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3133": {"Id": 3133, "StringId": "hero_3_1_hit_skill_attr_division_dmg_add_ratio", "Remark": "火箭分裂火花伤害加法修正系数", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3134": {"Id": 3134, "StringId": "hero_3_1_hit_skill_attr_division_dmg_mul_ratio", "Remark": "火箭分裂火花伤害乘法修正系数", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 2, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3135": {"Id": 3135, "StringId": "hero_3_1_hit_skill_attr_fierce_cnt", "Remark": "火箭穿透数量，填0表示只命中1个，-1表示无限", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3136": {"Id": 3136, "StringId": "hero_3_1_hit_skill_attr_fierce_dmg_ratio", "Remark": "火箭穿透后命中目标伤害占当前伤害比例", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3137": {"Id": 3137, "StringId": "hero_3_1_hit_skill_attr_explode_fire_dot_boolean", "Remark": "火箭爆炸是否附加引燃debuff：0，否；1，是", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3138": {"Id": 3138, "StringId": "hero_3_1_hit_skill_attr_explode_fire_dot_chance", "Remark": "火箭爆炸附加引燃debuff概率", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3139": {"Id": 3139, "StringId": "hero_3_1_hit_skill_attr_explode_fire_dot_buff_type", "Remark": "火箭爆炸附加引燃debuff类型", "SkillEffectType": 3, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 33, "Effect": 0}, "3140": {"Id": 3140, "StringId": "hero_3_1_hit_skill_attr_explode_fire_dot_time", "Remark": "火箭爆炸附加引燃debuff时间，单位：秒,-1=一直", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3141": {"Id": 3141, "StringId": "hero_3_1_hit_skill_attr_ignite_extra_dmg_boolean", "Remark": "火箭赋予的引燃状态是否额外追加最大生命值伤害：0，否；1，是", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3142": {"Id": 3142, "StringId": "hero_3_1_hit_skill_attr_ignite_extra_dmg", "Remark": "额外追加最大生命值伤害百分比（每次造成伤害结算）", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3143": {"Id": 3143, "StringId": "hero_3_1_hit_skill_attr_strike_stun_boolean", "Remark": "火箭冲击伤害是否附加眩晕debuff：0，否；1，是", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3144": {"Id": 3144, "StringId": "hero_3_1_hit_skill_attr_strike_stun_chance", "Remark": "火箭冲击伤害附加眩晕debuff概率", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3145": {"Id": 3145, "StringId": "hero_3_1_hit_skill_attr_strike_stun_buff_type", "Remark": "火箭冲击伤害附加眩晕debuff类型", "SkillEffectType": 3, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 6, "Effect": 0}, "3146": {"Id": 3146, "StringId": "hero_3_1_hit_skill_attr_strike_stun_time", "Remark": "火箭冲击伤害附加眩晕debuff时间，单位：秒,-1=一直", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3147": {"Id": 3147, "StringId": "hero_3_1_hit_skill_attr_ignore_fire_immunity_boolean", "Remark": "火箭攻击是否无视火系免疫：0，否；1，是", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3148": {"Id": 3148, "StringId": "hero_3_1_hit_skill_attr_explode_stun_boolean", "Remark": "火箭爆炸是否附加眩晕debuff：0，否；1，是", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3149": {"Id": 3149, "StringId": "hero_3_1_hit_skill_attr_explode_stun_chance", "Remark": "火箭爆炸附件眩晕debuff概率", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3150": {"Id": 3150, "StringId": "hero_3_1_hit_skill_attr_explode_stun_buff_type", "Remark": "火箭爆炸附加眩晕debuff类型", "SkillEffectType": 3, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 6, "Effect": 0}, "3151": {"Id": 3151, "StringId": "hero_3_1_hit_skill_attr_explode_stun_time", "Remark": "火箭爆炸附件眩晕debuff时间，单位：秒,-1=一直", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3152": {"Id": 3152, "StringId": "hero_3_1_hit_skill_attr_ignite_death_explode_boolean", "Remark": "火箭点燃的怪物死亡时是否发生爆炸：0，否；1，是", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3153": {"Id": 3153, "StringId": "hero_3_1_hit_skill_attr_ignite_death_explode_dmg_ratio", "Remark": "火箭点燃的怪物死亡时爆炸伤害系数", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3154": {"Id": 3154, "StringId": "hero_3_1_hit_skill_attr_ignite_death_explode_radius", "Remark": "火箭点燃的怪物死亡时爆炸半径", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3155": {"Id": 3155, "StringId": "hero_3_1_hit_skill_bullet_model", "Remark": "火箭子弹模型", "SkillEffectType": 3, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3102, "BuffType": 0, "Effect": 0}, "3156": {"Id": 3156, "StringId": "hero_3_1_hit_skill_explode_model", "Remark": "火箭爆炸模型", "SkillEffectType": 3, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3103, "BuffType": 0, "Effect": 0}, "3157": {"Id": 3157, "StringId": "hero_3_1_hit_skill_division_model", "Remark": "火箭分裂火花模型", "SkillEffectType": 3, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3104, "BuffType": 0, "Effect": 0}, "3158": {"Id": 3158, "StringId": "hero_3_1_hit_skill_self_model", "Remark": "发射火箭自身特效", "SkillEffectType": 3, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3101, "BuffType": 0, "Effect": 0}, "3159": {"Id": 3159, "StringId": "hero_3_1_hit_skill_fire_dot_model", "Remark": "火箭引燃特效", "SkillEffectType": 3, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3105, "BuffType": 0, "Effect": 0}, "3160": {"Id": 3160, "StringId": "hero_3_1_hit_skill_bullet_niubi_model", "Remark": "超爆子弹模型", "SkillEffectType": 3, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3109, "BuffType": 0, "Effect": 0}, "3161": {"Id": 3161, "StringId": "hero_3_1_hit_skill_affect_se", "Remark": "火箭受击特效", "SkillEffectType": 3, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3110, "BuffType": 0, "Effect": 0}, "3162": {"Id": 3162, "StringId": "hero_3_1_hit_skill_attr_copy_always_boolean", "Remark": "火箭每次命中都会分裂强化后的火箭（保留全部属性）", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3163": {"Id": 3163, "StringId": "hero_3_1_hit_skill_yujin_boolean", "Remark": "火箭爆炸是否会留下燃烧区域：0，否；1，是", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3164": {"Id": 3164, "StringId": "hero_3_1_hit_skill_yujin_radius_radio", "Remark": "灼烧区域半径：单位，米", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3165": {"Id": 3165, "StringId": "hero_3_1_hit_skill_yujin_time", "Remark": "灼烧区域持续时间：单位，秒", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3166": {"Id": 3166, "StringId": "hero_3_1_hit_skill_yujin_burn_damage_ratio", "Remark": "灼烧伤害占当前伤害系数比例", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3167": {"Id": 3167, "StringId": "hero_3_1_hit_skill_yujin_burn_damage_add_ratio", "Remark": "灼烧伤害加法修正系数", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3168": {"Id": 3168, "StringId": "hero_3_1_hit_skill_yujin_burn_interval", "Remark": "灼烧伤害间隔：单位，秒", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3169": {"Id": 3169, "StringId": "hero_3_1_hit_skill_attr_ignite_extra_dmg_max", "Remark": "额外追加最大生命值伤害不能超过攻击力的倍数", "SkillEffectType": 3, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3170": {"Id": 3170, "StringId": "hero_3_1_hit_skill_strike_model", "Remark": "火箭冲击模型", "SkillEffectType": 3, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3112, "BuffType": 0, "Effect": 0}, "3201": {"Id": 3201, "StringId": "hero_3_2_hit_skill_attr_dmg_ratio", "Remark": "冰霜玫瑰基础伤害系数(外圈伤害）", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3202": {"Id": 3202, "StringId": "hero_3_2_hit_skill_dmg_type", "Remark": "冰霜玫瑰技能类型：冰", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3203": {"Id": 3203, "StringId": "hero_3_2_hit_skill_attr_radius", "Remark": "冰霜玫瑰范围半径，单位：米", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3204": {"Id": 3204, "StringId": "hero_3_2_hit_skill_attr_dmg_interval", "Remark": "冰霜玫瑰伤害间隔，单位：秒", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3205": {"Id": 3205, "StringId": "hero_3_2_hit_skill_attr_dmg_add_ratio", "Remark": "冰霜玫瑰基础伤害加法修正系数（外圈伤害加法修正系数）", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3206": {"Id": 3206, "StringId": "hero_3_2_hit_skill_attr_strike_dmg_add_ratio", "Remark": "冰霜玫瑰基础直击伤害加法修正系数（外圈伤害加法修正系数）", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3207": {"Id": 3207, "StringId": "hero_3_2_hit_skill_attr_core_radius_ratio", "Remark": "冰霜玫瑰中心区域半径占当前总半径的比例", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3208": {"Id": 3208, "StringId": "hero_3_2_hit_skill_attr_core_dmg_ratio", "Remark": "冰霜玫瑰中心区域伤害占当前伤害的比例（内圈伤害/外圈伤害）", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3209": {"Id": 3209, "StringId": "hero_3_2_hit_skill_attr_core_dmg_add_ratio", "Remark": "冰霜玫瑰中心伤害加法修正系数（内圈伤害加法修正系数）", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3210": {"Id": 3210, "StringId": "hero_3_2_hit_skill_attr_time", "Remark": "冰霜玫瑰时间，单位：秒", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3211": {"Id": 3211, "StringId": "hero_3_2_hit_skill_attr_rapidfire_cnt", "Remark": "冰霜玫瑰发射次数", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3212": {"Id": 3212, "StringId": "hero_3_2_hit_skill_attr_rapidfire_interval", "Remark": "冰霜玫瑰发射间隔，单位：秒", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.8, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3213": {"Id": 3213, "StringId": "hero_3_2_hit_skill_attr_speed_down_buff", "Remark": "冰霜玫瑰减速buff", "SkillEffectType": 4, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 10, "Effect": 0}, "3214": {"Id": 3214, "StringId": "hero_3_2_hit_skill_attr_speed_down_time", "Remark": "冰霜玫瑰外圈减速时间，单位：秒", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3215": {"Id": 3215, "StringId": "hero_3_2_hit_skill_attr_core_speed_down_time", "Remark": "冰霜玫瑰内圈减速时间，单位：秒", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3216": {"Id": 3216, "StringId": "hero_3_2_hit_skill_attr_explode_boolean", "Remark": "冰霜玫瑰结束时是否发生爆炸：0，否；1，是", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3217": {"Id": 3217, "StringId": "hero_3_2_hit_skill_attr_explode_radius", "Remark": "冰霜玫瑰爆炸范围半径，单位：米", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3218": {"Id": 3218, "StringId": "hero_3_2_hit_skill_attr_explode_dmg_ratio", "Remark": "冰霜玫瑰结束时爆炸伤害系数占当前伤害比例", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3219": {"Id": 3219, "StringId": "hero_3_2_hit_skill_attr_explode_division_boolean", "Remark": "冰霜玫瑰结束时爆炸是否发生分裂：0，否；1，是", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3220": {"Id": 3220, "StringId": "hero_3_2_hit_skill_attr_explode_division_cnt", "Remark": "冰霜玫瑰结束时爆炸分裂小雪花数量（自动索敌的）", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3221": {"Id": 3221, "StringId": "hero_3_2_hit_skill_attr_explode_division_radius", "Remark": "冰霜玫瑰爆炸分裂小雪花半径，单位：米", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.25, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3222": {"Id": 3222, "StringId": "hero_3_2_hit_skill_attr_explode_division_fly_speed", "Remark": "分裂小雪花飞行速度，单位：米/秒", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 15, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3223": {"Id": 3223, "StringId": "hero_3_2_hit_skill_attr_explode_division_fly_range", "Remark": "分裂小雪花最大飞行距离，单位：米", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 40, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3224": {"Id": 3224, "StringId": "hero_3_2_hit_skill_attr_explode_division_fierce_cnt", "Remark": "分裂小雪花穿透数量", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3225": {"Id": 3225, "StringId": "hero_3_2_hit_skill_attr_explode_division_dmg_ratio", "Remark": "分裂小雪花伤害占当前技能系数比例", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3226": {"Id": 3226, "StringId": "hero_3_2_hit_skill_attr_explode_division_frozen_buff_boolean", "Remark": "分裂小雪花命中怪物后是否触发冻结buff：0，否；1，是", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3227": {"Id": 3227, "StringId": "hero_3_2_hit_skill_attr_explode_division_frozen_buff_type", "Remark": "分裂小雪花命中怪物后触发buff类型", "SkillEffectType": 4, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 5, "Effect": 0}, "3228": {"Id": 3228, "StringId": "hero_3_2_hit_skill_attr_explode_division_frozen_buff_time", "Remark": "分裂小雪花命中怪物后触发buff时间", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3229": {"Id": 3229, "StringId": "hero_3_2_hit_skill_attr_frozen_buff_boolean", "Remark": "冰霜玫瑰是否触发冻结buff：0，否；1，是", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3230": {"Id": 3230, "StringId": "hero_3_2_hit_skill_attr_frozen_buff_type", "Remark": "冰霜玫瑰每次伤害触发冻结buff", "SkillEffectType": 4, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 5, "Effect": 0}, "3231": {"Id": 3231, "StringId": "hero_3_2_hit_skill_attr_frozen_buff_time", "Remark": "冰霜玫瑰每次伤害触发冻结buff时间，单位：秒", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.35, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3232": {"Id": 3232, "StringId": "hero_3_2_hit_skill_attr_pull_boolean", "Remark": "冰霜玫瑰是否触发冰冻加农（将周围的怪物吸到冰暴中心）：0，否；1，是", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3233": {"Id": 3233, "StringId": "hero_3_2_hit_skill_attr_frozen_cannon_radius", "Remark": "冰霜玫瑰冰冻加农半径，单位：米", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3234": {"Id": 3234, "StringId": "hero_3_2_hit_skill_attr_pull_frozen_buff_type", "Remark": "冰冻加农触发冰冻buff类型", "SkillEffectType": 4, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 5, "Effect": 0}, "3235": {"Id": 3235, "StringId": "hero_3_2_hit_skill_attr_pull_buff_type", "Remark": "冰冻加农触发牵引buff类型", "SkillEffectType": 4, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 3, "Effect": 0}, "3236": {"Id": 3236, "StringId": "hero_3_2_hit_skill_attr_pull_speed", "Remark": "冰冻加农吸附怪物到冰暴中心的速度，单位：米/秒", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3237": {"Id": 3237, "StringId": "hero_3_2_hit_skill_self_se", "Remark": "发射冰霜玫瑰自身特效", "SkillEffectType": 4, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3201, "BuffType": 0, "Effect": 0}, "3238": {"Id": 3238, "StringId": "hero_3_2_hit_skill_start_se", "Remark": "冰霜玫瑰起始特效", "SkillEffectType": 4, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3202, "BuffType": 0, "Effect": 0}, "3239": {"Id": 3239, "StringId": "hero_3_2_hit_skill_start_model", "Remark": "冰霜玫瑰起始模型", "SkillEffectType": 4, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3206, "BuffType": 0, "Effect": 0}, "3240": {"Id": 3240, "StringId": "hero_3_2_hit_skill_loop_model", "Remark": "冰霜玫瑰循环模型", "SkillEffectType": 4, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3207, "BuffType": 0, "Effect": 0}, "3241": {"Id": 3241, "StringId": "hero_3_2_hit_skill_end_model", "Remark": "冰霜玫瑰结束模型", "SkillEffectType": 4, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3208, "BuffType": 0, "Effect": 0}, "3242": {"Id": 3242, "StringId": "hero_3_2_hit_skill_explode_division_model", "Remark": "冰霜玫瑰爆炸分裂小雪花模型", "SkillEffectType": 4, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3213, "BuffType": 0, "Effect": 0}, "3243": {"Id": 3243, "StringId": "hero_3_2_hit_skill_frozen_cannon_model", "Remark": "冰霜玫瑰冰冻加农模型", "SkillEffectType": 4, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3214, "BuffType": 0, "Effect": 0}, "3244": {"Id": 3244, "StringId": "hero_3_2_hit_skill_attr_add_time", "Remark": "冰霜玫瑰延续时间，单位：秒", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3245": {"Id": 3245, "StringId": "hero_3_2_hit_skill_attr_add_time_dmg_add_ratio", "Remark": "冰霜玫瑰延续时间伤害加法修正系数", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3246": {"Id": 3246, "StringId": "hero_3_2_hit_skill_explode_model", "Remark": "冰霜玫瑰爆炸模型", "SkillEffectType": 4, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3212, "BuffType": 0, "Effect": 0}, "3247": {"Id": 3247, "StringId": "hero_3_2_hit_skill_attr_cd_deduct_boolean", "Remark": "冰霜玫瑰首次释放时若仅命中1个敌人，则冷却缩减：0，否；1，是", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3248": {"Id": 3248, "StringId": "hero_3_2_hit_skill_attr_cd_deduct_ratio", "Remark": "冰霜玫瑰首次释放时若仅命中1个敌人，则冷却缩减比例", "SkillEffectType": 4, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3301": {"Id": 3301, "StringId": "hero_3_3_hit_skill_attr_dmg_ratio", "Remark": "西瓜轰炸基础伤害系数", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3302": {"Id": 3302, "StringId": "hero_3_hit_skill_dmg_type", "Remark": "西瓜技能类型：物理", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 6, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3303": {"Id": 3303, "StringId": "hero_3_3_hit_skill_attr_dmg_add_ratio", "Remark": "西瓜轰炸基础伤害加法修正系数", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3304": {"Id": 3304, "StringId": "hero_3_3_hit_skill_attr_strike_dmg_add_ratio", "Remark": "西瓜轰炸基础直击伤害加法修正系数", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3305": {"Id": 3305, "StringId": "hero_3_3_hit_skill_attr_cnt", "Remark": "西瓜轰炸次数", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3306": {"Id": 3306, "StringId": "hero_3_3_hit_skill_attr_time", "Remark": "西瓜轰炸掉落时间：单位，秒", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.6, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3307": {"Id": 3307, "StringId": "hero_3_3_hit_skill_attr_mul_interval", "Remark": "西瓜多次轰炸间隔：单位，秒", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.8, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3308": {"Id": 3308, "StringId": "hero_3_3_hit_skill_attr_mul_dmg_add_ratio", "Remark": "西瓜多次轰炸伤害加法修正系数", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3309": {"Id": 3309, "StringId": "hero_3_3_hit_skill_attr_radius", "Remark": "西瓜轰炸基础半径：单位，米", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3310": {"Id": 3310, "StringId": "hero_3_3_hit_skill_attr_core_radius_ratio", "Remark": "西瓜轰炸核心区域半径占当前半径比例", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3311": {"Id": 3311, "StringId": "hero_3_3_hit_skill_attr_radius_add_ratio", "Remark": "西瓜轰炸基础半径加法修正系数", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3312": {"Id": 3312, "StringId": "hero_3_3_hit_skill_attr_core_dmg_ratio", "Remark": "西瓜轰炸核心区域伤害占当前伤害比例", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3313": {"Id": 3313, "StringId": "hero_3_3_hit_skill_attr_core_dmg_add_ratio", "Remark": "西瓜轰炸核心区域伤害加法修正系数", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3314": {"Id": 3314, "StringId": "hero_3_3_hit_skill_attr_repel_boolean", "Remark": "西瓜轰炸是否附带击退：0，否；1，是", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3315": {"Id": 3315, "StringId": "hero_3_3_hit_skill_attr_repel_buff", "Remark": "西瓜轰炸击退buff类型", "SkillEffectType": 8, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 2, "Effect": 0}, "3316": {"Id": 3316, "StringId": "hero_3_3_hit_skill_attr_repel_distance", "Remark": "西瓜轰炸击退距离：单位，米", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3317": {"Id": 3317, "StringId": "hero_3_3_hit_skill_attr_repel_time", "Remark": "西瓜轰炸击退时间：单位，秒", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3318": {"Id": 3318, "StringId": "hero_3_3_hit_skill_attr_stun_boolean", "Remark": "西瓜轰炸是否附带眩晕：0，否；1，是", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3319": {"Id": 3319, "StringId": "hero_3_3_hit_skill_attr_stun_debuff_type", "Remark": "西瓜轰炸附带眩晕debuff类型", "SkillEffectType": 8, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 6, "Effect": 0}, "3320": {"Id": 3320, "StringId": "hero_3_3_hit_skill_attr_stun_debuff_time", "Remark": "西瓜轰炸附带眩晕debuff时间：单位，秒", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3321": {"Id": 3321, "StringId": "hero_3_3_hit_skill_attr_frozen_dmg_add_boolean", "Remark": "西瓜轰炸对冰冻单位是否增伤：0，否；1，是", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3322": {"Id": 3322, "StringId": "hero_3_3_hit_skill_attr_frozen_dmg_add_ratio", "Remark": "西瓜轰炸对冰冻单位增伤比例", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3323": {"Id": 3323, "StringId": "hero_3_3_hit_skill_attr_extra_dmg_add_boolean", "Remark": "西瓜轰炸是否会对怪物造成额外增伤：0，否；1，是", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3324": {"Id": 3324, "StringId": "hero_3_3_hit_skill_attr_extra_dmg_add_chance", "Remark": "西瓜轰炸对怪物造成额外增伤概率", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3325": {"Id": 3325, "StringId": "hero_3_3_hit_skill_attr_extra_dmg_add_ratio", "Remark": "西瓜轰炸对怪物造成额外增伤比例", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3326": {"Id": 3326, "StringId": "hero_3_3_hit_skill_attr_rapidfire_boolean", "Remark": "西瓜是否会在同一位置连续轰炸两次：0，否；1，是", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3327": {"Id": 3327, "StringId": "hero_3_3_hit_skill_attr_rapidfire_chance", "Remark": "西瓜在同一位置连续轰炸两次概率", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.25, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3328": {"Id": 3328, "StringId": "hero_3_3_hit_skill_attr_rapidfire_interval", "Remark": "西瓜在同一位置连续轰炸两次间隔时间：单位，秒", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3329": {"Id": 3329, "StringId": "hero_3_3_hit_skill_extra_fire_dmg_boolean", "Remark": "西瓜轰炸是否附带额外的火系伤害：0，否；1，是", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3330": {"Id": 3330, "StringId": "hero_3_3_hit_skill_extra_fire_dmg_type", "Remark": "西瓜轰炸附带额外的火系伤害类型", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3331": {"Id": 3331, "StringId": "hero_3_3_hit_skill_extra_fire_dmg_ratio", "Remark": "西瓜轰炸附带额外的火系伤害比例", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3332": {"Id": 3332, "StringId": "hero_3_3_hit_skill_yujin_boolean", "Remark": "西瓜轰炸是否会留下范围灼烧区域：0，否；1，是", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3333": {"Id": 3333, "StringId": "hero_3_3_hit_skill_yujin_dmg_type", "Remark": "西瓜轰炸产生的灼烧区域伤害类型", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3334": {"Id": 3334, "StringId": "hero_3_3_hit_skill_yujin_radius_radio", "Remark": "西瓜轰炸留下的灼烧区域半径占当前半径比例", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3335": {"Id": 3335, "StringId": "hero_3_3_hit_skill_yujin_time", "Remark": "灼烧区域时间：单位，秒", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3336": {"Id": 3336, "StringId": "hero_3_3_hit_skill_yujin_dmg_ratio", "Remark": "灼烧区域伤害占基础伤害比例", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3337": {"Id": 3337, "StringId": "hero_3_3_hit_skill_yujin_dmg_add_ratio", "Remark": "灼烧区域伤害加法修正系数", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3338": {"Id": 3338, "StringId": "hero_3_3_hit_skill_yujin_dmg_interval", "Remark": "灼烧区域伤害间隔时间：单位，秒", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3339": {"Id": 3339, "StringId": "hero_3_3_hit_skill_yujin_speed_down_boolean", "Remark": "灼烧区域是否会造成减速：0，否；1，是", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3340": {"Id": 3340, "StringId": "hero_3_3_hit_skill_yujin_speed_down_debuff_type", "Remark": "灼烧区域每次伤害造成减速debuff类型", "SkillEffectType": 8, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 9, "Effect": 0}, "3341": {"Id": 3341, "StringId": "hero_3_3_hit_skill_yujin_speed_down_debuff_time", "Remark": "灼烧区域每次伤害造成减速debuff时间", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.55, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3342": {"Id": 3342, "StringId": "hero_3_3_hit_skill_rampage_bombing_boolean", "Remark": "每次攻击是否触发持续性落下小型炸弹攻击：0，否；1，是", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3343": {"Id": 3343, "StringId": "hero_3_3_hit_skill_rampage_bombing_time", "Remark": "持续性落下小型炸弹攻击的总时间：单位，秒", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 7.7, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3344": {"Id": 3344, "StringId": "hero_3_3_hit_skill_rampage_bombing_interval", "Remark": "持续性落下小型炸弹攻击的间隔：单位，秒", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3345": {"Id": 3345, "StringId": "hero_3_3_hit_skill_rampage_bombing_dmg_ratio", "Remark": "小型炸弹伤害占基础伤害比例(小型炸弹不受加成影响）", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3346": {"Id": 3346, "StringId": "hero_3_3_hit_skill_rampage_bombing_radius", "Remark": "小型炸弹伤害半径：单位，米", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3347": {"Id": 3347, "StringId": "hero_3_3_hit_drop_model", "Remark": "西瓜下落模型", "SkillEffectType": 8, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3302, "BuffType": 0, "Effect": 0}, "3348": {"Id": 3348, "StringId": "hero_3_3_hit_explode_model", "Remark": "西瓜爆炸模型", "SkillEffectType": 8, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3306, "BuffType": 0, "Effect": 0}, "3349": {"Id": 3349, "StringId": "hero_3_3_hit_child_fire_model", "Remark": "小西瓜开火模型", "SkillEffectType": 8, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3307, "BuffType": 0, "Effect": 0}, "3350": {"Id": 3350, "StringId": "hero_3_3_hit_child_drop_model", "Remark": "小西瓜下落模型", "SkillEffectType": 8, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3308, "BuffType": 0, "Effect": 0}, "3351": {"Id": 3351, "StringId": "hero_3_3_hit_child_explode_model", "Remark": "小西瓜爆炸模型", "SkillEffectType": 8, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3309, "BuffType": 0, "Effect": 0}, "3352": {"Id": 3352, "StringId": "hero_3_3_hit_fire_model", "Remark": "西瓜灼烧模型", "SkillEffectType": 8, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 2515, "BuffType": 0, "Effect": 0}, "3353": {"Id": 3353, "StringId": "hero_3_3_hit_drop_2_model", "Remark": "2次西瓜下落模型", "SkillEffectType": 8, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3311, "BuffType": 0, "Effect": 0}, "3354": {"Id": 3354, "StringId": "hero_3_3_hit_nuclear_drop_model", "Remark": "核弹西瓜模型", "SkillEffectType": 8, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3315, "BuffType": 0, "Effect": 0}, "3355": {"Id": 3355, "StringId": "hero_3_3_hit_nuclear_explode_model", "Remark": "核弹西瓜爆炸模型", "SkillEffectType": 8, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3316, "BuffType": 0, "Effect": 0}, "3356": {"Id": 3356, "StringId": "hero_3_3_hit_nuclear_drop_2_model", "Remark": "核弹西瓜2次下落模型", "SkillEffectType": 8, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3317, "BuffType": 0, "Effect": 0}, "3357": {"Id": 3357, "StringId": "hero_3_3_hit_skill_nuclear_boolean", "Remark": "是否开启核弹西瓜：0，否；1，是", "SkillEffectType": 8, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3358": {"Id": 3358, "StringId": "hero_3_3_hit_core_model", "Remark": "核心增伤模型", "SkillEffectType": 8, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3318, "BuffType": 0, "Effect": 0}, "3401": {"Id": 3401, "StringId": "hero_3_4_hit_skill_attr_dmg_ratio", "Remark": "电弧基础伤害系数", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3402": {"Id": 3402, "StringId": "hero_3_4_hit_skill_attr_dmg_type", "Remark": "电弧技能伤害类型：电", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3403": {"Id": 3403, "StringId": "hero_3_4_hit_skill_attr_dmg_add_ratio", "Remark": "电弧基础伤害加法修正系数", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3404": {"Id": 3404, "StringId": "hero_3_4_hit_skill_attr_strike_dmg_add_ratio", "Remark": "电弧基础直击伤害加法修正系数", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3405": {"Id": 3405, "StringId": "hero_3_4_hit_skill_attr_catapult_cnt", "Remark": "电弧弹射次数", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3406": {"Id": 3406, "StringId": "hero_3_4_hit_skill_attr_catapult_interval", "Remark": "电弧弹射间隔时间，单位：秒", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3407": {"Id": 3407, "StringId": "hero_3_4_hit_skill_attr_mul_cnt", "Remark": "电弧弹射初始目标数量", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3408": {"Id": 3408, "StringId": "hero_3_4_hit_skill_attr_mul_interval", "Remark": "电弧多发间隔时间：单位，秒", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3409": {"Id": 3409, "StringId": "hero_3_4_hit_skill_attr_paralysis_type", "Remark": "电弧弹射攻击目标后麻痹类型", "SkillEffectType": 9, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 4, "Effect": 0}, "3410": {"Id": 3410, "StringId": "hero_3_4_hit_skill_attr_paralysis_time", "Remark": "电弧弹射攻击目标后麻痹时间，单位：秒", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3411": {"Id": 3411, "StringId": "hero_3_4_hit_skill_attr_catapult_range", "Remark": "电弧弹射最大距离，单位：米", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 13, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3412": {"Id": 3412, "StringId": "hero_3_4_hit_skill_attr_explode_boolean", "Remark": "电弧每次跃迁造成伤害后是否会造成爆炸（注意电磁免疫的情况）", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3413": {"Id": 3413, "StringId": "hero_3_4_hit_skill_attr_explode_dmg_ratio", "Remark": "电弧每次跃迁爆炸伤害占当前伤害比例", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3414": {"Id": 3414, "StringId": "hero_3_4_hit_skill_attr_explode_dmg_add_ratio", "Remark": "电弧每次跃迁爆炸伤害加法修正系数", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3415": {"Id": 3415, "StringId": "hero_3_4_hit_skill_attr_explode_radius", "Remark": "电弧每次跃迁爆炸半径，单位：米", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3416": {"Id": 3416, "StringId": "hero_3_4_hit_skill_attr_explode_radius_add_ratio", "Remark": "电弧每次跃迁爆炸半径加法修正系数", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3417": {"Id": 3417, "StringId": "hero_3_4_hit_skill_attr_explode_paralysis_boolean", "Remark": "电弧每次跃迁爆炸是否触发麻痹：0，否；1，是", "SkillEffectType": 9, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3418": {"Id": 3418, "StringId": "hero_3_4_hit_skill_attr_explode_paralysis_type", "Remark": "电弧每次跃迁爆炸触发麻痹类型", "SkillEffectType": 9, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 4, "Effect": 0}, "3419": {"Id": 3419, "StringId": "hero_3_4_hit_skill_attr_explode_paralysis_time", "Remark": "电弧每次跃迁爆炸触发麻痹时间，单位：秒", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3420": {"Id": 3420, "StringId": "hero_3_4_hit_skill_attr_stun_dmg_add_boolean", "Remark": "电弧命中眩晕的敌人时是否附加易伤debuff：0，否；1，是", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3421": {"Id": 3421, "StringId": "hero_3_4_hit_skill_attr_stun_dmg_add_debuff_type", "Remark": "电弧命中眩晕的敌人附加易伤debuff类型", "SkillEffectType": 9, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 20, "Effect": 0}, "3422": {"Id": 3422, "StringId": "hero_3_4_hit_skill_attr_stun_dmg_add_debuff_value", "Remark": "电弧命中眩晕的敌人附加易伤debuff值", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3423": {"Id": 3423, "StringId": "hero_3_4_hit_skill_attr_stun_dmg_add_debuff_time", "Remark": "电弧命中眩晕的敌人附加易伤debuff时间，单位：秒", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3424": {"Id": 3424, "StringId": "hero_3_4_hit_skill_attr_division_boolean", "Remark": "电弧弹射时是否会裂变跃迁电子：0，否；1，是（裂变的电子继承剩余弹射次数）", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3425": {"Id": 3425, "StringId": "hero_3_4_hit_skill_attr_division_chance", "Remark": "电弧弹射时裂变跃迁电子概率", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3426": {"Id": 3426, "StringId": "hero_3_4_hit_skill_attr_pull_boolean", "Remark": "电弧命中怪物时是否会牵引周围怪物：0，否；1，是", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3427": {"Id": 3427, "StringId": "hero_3_4_hit_skill_attr_pull_buff", "Remark": "电弧命中怪物时牵引buff类型", "SkillEffectType": 9, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 3, "Effect": 0}, "3428": {"Id": 3428, "StringId": "hero_3_4_hit_skill_attr_pull_time", "Remark": "电弧命中怪物时牵引时间，单位：秒", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3429": {"Id": 3429, "StringId": "hero_3_4_hit_skill_attr_pull_speed", "Remark": "电弧命中怪物时牵引速度，单位：米/秒", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3430": {"Id": 3430, "StringId": "hero_3_4_hit_skill_attr_pull_radius", "Remark": "电弧命中怪物时牵引半径，单位：米", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3431": {"Id": 3431, "StringId": "hero_3_4_hit_skill_attr_catapult_dmg_add_boolean", "Remark": "电弧伤害是否会随着弹射次数增加而叠加：0，否；1，是", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3432": {"Id": 3432, "StringId": "hero_3_4_hit_skill_attr_catapult_dmg_add_ratio", "Remark": "电弧弹射伤害加法修正系数", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0.1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3433": {"Id": 3433, "StringId": "hero_3_4_hit_skill_attr_lighting_struck_boolean", "Remark": "电弧攻击后是否附加招雷劈buff：0，否；1，是(buff时间=麻痹时间）", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3434": {"Id": 3434, "StringId": "hero_3_4_hit_skill_attr_lighting_struck_debuff_type", "Remark": "招雷劈debuff类型", "SkillEffectType": 9, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 19, "Effect": 0}, "3435": {"Id": 3435, "StringId": "hero_3_4_hit_skill_attr_lighting_struck_range", "Remark": "招雷劈电击范围，单位：米", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 40, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3436": {"Id": 3436, "StringId": "hero_3_4_hit_skill_attr_lighting_struck_dmg_ratio", "Remark": "招雷劈伤害系数占当前伤害系数比例", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3437": {"Id": 3437, "StringId": "hero_3_4_hit_skill_begin_model", "Remark": "电弧起始点模型", "SkillEffectType": 9, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3402, "BuffType": 0, "Effect": 0}, "3438": {"Id": 3438, "StringId": "hero_3_4_hit_skill_line_model", "Remark": "电弧线模型", "SkillEffectType": 9, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3403, "BuffType": 0, "Effect": 0}, "3439": {"Id": 3439, "StringId": "hero_3_4_hit_skill_end_model", "Remark": "电弧击中目标", "SkillEffectType": 9, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3404, "BuffType": 0, "Effect": 0}, "3440": {"Id": 3440, "StringId": "hero_3_4_hit_skill_explode_model", "Remark": "电弧爆炸模型", "SkillEffectType": 9, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3405, "BuffType": 0, "Effect": 0}, "3441": {"Id": 3441, "StringId": "hero_3_4_hit_skill_attr_paralysis_electric_shock_debuff_model", "Remark": "招雷劈debuff状态 模型", "SkillEffectType": 9, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3406, "BuffType": 0, "Effect": 0}, "3442": {"Id": 3442, "StringId": "hero_3_4_hit_skill_attr_paralysis_electric_shock_model", "Remark": "雷劈模型", "SkillEffectType": 9, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3407, "BuffType": 0, "Effect": 0}, "3443": {"Id": 3443, "StringId": "hero_3_4_hit_skill_long_line_model", "Remark": "电弧长线模型", "SkillEffectType": 9, "AttrDefaultType": 2, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 3411, "BuffType": 0, "Effect": 0}, "3444": {"Id": 3444, "StringId": "hero_3_4_hit_skill_attr_division_initial_boolean", "Remark": "电弧命中敌人时，是否会从怪物位置释放一条无强化的电弧：0，否；1，是", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3445": {"Id": 3445, "StringId": "hero_3_4_hit_skill_attr_division_initial_chance", "Remark": "电弧命中敌人时分裂无强化的电弧概率", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3446": {"Id": 3446, "StringId": "hero_3_4_hit_skill_attr_fire_dot_boolean", "Remark": "电弧命中是否附加引燃debuff：0，否；1，是", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3447": {"Id": 3447, "StringId": "hero_3_4_hit_skill_attr_fire_dot_chance", "Remark": "电弧命中附加引燃debuff概率", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3448": {"Id": 3448, "StringId": "hero_3_4_hit_skill_attr_fire_dot_buff_type", "Remark": "电弧命中附加引燃debuff类型", "SkillEffectType": 9, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 35, "Effect": 0}, "3449": {"Id": 3449, "StringId": "hero_3_4_hit_skill_attr_fire_dot_time", "Remark": "电弧命中附件引燃debuff时间，单位：秒,-1=一直", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3450": {"Id": 3450, "StringId": "hero_3_4_hit_skill_attr_path_dmg_boolean", "Remark": "电弧是否有路径伤害：0，否；1，是", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3451": {"Id": 3451, "StringId": "hero_3_4_hit_skill_attr_path_dmg_ratio", "Remark": "电弧路径伤害占基础伤害比例", "SkillEffectType": 9, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3501": {"Id": 3501, "StringId": "hero_3_5_hit_skill_attr_dmg_ratio", "Remark": "旋风基础伤害系数", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3502": {"Id": 3502, "StringId": "hero_3_5_hit_skill_attr_dmg_type", "Remark": "旋风技能伤害类型:风", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3503": {"Id": 3503, "StringId": "hero_3_5_hit_skill_attr_dmg_add_ratio", "Remark": "旋风基础伤害加法修正系数", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3504": {"Id": 3504, "StringId": "hero_3_5_hit_skill_attr_strike_dmg_add_ratio", "Remark": "旋风基础直击伤害加法修正系数", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3505": {"Id": 3505, "StringId": "hero_3_5_hit_skill_attr_dmg_radius", "Remark": "旋风半径：单位，米", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3506": {"Id": 3506, "StringId": "hero_3_5_hit_skill_attr_core_radius_ratio", "Remark": "旋风核心区域半径占当前旋风半径比例", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.55, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3507": {"Id": 3507, "StringId": "hero_3_5_hit_skill_attr_dmg_radius_add_ratio", "Remark": "旋风半径加法修正系数", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3508": {"Id": 3508, "StringId": "hero_3_5_hit_skill_attr_dmg_time", "Remark": "旋风时间：单位，秒", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3509": {"Id": 3509, "StringId": "hero_3_5_hit_skill_attr_dmg_time_add_ratio", "Remark": "旋风时间加法修正系数", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3510": {"Id": 3510, "StringId": "hero_3_5_hit_skill_attr_dmg_interval", "Remark": "旋风伤害间隔：单位，秒", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3511": {"Id": 3511, "StringId": "hero_3_5_hit_skill_attr_move_speed", "Remark": "旋风移动速度：单位，米/秒", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 6, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3512": {"Id": 3512, "StringId": "hero_3_5_hit_skill_attr_move_speed_add_ratio", "Remark": "旋风移动速度加法修正系数", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3513": {"Id": 3513, "StringId": "hero_3_5_hit_skill_attr_pull_type", "Remark": "旋风牵引debuff类型", "SkillEffectType": 14, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 3, "Effect": 0}, "3514": {"Id": 3514, "StringId": "hero_3_5_hit_skill_attr_pull_speed", "Remark": "旋风牵引速度：单位，米/秒", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 2.1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3515": {"Id": 3515, "StringId": "hero_3_5_hit_skill_attr_pull_speed_add_ratio", "Remark": "旋风牵引速度加法修正系数", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3516": {"Id": 3516, "StringId": "hero_3_5_hit_skill_attr_cnt", "Remark": "旋风数量", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3517": {"Id": 3517, "StringId": "hero_3_5_hit_skill_end_new_boolean", "Remark": "旋风结束后是否释放一个大型旋风：0，否；1，是", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3518": {"Id": 3518, "StringId": "hero_3_5_hit_skill_end_new_dmg_ratio", "Remark": "大型旋风伤害占当前伤害比例", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3519": {"Id": 3519, "StringId": "hero_3_5_hit_skill_end_new_radius_ratio", "Remark": "大型旋风半径占当前旋风半径比例", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1.1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3520": {"Id": 3520, "StringId": "hero_3_5_hit_skill_end_new_time", "Remark": "大型旋风时间：单位，秒", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3521": {"Id": 3521, "StringId": "hero_3_5_hit_skill_end_new_dmg_interval", "Remark": "大型旋风伤害间隔", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3522": {"Id": 3522, "StringId": "hero_3_5_hit_skill_end_new_pull_speed_ratio", "Remark": "大型旋风牵引速度占当前旋风牵引速度比例", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3523": {"Id": 3523, "StringId": "hero_3_5_hit_skill_extra_electric_dmg_boolean", "Remark": "旋风是否附带额外的电系伤害：0，否；1，是（闪电加农）", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3524": {"Id": 3524, "StringId": "hero_3_5_hit_skill_extra_electric_dmg_type", "Remark": "旋风附带额外的电系伤害类型", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3525": {"Id": 3525, "StringId": "hero_3_5_hit_skill_extra_electric_dmg_ratio", "Remark": "旋风附带电系占当前伤害比例", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.4, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3526": {"Id": 3526, "StringId": "hero_3_5_hit_skill_extra_electric_dmg_debuff_type", "Remark": "旋风附带的额外电系伤害会麻痹敌人", "SkillEffectType": 14, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 4, "Effect": 0}, "3527": {"Id": 3527, "StringId": "hero_3_5_hit_skill_extra_electric_dmg_debuff_time", "Remark": "旋风附带的额外电系伤害的麻痹时间：单位，秒", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3528": {"Id": 3528, "StringId": "hero_3_5_hit_skill_random_electric_boolean", "Remark": "闪电加农是否会随机电击附近的怪物：0，否；1，是", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3529": {"Id": 3529, "StringId": "hero_3_5_hit_skill_random_electric_dmg_interval", "Remark": "闪电加农随机电击附近的怪物间隔：单位，秒", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 1, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3530": {"Id": 3530, "StringId": "hero_3_5_hit_skill_random_electric_dmg_ratio", "Remark": "闪电加农随机电击附近怪物的伤害占基础伤害比例", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3531": {"Id": 3531, "StringId": "hero_3_5_hit_skill_random_electric_dmg_range", "Remark": "闪电加农随机电击附近怪物的半径：单位，米", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 40, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3532": {"Id": 3532, "StringId": "hero_3_5_hit_skill_attr_core_dmg_up_boolean", "Remark": "旋风核心区域是否会增伤", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3533": {"Id": 3533, "StringId": "hero_3_5_hit_skill_attr_core_dmg_up_ratio", "Remark": "旋风核心区域增伤比例（当前伤害x（1+核心加成比例）", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0.5, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3534": {"Id": 3534, "StringId": "hero_3_5_hit_skill_attr_debuff_pull_speed_add_boolean", "Remark": "旋风对有debuff的怪物牵引力是否翻倍：0，否；1，是", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3535": {"Id": 3535, "StringId": "hero_3_5_hit_skill_attr_debuff_pull_speed_add_ratio", "Remark": "旋风对有debuff的怪物牵引力额外加成（当前牵引速度直接x2）", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 2, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3536": {"Id": 3536, "StringId": "hero_3_5_hit_skill_model", "Remark": "旋风模型", "SkillEffectType": 14, "AttrDefaultType": 2, "OverlyingType": 0, "DefaultValue": 0, "BattleModel": 3501, "BuffType": 0, "Effect": 0}, "3537": {"Id": 3537, "StringId": "hero_3_5_hit_skill_end_new_model", "Remark": "大型旋风模型", "SkillEffectType": 14, "AttrDefaultType": 2, "OverlyingType": 0, "DefaultValue": 0, "BattleModel": 3502, "BuffType": 0, "Effect": 0}, "3538": {"Id": 3538, "StringId": "hero_3_5_hit_skill_electric_model", "Remark": "旋风电击模型", "SkillEffectType": 14, "AttrDefaultType": 2, "OverlyingType": 0, "DefaultValue": 0, "BattleModel": 3503, "BuffType": 0, "Effect": 0}, "3539": {"Id": 3539, "StringId": "hero_3_5_hit_skill_electric_cannon_model", "Remark": "旋风加农模型", "SkillEffectType": 14, "AttrDefaultType": 2, "OverlyingType": 0, "DefaultValue": 0, "BattleModel": 3504, "BuffType": 0, "Effect": 0}, "3540": {"Id": 3540, "StringId": "hero_3_5_hit_skill_attr_dot_burn_extra_dot_wind_boolean", "Remark": "旋风加农命中处于燃烧状态的目标时，附加风蚀dot：0，否；1，是(每层引燃独立赋予风蚀dot）", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3541": {"Id": 3541, "StringId": "hero_3_5_hit_skill_attr_dot_burn_extra_dot_wind_time", "Remark": "附加风蚀dot的时间：单位，秒", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3542": {"Id": 3542, "StringId": "hero_3_5_hit_skill_attr_dot_burn_extra_dot_wind_type", "Remark": "附加风蚀dot类型", "SkillEffectType": 14, "AttrDefaultType": 3, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 43, "Effect": 0}, "3543": {"Id": 3543, "StringId": "hero_3_5_hit_skill_attr_dot_burn_extra_dmg_max", "Remark": "风蚀buff伤害不能超过攻击力的倍数", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 3, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3544": {"Id": 3544, "StringId": "hero_3_5_hit_skill_attr_debuff_dmg_up_boolean", "Remark": "旋风加农对处于负面状态的目标，伤害增加", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 4, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}, "3545": {"Id": 3545, "StringId": "hero_3_5_hit_skill_attr_debuff_dmg_up_percent", "Remark": "旋风加农对处于负面状态的目标，伤害增加百分比", "SkillEffectType": 14, "AttrDefaultType": 1, "OverlyingType": 1, "DefaultValue": 0, "BattleModel": 0, "BuffType": 0, "Effect": 0}}