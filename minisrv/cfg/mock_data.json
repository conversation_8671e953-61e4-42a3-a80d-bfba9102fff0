{"Data": {"AchievementTable": "{\n\t\"Id\": 0,\n\t\"Group\": 0,\n\t\"Sub\": 0,\n\t\"TaskType\": 0,\n\t\"TaskCounterType\": 0,\n\t\"Formula\": \"\",\n\t\"Value\": 0,\n\t\"Reward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "ActivityTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Type\": 0\n}", "ArenaBotTable": "{\n\t\"Id\": 0,\n\t\"Name\": \"\",\n\t\"Avatar\": 0,\n\t\"Score\": 0,\n\t\"<PERSON>\": [\n\t\t{\n\t\t\t\"Hero\": 0,\n\t\t\t\"<PERSON>\": 0,\n\t\t\t\"<PERSON>\": 0,\n\t\t\t\"Skill\": [\n\t\t\t\t0\n\t\t\t],\n\t\t\t\"<PERSON>\": 0\n\t\t}\n\t],\n\t\"Equip\": [\n\t\t0\n\t],\n\t\"Gem\": [\n\t\t0\n\t]\n}", "ArenaChallengeRewardTable": "{\n\t\"Id\": 0,\n\t\"Win\": false,\n\t\"RewardType\": [\n\t\t0\n\t],\n\t\"RewardValue\": [\n\t\t0\n\t]\n}", "ArenaDailyRankRewardTable": "{\n\t\"Id\": 0,\n\t\"Rank\": {\n\t\t\"<PERSON>\": 0,\n\t\t\"<PERSON>\": 0\n\t},\n\t\"RewardType\": [\n\t\t0\n\t],\n\t\"RewardValue\": [\n\t\t0\n\t]\n}", "ArenaExtraChallengeCntTable": "{\n\t\"Id\": 0,\n\t\"Cnt\": 0,\n\t\"Price\": 0,\n\t\"IsMax\": false\n}", "ArenaMatchTable": "{\n\t\"Id\": 0,\n\t\"OwnRank\": {\n\t\t\"<PERSON>\": 0,\n\t\t\"<PERSON>\": 0\n\t},\n\t\"OppositeRank\": [\n\t\t{\n\t\t\t\"Min\": 0,\n\t\t\t\"<PERSON>\": 0\n\t\t}\n\t],\n\t\"OppositeWeight\": [\n\t\t0\n\t]\n}", "ArenaRefreshTable": "{\n\t\"Id\": 0,\n\t\"RefreshCnt\": {\n\t\t\"Min\": 0,\n\t\t\"<PERSON>\": 0\n\t},\n\t\"Price\": 0\n}", "ArenaScoreTable": "{\n\t\"Id\": 0,\n\t\"Score\": {\n\t\t\"<PERSON>\": 0,\n\t\t\"<PERSON>\": 0\n\t},\n\t\"Win\": 0,\n\t\"Fail\": 0\n}", "ArenaShopTable": "{\n\t\"Id\": 0,\n\t\"Period\": 0,\n\t\"Shop\": 0,\n\t\"Score\": 0,\n\t\"RewardType\": 0,\n\t\"RewardValue\": 0,\n\t\"Currency\": 0,\n\t\"Price\": 0,\n\t\"TimesLimit\": 0\n}", "ArenaWeeklyRankRewardTable": "{\n\t\"Id\": 0,\n\t\"Rank\": {\n\t\t\"<PERSON>\": 0,\n\t\t\"<PERSON>\": 0\n\t},\n\t\"RewardType\": [\n\t\t0\n\t],\n\t\"RewardValue\": [\n\t\t0\n\t]\n}", "AttributeHierarchyTable": "{\n\t\"Id\": 0\n}", "AvatarFrameTable": "{\n\t\"Id\": 0,\n\t\"Item\": 0,\n\t\"Benefits\": [\n\t\t{\n\t\t\t\"BenefitsID\": 0,\n\t\t\t\"BenefitsValue\": 0\n\t\t}\n\t],\n\t\"Name\": \"\"\n}", "AvatarTable": "{\n\t\"Id\": 0,\n\t\"Default\": false,\n\t\"Hero\": 0,\n\t\"Image\": \"\",\n\t\"Name\": \"\"\n}", "BattleAttributeTable": "{\n\t\"Id\": 0\n}", "BattleModelTable": "{\n\t\"Id\": 0\n}", "BenefitsCalcJustShowTable": "{\n\t\"Id\": 0,\n\t\"Formula\": 0,\n\t\"Param1\": [\n\t\t0\n\t],\n\t\"Param2\": [\n\t\t0\n\t],\n\t\"Param3\": [\n\t\t0\n\t]\n}", "BenefitsCalcTable": "{\n\t\"Id\": 0,\n\t\"Formula\": 0,\n\t\"Param1\": [\n\t\t0\n\t],\n\t\"Param2\": [\n\t\t0\n\t],\n\t\"Param3\": [\n\t\t0\n\t]\n}", "BenefitsTable": "{\n\t\"Id\": 0,\n\t\"Type\": \"\",\n\t\"Category\": \"\",\n\t\"HeroCareer\": 0,\n\t\"Negative\": false\n}", "BlackShopTable": "{\n\t\"Id\": 0,\n\t\"Shop\": 0,\n\t\"Level\": 0,\n\t\"RewardType\": 0,\n\t\"RewardValue\": 0,\n\t\"Currency\": 0,\n\t\"Price\": 0,\n\t\"TimesLimit\": 0\n}", "ChapterTaskMainTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Reward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "ChapterTaskTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Chapter\": 0,\n\t\"TaskType\": 0,\n\t\"TaskCounterType\": 0,\n\t\"Formula\": \"\",\n\t\"Value\": 0,\n\t\"Reward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "DailyTasksScoreTable": "{\n\t\"Id\": 0,\n\t\"Score\": 0,\n\t\"DailyOrWeekly\": 0,\n\t\"Reward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "DailyTasksTable": "{\n\t\"Id\": 0,\n\t\"TaskType\": 0,\n\t\"TaskCounterType\": 0,\n\t\"Formula\": \"\",\n\t\"Value\": 0,\n\t\"RewardType\": [\n\t\t0\n\t],\n\t\"RewardValue\": [\n\t\t0\n\t],\n\t\"Score\": 0\n}", "DaveLevelTable": "{\n\t\"Id\": 0,\n\t\"Level\": 0,\n\t\"IsMax\": false,\n\t\"LevelUp\": {\n\t\t\"CostType\": 0,\n\t\t\"CostValue\": 0\n\t},\n\t\"Atk\": 0\n}", "DropGroupTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"DropType\": 0\n}", "DropMainTable": "{\n\t\"Id\": 0,\n\t\"DropGroupId\": 0,\n\t\"Chance\": 0,\n\t\"Weight\": 0,\n\t\"ItemId\": 0,\n\t\"MinValue\": 0,\n\t\"MaxValue\": 0\n}", "DungeonChapterLevelTable": "{\n\t\"Id\": 0,\n\t\"Dungeon\": 0,\n\t\"Levels\": 0,\n\t\"Level\": 0,\n\t\"IsMax\": false,\n\t\"LevelUpCost\": {\n\t\t\"CostType\": 0,\n\t\t\"CostValue\": 0\n\t}\n}", "DungeonCoinLevelTable": "{\n\t\"Id\": 0,\n\t\"Dungeon\": 0,\n\t\"Level\": 0,\n\t\"IsMax\": false,\n\t\"FirstReward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t],\n\t\"Reward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "DungeonGeneLevelTable": "{\n\t\"Id\": 0,\n\t\"Dungeon\": 0,\n\t\"Level\": 0,\n\t\"IsMax\": false,\n\t\"FirstReward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t],\n\t\"Reward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "DungeonLordEquipLevelTable": "{\n\t\"Id\": 0,\n\t\"Dungeon\": 0,\n\t\"Level\": 0,\n\t\"IsMax\": false,\n\t\"FirstReward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t],\n\t\"Reward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "DungeonRefreshTable": "{\n\t\"Id\": 0,\n\t\"Dungeon\": 0,\n\t\"Levels\": 0,\n\t\"Wave\": 0,\n\t\"Reversionary\": 0,\n\t\"RefreshType\": 0,\n\t\"RefreshParamDelayTime\": [\n\t\t0\n\t],\n\t\"RefreshParamDeathCnt\": 0,\n\t\"MonsterId\": [\n\t\t0\n\t],\n\t\"MonsterCnt\": [\n\t\t0\n\t],\n\t\"Weights\": [\n\t\t0\n\t],\n\t\"Intervals\": [\n\t\t0\n\t]\n}", "DungeonSunshineLevelTable": "{\n\t\"Id\": 0,\n\t\"Dungeon\": 0,\n\t\"Level\": 0,\n\t\"IsMax\": false,\n\t\"FirstReward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t],\n\t\"Reward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "DungeonTypeTable": "{\n\t\"Id\": 0,\n\t\"DungeonType\": 0\n}", "FunctionPreviewTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Function\": 0\n}", "FunctionTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Level\": 0,\n\t\"Day\": 0,\n\t\"Logic\": 0\n}", "GameConfigs": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Value\": \"\"\n}", "GemAffixQualityTable": "{\n\t\"Id\": 0,\n\t\"GemAffixQuality\": 0,\n\t\"Weight\": 0\n}", "GemQualityTypeTable": "{\n\t\"Id\": 0,\n\t\"GemQualityType\": 0,\n\t\"Power\": 0,\n\t\"Desc\": \"\"\n}", "GoToTable": "{\n\t\"Id\": 0\n}", "GuildFlagTable": "{\n\t\"Id\": 0,\n\t\"Type\": 0,\n\t\"Image\": \"\",\n\t\"Unlock\": 0,\n\t\"Order\": 0,\n\t\"Default\": false\n}", "GuildHaggleTable": "{\n\t\"Id\": 0,\n\t\"Reward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t],\n\t\"RewardCost\": [\n\t\t0\n\t],\n\t\"HaggleLeast\": 0,\n\t\"HaggleMag\": 0,\n\t\"HaggleRate\": 0,\n\t\"RewardType\": 0,\n\t\"RewardValue\": 0\n}", "GuildLevelTable": "{\n\t\"Id\": 0,\n\t\"IsMaxLevel\": false,\n\t\"Level\": 0,\n\t\"Exp\": 0,\n\t\"Member\": 0\n}", "GuildPermissionTable": "{\n\t\"Id\": 0,\n\t\"Permission\": 0,\n\t\"CnBan\": false,\n\t\"GuildRank\": [\n\t\t0\n\t]\n}", "GuildRankTable": "{\n\t\"Id\": 0,\n\t\"Rank\": 0\n}", "GuildShopTable": "{\n\t\"Id\": 0,\n\t\"Period\": 0,\n\t\"Shop\": 0,\n\t\"GuildLevelLimit\": 0,\n\t\"RewardType\": 0,\n\t\"RewardValue\": 0,\n\t\"Currency\": 0,\n\t\"Price\": 0,\n\t\"TimesLimit\": 0\n}", "GuildTaskTable": "{\n\t\"Id\": 0,\n\t\"TaskType\": 0,\n\t\"TaskCounterType\": 0,\n\t\"Formula\": \"\",\n\t\"Value\": 0,\n\t\"RewardType\": [\n\t\t0\n\t],\n\t\"RewardValue\": [\n\t\t0\n\t],\n\t\"Score\": 0\n}", "GuildTasksScoreTable": "{\n\t\"Id\": 0,\n\t\"Score\": 0,\n\t\"RewardType\": [\n\t\t0\n\t],\n\t\"RewardValue\": [\n\t\t0\n\t]\n}", "HeroBondsTable": "{\n\t\"Id\": 0,\n\t\"Remark\": \"\",\n\t\"BondsBenefits\": [\n\t\t{\n\t\t\t\"BenefitsID\": 0,\n\t\t\t\"BenefitsValue\": 0\n\t\t}\n\t]\n}", "HeroCareerTable": "{\n\t\"Id\": 0\n}", "HeroConfigTable": "{\n\t\"Id\": 0,\n\t\"HeroConfig\": 0,\n\t\"Unlock\": 0\n}", "HeroElementTable": "{\n\t\"Id\": 0\n}", "HeroFragmentTable": "{\n\t\"Id\": 0,\n\t\"HeroFragment\": 0,\n\t\"Hero\": 0,\n\t\"ComposeCnt\": 0,\n\t\"DivisionCnt\": 0,\n\t\"CanUseUniversalFragmentExchange\": false,\n\t\"UniversalFragmentType\": 0\n}", "HeroGeneFragmentTable": "{\n\t\"Id\": 0,\n\t\"<PERSON>\": 0,\n\t\"HeroGeneralGeneFragment\": 0,\n\t\"HeroGeneFragment\": 0,\n\t\"Weight\": 0\n}", "HeroGeneTable": "{\n\t\"Id\": 0,\n\t\"HeroID\": 0,\n\t\"HeroGeneLevel\": 0,\n\t\"IsMax\": false,\n\t\"LevelUp\": [\n\t\t{\n\t\t\t\"CostType\": 0,\n\t\t\t\"CostValue\": 0\n\t\t}\n\t],\n\t\"Power\": 0,\n\t\"Attr\": {\n\t\t\"Atk\": 0,\n\t\t\"Def\": 0,\n\t\t\"Hp\": 0,\n\t\t\"CritChance\": 0,\n\t\t\"CritDmgUpPer\": 0,\n\t\t\"DmgUpPer\": 0,\n\t\t\"CritResistChance\": 0,\n\t\t\"BeCritDmgDownPer\": 0,\n\t\t\"BeDmgDownPer\": 0,\n\t\t\"CdRate\": 0,\n\t\t\"Benefits\": [\n\t\t\t{\n\t\t\t\t\"BenefitsID\": 0,\n\t\t\t\t\"BenefitsValue\": 0\n\t\t\t}\n\t\t]\n\t},\n\t\"AllHeroCritDmgUp\": 0,\n\t\"IsUnlockExtraSkillEffect\": false,\n\t\"RefHeroStarLevel\": 0,\n\t\"PVEPassiveSkillEffect\": [\n\t\t0\n\t]\n}", "HeroLevelTable": "{\n\t\"Id\": 0,\n\t\"PlanID\": 0,\n\t\"ReqDesc\": \"\",\n\t\"HeroLevel\": 0,\n\t\"IsMax\": false,\n\t\"LevelUp\": [\n\t\t{\n\t\t\t\"CostType\": 0,\n\t\t\t\"CostValue\": 0\n\t\t}\n\t],\n\t\"Power\": 0,\n\t\"Attr\": {\n\t\t\"Atk\": 0,\n\t\t\"Def\": 0,\n\t\t\"Hp\": 0,\n\t\t\"CritChance\": 0,\n\t\t\"CritDmgUpPer\": 0,\n\t\t\"DmgUpPer\": 0,\n\t\t\"CritResistChance\": 0,\n\t\t\"BeCritDmgDownPer\": 0,\n\t\t\"BeDmgDownPer\": 0,\n\t\t\"CdRate\": 0,\n\t\t\"Benefits\": [\n\t\t\t{\n\t\t\t\t\"BenefitsID\": 0,\n\t\t\t\t\"BenefitsValue\": 0\n\t\t\t}\n\t\t]\n\t}\n}", "HeroLotteryGroupTable": "{\n\t\"Id\": 0,\n\t\"FreeCD\": 0,\n\t\"DailyFreeTimesLimit\": 0,\n\t\"ImageBig\": \"\",\n\t\"ImageTag\": \"\",\n\t\"Bet\": [\n\t\t0\n\t],\n\t\"SingleDrawCostType\": 0,\n\t\"SingleDrawCostValue\": 0,\n\t\"SingleDrawCostDiamdondCnt\": 0\n}", "HeroLotteryMustTable": "{\n\t\"Id\": 0,\n\t\"HeroLotteryGroup\": 0,\n\t\"MustCnt\": 0,\n\t\"IncreaseCnt\": 0,\n\t\"Increment\": [\n\t\t0\n\t],\n\t\"IsRefresh\": false,\n\t\"Must11\": [\n\t\t0\n\t],\n\t\"MustSSR\": {\n\t\t\"Hero\": [\n\t\t\t0\n\t\t],\n\t\t\"Weight\": [\n\t\t\t0\n\t\t]\n\t},\n\t\"FirstHero\": [\n\t\t0\n\t],\n\t\"FirstHeroWeight\": [\n\t\t0\n\t],\n\t\"TenthHero\": [\n\t\t0\n\t],\n\t\"TenthHeroWeight\": [\n\t\t0\n\t]\n}", "HeroLotteryRandomGroupTable": "{\n\t\"Id\": 0,\n\t\"ChapterUnlockMin\": 0,\n\t\"ChapterUnlockMax\": 0\n}", "HeroLotteryRandomTable": "{\n\t\"Id\": 0,\n\t\"IsMust\": false,\n\t\"RandomGroup\": 0,\n\t\"HeroLotteryGroup\": 0,\n\t\"ItemType\": 0,\n\t\"ItemValue\": 0,\n\t\"Weight\": 0\n}", "HeroQualityTable": "{\n\t\"Id\": 0\n}", "HeroRestrainTable": "{\n\t\"Id\": 0,\n\t\"HeroTypeRestrainSide\": 0,\n\t\"HeroTypeRestrainedSide\": 0,\n\t\"RestrainDmgAddRatio\": 0\n}", "HeroSkillAttrTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Remark\": \"\",\n\t\"SkillEffectType\": 0,\n\t\"AttrDefaultType\": 0,\n\t\"OverlyingType\": 0,\n\t\"DefaultValue\": 0,\n\t\"BattleModel\": 0,\n\t\"BuffType\": 0,\n\t\"Effect\": 0\n}", "HeroSkillAwakeTable": "{\n\t\"Id\": 0,\n\t\"SkillGroupId\": 0,\n\t\"Level\": 0,\n\t\"Power\": 0,\n\t\"IsMax\": false,\n\t\"UnlockHeroStar\": 0,\n\t\"LevelUp\": [\n\t\t{\n\t\t\t\"CostType\": 0,\n\t\t\t\"CostValue\": 0\n\t\t}\n\t],\n\t\"TimeCharge\": 0,\n\t\"SkillCoolDown\": 0,\n\t\"SkillRange\": 0,\n\t\"SkillType\": 0,\n\t\"IsUnlockExtraSkillEffect\": false,\n\t\"ExtraEffect\": {\n\t\t\"UnlockExtraEffect\": false,\n\t\t\"HeroSkillEffect\": [\n\t\t\t0\n\t\t]\n\t},\n\t\"PVEPassiveSkillEffect\": [\n\t\t0\n\t],\n\t\"PassiveSkillEffect\": [\n\t\t0\n\t],\n\t\"ActiveSkillEffect\": [\n\t\t0\n\t]\n}", "HeroSkillBuffTable": "{\n\t\"Id\": 0,\n\t\"BuffType\": 0,\n\t\"IsRemove\": false,\n\t\"StackingType\": 0,\n\t\"StackingTimes\": 0,\n\t\"BuffEffectType\": 0,\n\t\"BenefitsParam\": [\n\t\t{\n\t\t\t\"BenefitsID\": 0,\n\t\t\t\"BenefitsValue\": 0\n\t\t}\n\t],\n\t\"LameParam\": 0,\n\t\"DotParam\": {\n\t\t\"DotDmgType\": 0,\n\t\t\"DotDmgValue\": 0,\n\t\t\"DotDmgInterval\": 0\n\t},\n\t\"ImmunityParam\": [\n\t\t0\n\t],\n\t\"HpRecovery\": {\n\t\t\"HpRecoveryIsNegative\": false,\n\t\t\"HpRecoveryType\": 0,\n\t\t\"HpRecoveryInterval\": 0,\n\t\t\"HpRecoveryValue\": 0\n\t},\n\t\"ShieldParam\": {\n\t\t\"ShiledHpRatio\": 0\n\t},\n\t\"RampageParam\": {\n\t\t\"Range\": 0,\n\t\t\"HpPer\": 0,\n\t\t\"Chance\": 0,\n\t\t\"Buff\": [\n\t\t\t0\n\t\t],\n\t\t\"Time\": [\n\t\t\t0\n\t\t]\n\t},\n\t\"BuffDelayParam\": {\n\t\t\"AllDebuff\": false,\n\t\t\"BuffType\": [\n\t\t\t0\n\t\t],\n\t\t\"DelayTimeAddRatio\": 0\n\t},\n\t\"CoolingOffParam\": {\n\t\t\"Hp\": 0,\n\t\t\"Stop\": false,\n\t\t\"Time\": 0,\n\t\t\"Buff\": 0\n\t},\n\t\"ArmourParam\": {\n\t\t\"Cnt\": 0\n\t},\n\t\"FrozenHpRecoveryParam\": {\n\t\t\"Cd\": 0,\n\t\t\"Limit\": 0,\n\t\t\"Time\": 0,\n\t\t\"Buff\": 0\n\t},\n\t\"ElectrostaticSputteringParam\": {\n\t\t\"Range\": {\n\t\t\t\"CircularRadius\": 0,\n\t\t\t\"CircularRadiusAdditionRatio\": 0\n\t\t},\n\t\t\"DmgRatio\": 0\n\t},\n\t\"InjuryHealingParam\": {\n\t\t\"Cd\": 0,\n\t\t\"Limit\": 0,\n\t\t\"Time\": 0,\n\t\t\"Buff\": 0\n\t},\n\t\"SummonParam\": {\n\t\t\"IsAtkedTrigger\": false,\n\t\t\"Cd\": 0,\n\t\t\"Limit\": 0,\n\t\t\"Ratio\": 0,\n\t\t\"MonsterId\": [\n\t\t\t0\n\t\t],\n\t\t\"MonsterCnt\": [\n\t\t\t0\n\t\t]\n\t},\n\t\"CrabWalkParam\": {\n\t\t\"Ratio\": 0,\n\t\t\"HorizontalMoveTimeMin\": 0,\n\t\t\"HorizontalMoveTimeMax\": 0\n\t},\n\t\"InvulnerableParam\": {\n\t\t\"HpPer\": [\n\t\t\t0\n\t\t],\n\t\t\"Time\": [\n\t\t\t0\n\t\t]\n\t},\n\t\"SplitParam\": {\n\t\t\"MonsterType\": [\n\t\t\t0\n\t\t],\n\t\t\"MonsterCnt\": [\n\t\t\t0\n\t\t],\n\t\t\"Ratio\": [\n\t\t\t0\n\t\t]\n\t},\n\t\"LifeStealParam\": {\n\t\t\"LifeStealType\": 0,\n\t\t\"LifeStealValue\": 0\n\t},\n\t\"InstantDeathParam\": {\n\t\t\"Time\": 0\n\t},\n\t\"TombStoneParam\": {\n\t\t\"Ratio\": 0,\n\t\t\"MonsterId\": 0\n\t},\n\t\"ReviveParam\": {\n\t\t\"LostHpPer\": 0,\n\t\t\"Cnt\": 0\n\t},\n\t\"RangeRampageParam\": {\n\t\t\"Range\": {\n\t\t\t\"CircularRadius\": 0,\n\t\t\t\"CircularRadiusAdditionRatio\": 0\n\t\t},\n\t\t\"Buff\": [\n\t\t\t0\n\t\t],\n\t\t\"Time\": [\n\t\t\t0\n\t\t]\n\t},\n\t\"TriggerParam\": {\n\t\t\"IsAtkedTrigger\": false,\n\t\t\"Cd\": 0,\n\t\t\"Limit\": 0,\n\t\t\"Buff\": 0,\n\t\t\"Time\": 0\n\t},\n\t\"ConditionTriggerParam\": {\n\t\t\"Target\": 0,\n\t\t\"Hp\": 0,\n\t\t\"Buff\": 0,\n\t\t\"Time\": 0\n\t},\n\t\"ImmolateParam\": {\n\t\t\"Sacrifice\": 0,\n\t\t\"Recipient\": 0,\n\t\t\"Buff\": 0\n\t},\n\t\"HpSwitchParam\": {\n\t\t\"LostHpPer\": 0,\n\t\t\"Buff\": 0\n\t},\n\t\"StepingStoneParam\": {\n\t\t\"Giver\": 0,\n\t\t\"GiverDmgRatio\": 0,\n\t\t\"Recipient\": 0,\n\t\t\"Buff\": 0\n\t},\n\t\"EDERParam\": {\n\t\t\"Hp\": 0\n\t},\n\t\"ShippudenParam\": 0\n}", "HeroSkillBuffTypeTable": "{\n\t\"Id\": 0,\n\t\"BuffType\": 0\n}", "HeroSkillEffectTable": "{\n\t\"Id\": 0,\n\t\"SkillEffectType\": 0,\n\t\"SkillAttr\": [\n\t\t{\n\t\t\t\"SkillAttr\": 0,\n\t\t\t\"SkillAttrParam\": 0\n\t\t}\n\t],\n\t\"BuffParam\": {\n\t\t\"Target\": 0,\n\t\t\"Chance\": 0,\n\t\t\"BuffType\": 0,\n\t\t\"BuffTime\": 0\n\t},\n\t\"AoeParam\": {\n\t\t\"Target\": 0,\n\t\t\"Delay\": 0,\n\t\t\"Time\": 0,\n\t\t\"DmgInterval\": 0,\n\t\t\"DmgType\": 0,\n\t\t\"DmgValue\": 0\n\t},\n\t\"RougeTab\": 0,\n\t\"RougeTabReplace\": 0,\n\t\"RougeTabReplaced\": 0,\n\t\"RougeTabUnlock\": [\n\t\t0\n\t],\n\t\"BossEarLaserParam\": {\n\t\t\"Target\": 0,\n\t\t\"DmgRatio\": 0,\n\t\t\"Time\": 0,\n\t\t\"Times\": 0,\n\t\t\"CastTime\": 0\n\t},\n\t\"BossMouseLaserParam\": {\n\t\t\"Target\": 0,\n\t\t\"DmgRatio\": 0,\n\t\t\"Time\": 0,\n\t\t\"Times\": 0,\n\t\t\"CastTime\": 0\n\t},\n\t\"HomelanderLaserParam\": {\n\t\t\"Target\": 0,\n\t\t\"DmgRatio\": 0,\n\t\t\"Time\": 0,\n\t\t\"Interval\": 0,\n\t\t\"CastTime\": 0\n\t},\n\t\"MissileParam\": {\n\t\t\"Target\": 0,\n\t\t\"DmgRatio\": 0\n\t},\n\t\"MissileSpecialEffects\": \"\",\n\t\"MeleeParam\": {\n\t\t\"Target\": 0,\n\t\t\"DmgRatio\": 0\n\t},\n\t\"Boss2Param\": {\n\t\t\"Target\": 0,\n\t\t\"DmgRatio\": 0\n\t},\n\t\"MultipleMeleeParam\": {\n\t\t\"Target\": 0,\n\t\t\"DmgRatio\": 0,\n\t\t\"Cnt\": 0\n\t},\n\t\"SuicideBombingParam\": {\n\t\t\"Target\": 0,\n\t\t\"DmgRatio\": 0,\n\t\t\"RangeParam\": {\n\t\t\t\"CircularRadius\": 0,\n\t\t\t\"CircularRadiusAdditionRatio\": 0\n\t\t}\n\t},\n\t\"Phase2Boss1Param\": {\n\t\t\"Target\": 0,\n\t\t\"DmgRatio\": 0\n\t},\n\t\"Phase2Boss4Param\": {\n\t\t\"Target\": 0,\n\t\t\"DmgRatio\": 0\n\t}\n}", "HeroSkillGroupTable": "{\n\t\"Id\": 0,\n\t\"HeroId\": 0,\n\t\"HitSkillType\": 0,\n\t\"MonsterPosType\": [\n\t\t0\n\t],\n\t\"IsBallistic\": false,\n\t\"UnlockHeroLevel\": 0,\n\t\"UnlockHeroStar\": 0,\n\t\"HeroSkillType\": 0\n}", "HeroSkillTypeTable": "{\n\t\"Id\": 0\n}", "HeroStarTable": "{\n\t\"Id\": 0,\n\t\"PlanID\": 0,\n\t\"HeroStarLevel\": 0,\n\t\"HeroSkillLevelLimit\": 0,\n\t\"IsMax\": false,\n\t\"StarUpCostValue\": 0,\n\t\"StarUpDifference\": 0,\n\t\"StarUpCommonItem\": \"\",\n\t\"StarUpCommonItemNum\": 0,\n\t\"IsGradeUp\": false,\n\t\"GradeUpCostValue\": 0,\n\t\"HeroQuality\": 0,\n\t\"Power\": 0,\n\t\"Attr\": {\n\t\t\"Atk\": 0,\n\t\t\"Def\": 0,\n\t\t\"Hp\": 0,\n\t\t\"CritChance\": 0,\n\t\t\"CritDmgUpPer\": 0,\n\t\t\"DmgUpPer\": 0,\n\t\t\"CritResistChance\": 0,\n\t\t\"BeCritDmgDownPer\": 0,\n\t\t\"BeDmgDownPer\": 0,\n\t\t\"CdRate\": 0,\n\t\t\"Benefits\": [\n\t\t\t{\n\t\t\t\t\"BenefitsID\": 0,\n\t\t\t\t\"BenefitsValue\": 0\n\t\t\t}\n\t\t]\n\t}\n}", "HeroTable": "{\n\t\"Id\": 0,\n\t\"ItemId\": 0,\n\t\"ItemGeneId\": 0,\n\t\"StarUpCostItem\": 0,\n\t\"GradeUpCostItem\": 0,\n\t\"StarPlanID\": 0,\n\t\"LevelPlanID\": 0,\n\t\"HeroCareer\": 0,\n\t\"HeroElement\": 0,\n\t\"HeroQuality\": 0,\n\t\"HeroType\": 0,\n\t\"HasAtkFirstCareer\": false,\n\t\"AtkFirstCareer\": 0,\n\t\"SkillRectangle\": 0,\n\t\"CollisionRadius\": 0,\n\t\"HitSkill\": 0,\n\t\"NegativeSkill\": 0,\n\t\"GiftSkill\": 0,\n\t\"GiftSkillBenefits\": [\n\t\t{\n\t\t\t\"BenefitsID\": 0,\n\t\t\t\"BenefitsValue\": 0\n\t\t}\n\t]\n}", "HeroTypeTable": "{\n\t\"Id\": 0,\n\t\"HeroType\": 0,\n\t\"Name\": \"\",\n\t\"Image\": \"\"\n}", "Iap1stTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"IapPackageId\": 0,\n\t\"Priority\": 0,\n\t\"Limit\": 0,\n\t\"Times\": 0,\n\t\"D2\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t],\n\t\"D3\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "Iap2XTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"IapPackageId\": 0,\n\t\"Limit\": 0,\n\t\"Times\": 0\n}", "IapAdFreeTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"IapPackageId\": 0,\n\t\"Limit\": 0,\n\t\"Times\": 0\n}", "IapBPTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"IapPackageId\": 0\n}", "IapBpRewardTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Bp\": 0,\n\t\"Level\": 0,\n\t\"Exp\": 0,\n\t\"RewardFree\": {\n\t\t\"RewardType\": 0,\n\t\t\"RewardValue\": 0\n\t}\n}", "IapDailySaleFreeRewardTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Reset\": 0,\n\t\"Times\": 0,\n\t\"Reward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "IapDailySaleRewardGroupTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Hero\": 0,\n\t\"Gift\": 0,\n\t\"Unlock\": 0\n}", "IapDailySaleRewardTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Group\": 0,\n\t\"IapPackageId\": 0,\n\t\"IapPackageOrder\": 0,\n\t\"IsPack\": false,\n\t\"Limit\": 0,\n\t\"Times\": 0\n}", "IapDailySaleTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\"\n}", "IapDealTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Type\": 0\n}", "IapLevelFundRewardTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Fund\": 0,\n\t\"Level\": 0,\n\t\"RewardFree\": {\n\t\t\"RewardType\": 0,\n\t\t\"RewardValue\": 0\n\t},\n\t\"RewardVip\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "IapLevelFundTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Unlock\": 0,\n\t\"Stage\": 0,\n\t\"IapPackageId\": 0,\n\t\"Limit\": 0,\n\t\"Times\": 0\n}", "IapLifeCardTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Benefits\": [\n\t\t0\n\t],\n\t\"BenefitsValue\": [\n\t\t0\n\t],\n\t\"IapPackageId\": 0,\n\t\"Limit\": 0,\n\t\"Times\": 0,\n\t\"RewardDaily\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "IapMonthCardTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Benefits\": [\n\t\t0\n\t],\n\t\"BenefitsValue\": [\n\t\t0\n\t],\n\t\"IapPackageId\": 0,\n\t\"Limit\": 0,\n\t\"Times\": 0,\n\t\"RewardDaily\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "IapPackageDiamondShopTable": "{\n\t\"Id\": 0,\n\t\"IapPackageId\": 0,\n\t\"FirstDoubleReward\": false\n}", "IapPackageRewardTable": "{\n\t\"Id\": 0,\n\t\"RewardType\": [\n\t\t0\n\t],\n\t\"RewardValue\": [\n\t\t0\n\t]\n}", "IapPackageTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Remark\": \"\",\n\t\"IapPackageType\": 0,\n\t\"PayID\": 0,\n\t\"IapPackageRewardId\": 0\n}", "IapPriceTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Price\": \"\",\n\t\"Cny\": 0,\n\t\"Krw\": 0,\n\t\"<PERSON>\": 0\n}", "IapRegularPackGroupTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Reset\": 0\n}", "IapRegularPackTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Group\": 0,\n\t\"IapPackageId\": 0,\n\t\"Times\": 0\n}", "IapShopMallTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Type\": 0\n}", "IapSignRewardTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Sign\": 0,\n\t\"Day\": 0,\n\t\"RewardFree\": {\n\t\t\"RewardType\": 0,\n\t\t\"RewardValue\": 0\n\t}\n}", "IapSignTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"IapPackageId\": 0,\n\t\"Limit\": 0,\n\t\"Times\": 0\n}", "IapTriggerPackGroupTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"TriggerId\": 0,\n\t\"TriggerInterval\": 0,\n\t\"TriggerPackType\": 0,\n\t\"Formula\": \"\",\n\t\"RechargeDay\": 0,\n\t\"RechargeFormula\": \"\"\n}", "IapTriggerPackTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Group\": 0,\n\t\"IapPackageId\": 0,\n\t\"Times\": 0\n}", "IapTurnPackTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"IsFree\": false,\n\t\"IapPackageId\": 0,\n\t\"Limit\": 0,\n\t\"Times\": 0,\n\t\"FreeReward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "IdleMonsterTable": "{\n\t\"Id\": 0,\n\t\"Chapters\": 0,\n\t\"Wave\": 0,\n\t\"Control\": \"\",\n\t\"RefreshType\": 0,\n\t\"RefreshParam\": {\n\t\t\"DelayTime\": 0,\n\t\t\"DeathCnt\": 0\n\t},\n\t\"Monster\": {\n\t\t\"MonsterId\": [\n\t\t\t0\n\t\t],\n\t\t\"MonsterCnt\": [\n\t\t\t0\n\t\t]\n\t}\n}", "IdleRewardTable": "{\n\t\"Id\": 0,\n\t\"Level\": 0,\n\t\"Reward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t],\n\t\"ExtraRewardDrop\": 0\n}", "IdleRewardTime": "{\n\t\"Id\": 0,\n\t\"MinTime\": 0,\n\t\"MaxTime\": 0,\n\t\"DefaultReward1RefreshTime\": 0,\n\t\"DefaultReward2RefreshTime\": 0,\n\t\"DefaultReward3RefreshTime\": 0,\n\t\"DefaultReward4RefreshTime\": 0,\n\t\"DefaultReward5RefreshTime\": 0,\n\t\"DefaultReward6RefreshTime\": 0,\n\t\"ExtraRewardRefreshTime\": 0\n}", "ItemQualityTable": "{\n\t\"Id\": 0\n}", "ItemSourceTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Name\": \"\",\n\t\"Desc\": \"\"\n}", "ItemTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Type\": 0,\n\t\"Value\": 0,\n\t\"Benefits\": [\n\t\t0\n\t],\n\t\"BenefitsValue\": [\n\t\t0\n\t],\n\t\"Quality\": 0,\n\t\"AutoUse\": false,\n\t\"RepeatAutoTransform\": false,\n\t\"ChestDropGroup\": 0,\n\t\"ChestSelect\": 0,\n\t\"MaxUseCnt\": 0,\n\t\"IsDiamond\": false,\n\t\"DiamondExchange\": false,\n\t\"DiamondCnt\": 0,\n\t\"ShowSource\": false,\n\t\"ItemSource\": [\n\t\t0\n\t],\n\t\"InBag\": false,\n\t\"BagType\": 0\n}", "LanguageCnTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Key\": \"\"\n}", "LevelShopTable": "{\n\t\"Id\": 0,\n\t\"Shop\": 0,\n\t\"Level\": 0,\n\t\"IsFree\": false,\n\t\"RewardType\": 0,\n\t\"RewardValue\": 0,\n\t\"Currency\": 0,\n\t\"Price\": 0,\n\t\"TimesLimit\": 0\n}", "LoginOpenTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Function\": 0\n}", "LordEquipGradeTable": "{\n\t\"Id\": 0,\n\t\"LordEquipType\": 0,\n\t\"Grade\": 0,\n\t\"IsMax\": false,\n\t\"BasePower\": 0,\n\t\"ExtraPower\": 0,\n\t\"LevelUp\": [\n\t\t{\n\t\t\t\"CostType\": 0,\n\t\t\t\"CostValue\": 0\n\t\t}\n\t],\n\t\"UnlockLevel\": 0,\n\t\"Attr\": {\n\t\t\"Atk\": 0,\n\t\t\"Def\": 0,\n\t\t\"Hp\": 0,\n\t\t\"CritChance\": 0,\n\t\t\"CritDmgUpPer\": 0,\n\t\t\"DmgUpPer\": 0,\n\t\t\"CritResistChance\": 0,\n\t\t\"BeCritDmgDownPer\": 0,\n\t\t\"BeDmgDownPer\": 0,\n\t\t\"CdRate\": 0,\n\t\t\"Benefits\": [\n\t\t\t{\n\t\t\t\t\"BenefitsID\": 0,\n\t\t\t\t\"BenefitsValue\": 0\n\t\t\t}\n\t\t]\n\t},\n\t\"ExtraAttr\": {\n\t\t\"Atk\": 0,\n\t\t\"Def\": 0,\n\t\t\"Hp\": 0,\n\t\t\"CritChance\": 0,\n\t\t\"CritDmgUpPer\": 0,\n\t\t\"DmgUpPer\": 0,\n\t\t\"CritResistChance\": 0,\n\t\t\"BeCritDmgDownPer\": 0,\n\t\t\"BeDmgDownPer\": 0,\n\t\t\"CdRate\": 0,\n\t\t\"Benefits\": [\n\t\t\t{\n\t\t\t\t\"BenefitsID\": 0,\n\t\t\t\t\"BenefitsValue\": 0\n\t\t\t}\n\t\t]\n\t}\n}", "LordEquipGradeTypeTable": "{\n\t\"Id\": 0,\n\t\"LordEquipGrade\": 0\n}", "LordEquipSlotsTable": "{\n\t\"Id\": 0,\n\t\"Level\": 0\n}", "LordEquipTable": "{\n\t\"Id\": 0,\n\t\"LordEquipType\": 0,\n\t\"RefGrade\": 0,\n\t\"IsMax\": false,\n\t\"Level\": 0,\n\t\"LevelUp\": [\n\t\t{\n\t\t\t\"CostType\": 0,\n\t\t\t\"CostValue\": 0\n\t\t}\n\t],\n\t\"Attr\": {\n\t\t\"Atk\": 0,\n\t\t\"Def\": 0,\n\t\t\"Hp\": 0,\n\t\t\"CritChance\": 0,\n\t\t\"CritDmgUpPer\": 0,\n\t\t\"DmgUpPer\": 0,\n\t\t\"CritResistChance\": 0,\n\t\t\"BeCritDmgDownPer\": 0,\n\t\t\"BeDmgDownPer\": 0,\n\t\t\"CdRate\": 0,\n\t\t\"Benefits\": [\n\t\t\t{\n\t\t\t\t\"BenefitsID\": 0,\n\t\t\t\t\"BenefitsValue\": 0\n\t\t\t}\n\t\t]\n\t}\n}", "LordEquipTypeTable": "{\n\t\"Id\": 0,\n\t\"LordEquipType\": 0,\n\t\"Weight\": 0\n}", "LordGemCraftTable": "{\n\t\"Id\": 0,\n\t\"PreCraftQuality\": 0,\n\t\"PostCraftQuality\": 0,\n\t\"GemCnt\": 0\n}", "LordGemDropCntTable": "{\n\t\"Id\": 0,\n\t\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\": 0,\n\t\"<PERSON><PERSON><PERSON>Cnt\": 0,\n\t\"LordGemCntWeight\": 0\n}", "LordGemDropQualityTable": "{\n\t\"Id\": 0,\n\t\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\": 0,\n\t\"LordGemQuality\": 0,\n\t\"LordGemQualityWeight\": 0\n}", "LordGemRandomGroupChanceTable": "{\n\t\"Id\": 0,\n\t\"LordGemRandomGroup\": 0,\n\t\"LordGemRandomRewardGroup\": 0,\n\t\"GemQualityType\": 0,\n\t\"Weight\": 0,\n\t\"Cnt\": 0\n}", "LordGemRandomGroupMustTable": "{\n\t\"Id\": 0,\n\t\"LordGemRandomGroup\": 0,\n\t\"MustCnt\": 0,\n\t\"GemQualityType\": 0,\n\t\"Cnt\": 0,\n\t\"IsRefresh\": false\n}", "LordGemRandomGroupTable": "{\n\t\"Id\": 0,\n\t\"FreeCD\": 0,\n\t\"DailyFreeTimesLimit\": 0,\n\t\"ImageBig\": \"\",\n\t\"ImageTag\": \"\",\n\t\"Bet\": [\n\t\t0\n\t],\n\t\"SingleDrawCostType\": 0,\n\t\"SingleDrawCostValue\": 0,\n\t\"SingleDrawCostDiamdondCnt\": 0\n}", "LordGemRandomRewardGroupTable": "{\n\t\"Id\": 0,\n\t\"LordGemRandomGroup\": 0,\n\t\"MinLevel\": 0,\n\t\"MaxLevel\": 0\n}", "LordGemReforgeTable": "{\n\t\"Id\": 0,\n\t\"GemQualityType\": 0,\n\t\"CanReforge\": false,\n\t\"Reforge\": {\n\t\t\"CostType\": 0,\n\t\t\"CostValue\": 0\n\t}\n}", "LordGemTable": "{\n\t\"Id\": 0,\n\t\"Item\": 0,\n\t\"Hero\": [\n\t\t0\n\t],\n\t\"Image\": \"\",\n\t\"GemAffixId\": 0,\n\t\"LordEquipType\": 0,\n\t\"GemQualityType\": 0,\n\t\"GemAffixQuality\": 0,\n\t\"Modifier\": 0,\n\t\"Weight\": 0\n}", "MailTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Title\": \"\",\n\t\"Subject\": \"\",\n\t\"Image\": \"\"\n}", "MainChapterLevelTable": "{\n\t\"Id\": 0,\n\t\"Chapter\": 0,\n\t\"Level\": 0,\n\t\"IsMax\": false,\n\t\"LevelUpCost\": {\n\t\t\"CostType\": 0,\n\t\t\"CostValue\": 0\n\t}\n}", "MainChapterTable": "{\n\t\"Id\": 0\n}", "MainLevelPassRewardTable": "{\n\t\"Id\": 0,\n\t\"MainLevel\": 0,\n\t\"Star\": 0,\n\t\"Reward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "MainLevelRangeDmgTable": "{\n\t\"Id\": 0,\n\t\"Range\": [\n\t\t0\n\t],\n\t\"DmgRatio\": 0\n}", "MainLevelRewardRatioTable": "{\n\t\"Id\": 0,\n\t\"MaxRougeLevel\": 0,\n\t\"RewardRatio\": 0\n}", "MainLevelRewardTable": "{\n\t\"Id\": 0,\n\t\"MainLevel\": 0,\n\t\"IsElite\": false,\n\t\"Reward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "MainLevelRogueRewardWeightTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"KillRewardRougeTabCnt\": [\n\t\t0\n\t],\n\t\"KillRewardRougeTabCntWeight\": [\n\t\t0\n\t]\n}", "MainLevelTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"IsMaxLevel\": false,\n\t\"Chapter\": 0,\n\t\"Level\": 0,\n\t\"EliteMonsterAtkRatio\": 0,\n\t\"EliteMonsterDefRatio\": 0,\n\t\"EliteMonsterHpRatio\": 0,\n\t\"LevelType\": 0,\n\t\"KillRewardRougeTabCntScheme\": 0,\n\t\"CommonChallengeReward\": 0,\n\t\"EliteChallengeReward\": 0,\n\t\"OneStarReward\": 0,\n\t\"TwoStarReward\": 0,\n\t\"ThreeStarReward\": 0,\n\t\"ExtraReward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "MainLineTasksTable": "{\n\t\"Id\": 0,\n\t\"ReqMainLineTasks\": 0,\n\t\"TaskType\": 0,\n\t\"TaskCounterType\": 0,\n\t\"Formula\": \"\",\n\t\"Value\": 0,\n\t\"RewardType\": 0,\n\t\"RewardValue\": 0,\n\t\"GuideTrigger\": false\n}", "MapEventBuffTable": "{\n\t\"Id\": 0,\n\t\"BuffType\": 0,\n\t\"BuffTime\": 0\n}", "MapEventMonsterGroupTable": "{\n\t\"Id\": 0,\n\t\"MonsterWaveGroup\": [\n\t\t0\n\t]\n}", "MapEventMonsterTable": "{\n\t\"Id\": 0,\n\t\"Chapters\": 0,\n\t\"Levels\": 0,\n\t\"Wave\": 0,\n\t\"RefreshType\": 0,\n\t\"RefreshParam\": {\n\t\t\"DelayTime\": 0,\n\t\t\"DeathCnt\": 0\n\t},\n\t\"Monster\": {\n\t\t\"MonsterId\": [\n\t\t\t0\n\t\t],\n\t\t\"MonsterCnt\": [\n\t\t\t0\n\t\t]\n\t}\n}", "MapEventObstacleTable": "{\n\t\"Id\": 0,\n\t\"Effect\": 0\n}", "MapEventPropTable": "{\n\t\"Id\": 0,\n\t\"Effect\": 0\n}", "MapEventRewardTable": "{\n\t\"Id\": 0,\n\t\"Reward\": {\n\t\t\"RewardType\": 0,\n\t\t\"RewardValue\": 0\n\t}\n}", "MapEventSkillTable": "{\n\t\"Id\": 0,\n\t\"MapEventType\": 0,\n\t\"SkillCoolDown\": 0,\n\t\"PassiveSkillEffect\": [\n\t\t0\n\t],\n\t\"ActiveSkillEffect\": [\n\t\t0\n\t]\n}", "MapEventTable": "{\n\t\"Id\": 0,\n\t\"MapEventType\": 0,\n\t\"Monster\": 0,\n\t\"Prop\": 0,\n\t\"Buff\": 0,\n\t\"Obstacle\": 0,\n\t\"<PERSON>\": 0\n}", "MapRefreshMonsterEventTable": "{\n\t\"Id\": 0,\n\t\"Chapters\": 0,\n\t\"Levels\": 0,\n\t\"Wave\": 0,\n\t\"SpeedRatio\": 0,\n\t\"HpRatio\": 0,\n\t\"AtkRatio\": 0,\n\t\"DefRatio\": 0,\n\t\"Reversionary\": 0,\n\t\"RefreshType\": 0,\n\t\"RefreshParamDelayTime\": [\n\t\t0\n\t],\n\t\"RefreshParamDeathCnt\": 0,\n\t\"MonsterId\": [\n\t\t0\n\t],\n\t\"MonsterCnt\": [\n\t\t0\n\t],\n\t\"Weights\": [\n\t\t0\n\t],\n\t\"Intervals\": [\n\t\t0\n\t]\n}", "ModifierTable": "{\n\t\"Id\": 0,\n\t\"<PERSON>\": 0,\n\t\"IsAllHero\": false,\n\t\"AddBuff\": 0,\n\t\"RougeCorrect\": [\n\t\t{\n\t\t\t\"CorrectType\": 0,\n\t\t\t\"CorrectSkillAttr\": 0,\n\t\t\t\"CorrectSkillValue\": 0,\n\t\t\t\"CorrectSkillModel\": 0,\n\t\t\t\"CorrectSkillBuff\": 0,\n\t\t\t\"CorrectSkillEffect\": 0\n\t\t}\n\t],\n\t\"RougeCorrectBenefits\": [\n\t\t{\n\t\t\t\"CorrectType\": 0,\n\t\t\t\"CorrectBenefits\": 0,\n\t\t\t\"CorrectSkillValue\": 0\n\t\t}\n\t],\n\t\"RougeCorrectPlayerBenefits\": {\n\t\t\"CorrectType\": 0,\n\t\t\"CorrectBenefits\": 0,\n\t\t\"CorrectSkillValue\": 0\n\t}\n}", "MonsterCareerTable": "{\n\t\"Id\": 0,\n\t\"MonsterGrade\": 0\n}", "MonsterGradeTable": "{\n\t\"Id\": 0,\n\t\"MonsterGrade\": 0\n}", "MonsterPosTypeTable": "{\n\t\"Id\": 0,\n\t\"MonsterPosType\": 0\n}", "MonsterPreviewSchemeTable": "{\n\t\"Id\": 0\n}", "MonsterSkillTable": "{\n\t\"Id\": 0,\n\t\"SkillCoolDown\": 0,\n\t\"SkillRange\": 0,\n\t\"SkillType\": 0,\n\t\"PassiveSkillEffect\": [\n\t\t0\n\t],\n\t\"ActiveSkillEffect\": [\n\t\t0\n\t]\n}", "MonsterTable": "{\n\t\"Id\": 0,\n\t\"MonsterType\": 0,\n\t\"MonsterLevel\": 0,\n\t\"DropGroupId\": 0,\n\t\"Power\": 0,\n\t\"Hp\": 0,\n\t\"Atk\": 0,\n\t\"Def\": 0,\n\t\"CritChance\": 0,\n\t\"CritDmgUpPer\": 0,\n\t\"CdRate\": 0,\n\t\"DmgUpPer\": 0,\n\t\"CritResistChance\": 0,\n\t\"BeCritDmgDownPer\": 0,\n\t\"BeDmgDownPer\": 0\n}", "MonsterTypeTable": "{\n\t\"Id\": 0,\n\t\"MonsterGrade\": 0,\n\t\"MonsterSkill\": [\n\t\t0\n\t],\n\t\"CollisionRadius\": 0,\n\t\"UnlockReward\": {\n\t\t\"RewardType\": 0,\n\t\t\"RewardValue\": 0\n\t}\n}", "NewbieTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Force\": false,\n\t\"BIName\": \"\"\n}", "NpcDialogueTable": "{\n\t\"Id\": 0\n}", "PhotovoltaicTable": "{\n\t\"Id\": 0,\n\t\"EnergyType\": 0,\n\t\"EnergyTime\": [\n\t\t0\n\t],\n\t\"EnergyCnt\": 0,\n\t\"EnergyLimit\": 0\n}", "PresetsTable": "{\n\t\"Id\": 0,\n\t\"ItemId\": 0,\n\t\"Cnt\": 0\n}", "RankMainTable": "{\n\t\"Id\": 0,\n\t\"RankName\": \"\",\n\t\"RankLen\": 0\n}", "RankRewardTable": "{\n\t\"Id\": 0,\n\t\"Level\": 0,\n\t\"Reward\": {\n\t\t\"RewardType\": 0,\n\t\t\"RewardValue\": 0\n\t}\n}", "RougeRefreshTable": "{\n\t\"Id\": 0,\n\t\"AdFreeRefreshCnt\": 0,\n\t\"DiamondRefreshCnt\": 0,\n\t\"DiamondRefreshCost\": {\n\t\t\"CostType\": 0,\n\t\t\"CostValue\": 0\n\t}\n}", "RougeTabEffectTable": "{\n\t\"Id\": 0,\n\t\"<PERSON>\": 0,\n\t\"IsAllHero\": false,\n\t\"RougeCorrect\": [\n\t\t{\n\t\t\t\"CorrectType\": 0,\n\t\t\t\"CorrectSkillAttr\": 0,\n\t\t\t\"CorrectSkillValue\": 0,\n\t\t\t\"CorrectSkillModel\": 0,\n\t\t\t\"CorrectSkillBuff\": 0,\n\t\t\t\"CorrectSkillEffect\": 0\n\t\t}\n\t],\n\t\"RougeCorrectBenefits\": [\n\t\t{\n\t\t\t\"CorrectType\": 0,\n\t\t\t\"CorrectBenefits\": 0,\n\t\t\t\"CorrectSkillValue\": 0\n\t\t}\n\t]\n}", "RougeTabGroupRandomTable": "{\n\t\"Id\": 0,\n\t\"HeroDeployedCnt\": 0,\n\t\"Chance\": 0\n}", "RougeTabGroupTable": "{\n\t\"Id\": 0,\n\t\"HeroSkillGroup\": 0,\n\t\"Hero\": 0,\n\t\"Weight\": 0\n}", "RougeTabNewbieTable": "{\n\t\"Id\": 0,\n\t\"Chapter\": 0,\n\t\"Count\": 0,\n\t\"RougeTabGroup\": [\n\t\t0\n\t],\n\t\"RecommendPos\": 0\n}", "RougeTabTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"IsSenior\": false,\n\t\"IsPassive\": false,\n\t\"IsSurplus\": false,\n\t\"IsRecovery\": false,\n\t\"IsTree\": false,\n\t\"ShowGroup\": 0,\n\t\"RougeTabGroup\": 0,\n\t\"ReqHeroGeneLevel\": 0,\n\t\"ReqRougeTab\": [\n\t\t0\n\t],\n\t\"ReqRougeTabCnt\": [\n\t\t0\n\t],\n\t\"RougeTabType\": 0,\n\t\"RougeTabUnlock\": [\n\t\t0\n\t],\n\t\"RougeTabEffect\": [\n\t\t0\n\t],\n\t\"Weight\": 0,\n\t\"WeightCoef\": 0,\n\t\"Limit\": 0,\n\t\"MutexGroup\": 0\n}", "RougeWeightCoef": "{\n\t\"Id\": 0,\n\t\"UpgradeTime1Coef\": [\n\t\t{\n\t\t\t\"UpgradeTimeCoef\": 0\n\t\t}\n\t]\n}", "SelectChestGroupTable": "{\n\t\"Id\": 0,\n\t\"Item\": 0,\n\t\"SelectCnt\": 0\n}", "SelectChestMainTable": "{\n\t\"Id\": 0,\n\t\"ItemType\": 0,\n\t\"ItemCnt\": 0,\n\t\"SelectChestGroup\": 0\n}", "SevenDayTasksScoreTable": "{\n\t\"Id\": 0,\n\t\"ScoreType\": 0,\n\t\"ScoreValue\": 0,\n\t\"Reward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t],\n\t\"Day7Reward\": 0\n}", "SevenDayTasksTable": "{\n\t\"Id\": 0,\n\t\"Condition\": 0,\n\t\"Day\": 0,\n\t\"TaskId\": 0,\n\t\"TaskType\": 0,\n\t\"Formula\": \"\",\n\t\"Value\": 0,\n\t\"RewardType\": [\n\t\t0\n\t],\n\t\"RewardValue\": [\n\t\t0\n\t]\n}", "ShopTable": "{\n\t\"Id\": 0,\n\t\"Type\": 0,\n\t\"Name\": \"\",\n\t\"PurchaseLimit\": 0\n}", "Sign7Table": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"Turn\": 0,\n\t\"Day\": 0,\n\t\"Reward\": [\n\t\t{\n\t\t\t\"RewardType\": 0,\n\t\t\t\"RewardValue\": 0\n\t\t}\n\t]\n}", "SkillDmgTypeTable": "{\n\t\"Id\": 0,\n\t\"SkillDmgType\": 0\n}", "TowerAILevelTable": "{\n\t\"Id\": 0,\n\t\"AI\": 0,\n\t\"AILevel\": 0,\n\t\"Power\": 0,\n\t\"Hp\": 0,\n\t\"Atk\": 0,\n\t\"Def\": 0,\n\t\"MoveSpeed\": 0,\n\t\"CritChance\": 0,\n\t\"CritDmgUpPer\": 0,\n\t\"AtkUpPer\": 0,\n\t\"DmgUpPer\": 0,\n\t\"CritResistChance\": 0,\n\t\"BeCritDmgDownPer\": 0,\n\t\"BeDmgDownPer\": 0,\n\t\"DefUpPer\": 0,\n\t\"HpUpPer\": 0,\n\t\"CdRate\": 0\n}", "TowerAITable": "{\n\t\"Id\": 0,\n\t\"AI\": 0\n}", "TowerTable": "{\n\t\"Id\": 0,\n\t\"Shelter\": 0,\n\t\"Name\": \"\",\n\t\"Layer\": 0,\n\t\"IsReward\": false,\n\t\"AI\": [\n\t\t{\n\t\t\t\"AI\": 0,\n\t\t\t\"Level\": 0,\n\t\t\t\"Star\": 0,\n\t\t\t\"Skill\": [\n\t\t\t\t0\n\t\t\t],\n\t\t\t\"<PERSON>\": 0\n\t\t}\n\t],\n\t\"RewardType\": [\n\t\t0\n\t],\n\t\"RewardValue\": [\n\t\t0\n\t]\n}", "TurnRewardTable": "{\n\t\"Id\": 0,\n\t\"Turn\": 0,\n\t\"Weight\": 0\n}", "TurnScoreRewardTable": "{\n\t\"Id\": 0,\n\t\"Turn\": 0\n}", "TurnTable": "{\n\t\"Id\": 0,\n\t\"StringId\": \"\",\n\t\"DailyFreeTimesLimit\": 0,\n\t\"Bet\": [\n\t\t0\n\t],\n\t\"DrawCostType\": [\n\t\t0\n\t],\n\t\"DrawCostValue\": [\n\t\t0\n\t],\n\t\"DrawCostDiamdondCnt\": [\n\t\t0\n\t],\n\t\"Limit\": 0\n}", "VehicleTable": "{\n\t\"Id\": 0,\n\t\"Power\": 0,\n\t\"Hp\": 0,\n\t\"Atk\": 0,\n\t\"Def\": 0,\n\t\"VerticalMoveSpeed\": 0,\n\t\"CritChance\": 0,\n\t\"CritDmgUpPer\": 0,\n\t\"CdRate\": 0,\n\t\"DmgUpPer\": 0,\n\t\"CritResistChance\": 0,\n\t\"BeCritDmgDownPer\": 0,\n\t\"BeDmgDownPer\": 0\n}"}}