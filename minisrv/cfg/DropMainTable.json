{"1": {"Id": 1, "DropGroupId": 1, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 10, "MaxValue": 10}, "2": {"Id": 2, "DropGroupId": 2, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 20, "MaxValue": 20}, "3": {"Id": 3, "DropGroupId": 3, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 60, "MaxValue": 60}, "4": {"Id": 4, "DropGroupId": 4, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 100, "MaxValue": 100}, "5": {"Id": 5, "DropGroupId": 5, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 150, "MaxValue": 150}, "6": {"Id": 6, "DropGroupId": 6, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 190, "MaxValue": 190}, "7": {"Id": 7, "DropGroupId": 7, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 240, "MaxValue": 240}, "8": {"Id": 8, "DropGroupId": 8, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 300, "MaxValue": 300}, "9": {"Id": 9, "DropGroupId": 9, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 360, "MaxValue": 360}, "10": {"Id": 10, "DropGroupId": 10, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 420, "MaxValue": 420}, "11": {"Id": 11, "DropGroupId": 11, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 5, "MaxValue": 5}, "12": {"Id": 12, "DropGroupId": 12, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 10, "MaxValue": 10}, "13": {"Id": 13, "DropGroupId": 13, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 30, "MaxValue": 30}, "14": {"Id": 14, "DropGroupId": 14, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 50, "MaxValue": 50}, "15": {"Id": 15, "DropGroupId": 15, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 75, "MaxValue": 75}, "16": {"Id": 16, "DropGroupId": 16, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 95, "MaxValue": 95}, "17": {"Id": 17, "DropGroupId": 17, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 120, "MaxValue": 120}, "18": {"Id": 18, "DropGroupId": 18, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 150, "MaxValue": 150}, "19": {"Id": 19, "DropGroupId": 19, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 180, "MaxValue": 180}, "20": {"Id": 20, "DropGroupId": 20, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 210, "MaxValue": 210}, "21": {"Id": 21, "DropGroupId": 21, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 20, "MaxValue": 20}, "22": {"Id": 22, "DropGroupId": 22, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 40, "MaxValue": 40}, "23": {"Id": 23, "DropGroupId": 23, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 120, "MaxValue": 120}, "24": {"Id": 24, "DropGroupId": 24, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 200, "MaxValue": 200}, "25": {"Id": 25, "DropGroupId": 25, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 300, "MaxValue": 300}, "26": {"Id": 26, "DropGroupId": 26, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 380, "MaxValue": 380}, "27": {"Id": 27, "DropGroupId": 27, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 480, "MaxValue": 480}, "28": {"Id": 28, "DropGroupId": 28, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 600, "MaxValue": 600}, "29": {"Id": 29, "DropGroupId": 29, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 720, "MaxValue": 720}, "30": {"Id": 30, "DropGroupId": 30, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 840, "MaxValue": 840}, "31": {"Id": 31, "DropGroupId": 31, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 20, "MaxValue": 20}, "32": {"Id": 32, "DropGroupId": 32, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 40, "MaxValue": 40}, "33": {"Id": 33, "DropGroupId": 33, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 120, "MaxValue": 120}, "34": {"Id": 34, "DropGroupId": 34, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 200, "MaxValue": 200}, "35": {"Id": 35, "DropGroupId": 35, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 300, "MaxValue": 300}, "36": {"Id": 36, "DropGroupId": 36, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 380, "MaxValue": 380}, "37": {"Id": 37, "DropGroupId": 37, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 480, "MaxValue": 480}, "38": {"Id": 38, "DropGroupId": 38, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 600, "MaxValue": 600}, "39": {"Id": 39, "DropGroupId": 39, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 720, "MaxValue": 720}, "40": {"Id": 40, "DropGroupId": 40, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 840, "MaxValue": 840}, "41": {"Id": 41, "DropGroupId": 1001, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 500, "MaxValue": 500}, "42": {"Id": 42, "DropGroupId": 1002, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 1000, "MaxValue": 1000}, "43": {"Id": 43, "DropGroupId": 1003, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 3000, "MaxValue": 3000}, "44": {"Id": 44, "DropGroupId": 1004, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 5000, "MaxValue": 5000}, "45": {"Id": 45, "DropGroupId": 1005, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 7500, "MaxValue": 7500}, "46": {"Id": 46, "DropGroupId": 1006, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 9500, "MaxValue": 9500}, "47": {"Id": 47, "DropGroupId": 1007, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 12000, "MaxValue": 12000}, "48": {"Id": 48, "DropGroupId": 1008, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 15000, "MaxValue": 15000}, "49": {"Id": 49, "DropGroupId": 1009, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 18000, "MaxValue": 18000}, "50": {"Id": 50, "DropGroupId": 1010, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 21000, "MaxValue": 21000}, "51": {"Id": 51, "DropGroupId": 1011, "Chance": 10000, "Weight": 0, "ItemId": 77, "MinValue": 1, "MaxValue": 1}, "52": {"Id": 52, "DropGroupId": 1011, "Chance": 1000, "Weight": 0, "ItemId": 88, "MinValue": 1, "MaxValue": 1}, "53": {"Id": 53, "DropGroupId": 1012, "Chance": 10000, "Weight": 0, "ItemId": 78, "MinValue": 1, "MaxValue": 1}, "54": {"Id": 54, "DropGroupId": 1012, "Chance": 3000, "Weight": 0, "ItemId": 88, "MinValue": 1, "MaxValue": 1}, "55": {"Id": 55, "DropGroupId": 1013, "Chance": 10000, "Weight": 0, "ItemId": 79, "MinValue": 1, "MaxValue": 1}, "56": {"Id": 56, "DropGroupId": 1013, "Chance": 5000, "Weight": 0, "ItemId": 88, "MinValue": 1, "MaxValue": 1}, "57": {"Id": 57, "DropGroupId": 1014, "Chance": 10000, "Weight": 0, "ItemId": 77, "MinValue": 1, "MaxValue": 1}, "58": {"Id": 58, "DropGroupId": 1014, "Chance": 1000, "Weight": 0, "ItemId": 88, "MinValue": 1, "MaxValue": 1}, "60": {"Id": 60, "DropGroupId": 1015, "Chance": 10000, "Weight": 0, "ItemId": 77, "MinValue": 1, "MaxValue": 1}, "61": {"Id": 61, "DropGroupId": 1015, "Chance": 1000, "Weight": 0, "ItemId": 88, "MinValue": 1, "MaxValue": 1}, "63": {"Id": 63, "DropGroupId": 1016, "Chance": 10000, "Weight": 0, "ItemId": 77, "MinValue": 1, "MaxValue": 1}, "64": {"Id": 64, "DropGroupId": 1016, "Chance": 1500, "Weight": 0, "ItemId": 88, "MinValue": 1, "MaxValue": 1}}