{"1": {"Id": 1, "Dungeon": 5, "Level": 1, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 500}]}, "2": {"Id": 2, "Dungeon": 5, "Level": 2, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 530}]}, "3": {"Id": 3, "Dungeon": 5, "Level": 3, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 560}]}, "4": {"Id": 4, "Dungeon": 5, "Level": 4, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 590}]}, "5": {"Id": 5, "Dungeon": 5, "Level": 5, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 620}]}, "6": {"Id": 6, "Dungeon": 5, "Level": 6, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 650}]}, "7": {"Id": 7, "Dungeon": 5, "Level": 7, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 680}]}, "8": {"Id": 8, "Dungeon": 5, "Level": 8, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 710}]}, "9": {"Id": 9, "Dungeon": 5, "Level": 9, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 740}]}, "10": {"Id": 10, "Dungeon": 5, "Level": 10, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 770}]}, "11": {"Id": 11, "Dungeon": 5, "Level": 11, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 800}]}, "12": {"Id": 12, "Dungeon": 5, "Level": 12, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 830}]}, "13": {"Id": 13, "Dungeon": 5, "Level": 13, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 860}]}, "14": {"Id": 14, "Dungeon": 5, "Level": 14, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 890}]}, "15": {"Id": 15, "Dungeon": 5, "Level": 15, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 920}]}, "16": {"Id": 16, "Dungeon": 5, "Level": 16, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 950}]}, "17": {"Id": 17, "Dungeon": 5, "Level": 17, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 980}]}, "18": {"Id": 18, "Dungeon": 5, "Level": 18, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1010}]}, "19": {"Id": 19, "Dungeon": 5, "Level": 19, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1040}]}, "20": {"Id": 20, "Dungeon": 5, "Level": 20, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1070}]}, "21": {"Id": 21, "Dungeon": 5, "Level": 21, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1100}]}, "22": {"Id": 22, "Dungeon": 5, "Level": 22, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1130}]}, "23": {"Id": 23, "Dungeon": 5, "Level": 23, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1160}]}, "24": {"Id": 24, "Dungeon": 5, "Level": 24, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1190}]}, "25": {"Id": 25, "Dungeon": 5, "Level": 25, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1220}]}, "26": {"Id": 26, "Dungeon": 5, "Level": 26, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1250}]}, "27": {"Id": 27, "Dungeon": 5, "Level": 27, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1280}]}, "28": {"Id": 28, "Dungeon": 5, "Level": 28, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1310}]}, "29": {"Id": 29, "Dungeon": 5, "Level": 29, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1340}]}, "30": {"Id": 30, "Dungeon": 5, "Level": 30, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1370}]}, "31": {"Id": 31, "Dungeon": 5, "Level": 31, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1400}]}, "32": {"Id": 32, "Dungeon": 5, "Level": 32, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1430}]}, "33": {"Id": 33, "Dungeon": 5, "Level": 33, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1460}]}, "34": {"Id": 34, "Dungeon": 5, "Level": 34, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1490}]}, "35": {"Id": 35, "Dungeon": 5, "Level": 35, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1520}]}, "36": {"Id": 36, "Dungeon": 5, "Level": 36, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1550}]}, "37": {"Id": 37, "Dungeon": 5, "Level": 37, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1580}]}, "38": {"Id": 38, "Dungeon": 5, "Level": 38, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1610}]}, "39": {"Id": 39, "Dungeon": 5, "Level": 39, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1640}]}, "40": {"Id": 40, "Dungeon": 5, "Level": 40, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1670}]}, "41": {"Id": 41, "Dungeon": 5, "Level": 41, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1700}]}, "42": {"Id": 42, "Dungeon": 5, "Level": 42, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1730}]}, "43": {"Id": 43, "Dungeon": 5, "Level": 43, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1760}]}, "44": {"Id": 44, "Dungeon": 5, "Level": 44, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1790}]}, "45": {"Id": 45, "Dungeon": 5, "Level": 45, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1820}]}, "46": {"Id": 46, "Dungeon": 5, "Level": 46, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1850}]}, "47": {"Id": 47, "Dungeon": 5, "Level": 47, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1880}]}, "48": {"Id": 48, "Dungeon": 5, "Level": 48, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1910}]}, "49": {"Id": 49, "Dungeon": 5, "Level": 49, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1940}]}, "50": {"Id": 50, "Dungeon": 5, "Level": 50, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 1970}]}, "51": {"Id": 51, "Dungeon": 5, "Level": 51, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2000}]}, "52": {"Id": 52, "Dungeon": 5, "Level": 52, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2030}]}, "53": {"Id": 53, "Dungeon": 5, "Level": 53, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2060}]}, "54": {"Id": 54, "Dungeon": 5, "Level": 54, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2090}]}, "55": {"Id": 55, "Dungeon": 5, "Level": 55, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2120}]}, "56": {"Id": 56, "Dungeon": 5, "Level": 56, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2150}]}, "57": {"Id": 57, "Dungeon": 5, "Level": 57, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2180}]}, "58": {"Id": 58, "Dungeon": 5, "Level": 58, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2210}]}, "59": {"Id": 59, "Dungeon": 5, "Level": 59, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2240}]}, "60": {"Id": 60, "Dungeon": 5, "Level": 60, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2270}]}, "61": {"Id": 61, "Dungeon": 5, "Level": 61, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2300}]}, "62": {"Id": 62, "Dungeon": 5, "Level": 62, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2330}]}, "63": {"Id": 63, "Dungeon": 5, "Level": 63, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2360}]}, "64": {"Id": 64, "Dungeon": 5, "Level": 64, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2390}]}, "65": {"Id": 65, "Dungeon": 5, "Level": 65, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2420}]}, "66": {"Id": 66, "Dungeon": 5, "Level": 66, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2450}]}, "67": {"Id": 67, "Dungeon": 5, "Level": 67, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2480}]}, "68": {"Id": 68, "Dungeon": 5, "Level": 68, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2510}]}, "69": {"Id": 69, "Dungeon": 5, "Level": 69, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2540}]}, "70": {"Id": 70, "Dungeon": 5, "Level": 70, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2570}]}, "71": {"Id": 71, "Dungeon": 5, "Level": 71, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2600}]}, "72": {"Id": 72, "Dungeon": 5, "Level": 72, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2630}]}, "73": {"Id": 73, "Dungeon": 5, "Level": 73, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2660}]}, "74": {"Id": 74, "Dungeon": 5, "Level": 74, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2690}]}, "75": {"Id": 75, "Dungeon": 5, "Level": 75, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2720}]}, "76": {"Id": 76, "Dungeon": 5, "Level": 76, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2750}]}, "77": {"Id": 77, "Dungeon": 5, "Level": 77, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2780}]}, "78": {"Id": 78, "Dungeon": 5, "Level": 78, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2810}]}, "79": {"Id": 79, "Dungeon": 5, "Level": 79, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2840}]}, "80": {"Id": 80, "Dungeon": 5, "Level": 80, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2870}]}, "81": {"Id": 81, "Dungeon": 5, "Level": 81, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2900}]}, "82": {"Id": 82, "Dungeon": 5, "Level": 82, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2930}]}, "83": {"Id": 83, "Dungeon": 5, "Level": 83, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2960}]}, "84": {"Id": 84, "Dungeon": 5, "Level": 84, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 2990}]}, "85": {"Id": 85, "Dungeon": 5, "Level": 85, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 3020}]}, "86": {"Id": 86, "Dungeon": 5, "Level": 86, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 3050}]}, "87": {"Id": 87, "Dungeon": 5, "Level": 87, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 3080}]}, "88": {"Id": 88, "Dungeon": 5, "Level": 88, "ChooseNum": 5, "IsMax": true, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 3110}]}, "89": {"Id": 89, "Dungeon": 5, "Level": 89, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 3140}]}, "90": {"Id": 90, "Dungeon": 5, "Level": 90, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 3170}]}, "91": {"Id": 91, "Dungeon": 5, "Level": 91, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 3200}]}, "92": {"Id": 92, "Dungeon": 5, "Level": 92, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 3230}]}, "93": {"Id": 93, "Dungeon": 5, "Level": 93, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 3260}]}, "94": {"Id": 94, "Dungeon": 5, "Level": 94, "ChooseNum": 5, "IsMax": true, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 3290}]}, "95": {"Id": 95, "Dungeon": 5, "Level": 95, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 3320}]}, "96": {"Id": 96, "Dungeon": 5, "Level": 96, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 3350}]}, "97": {"Id": 97, "Dungeon": 5, "Level": 97, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 3380}]}, "98": {"Id": 98, "Dungeon": 5, "Level": 98, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 3410}]}, "99": {"Id": 99, "Dungeon": 5, "Level": 99, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 3440}]}, "100": {"Id": 100, "Dungeon": 5, "Level": 100, "ChooseNum": 5, "IsMax": true, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 6, "RewardValue": 3470}]}}