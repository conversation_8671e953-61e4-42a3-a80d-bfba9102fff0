// Code generated by Cfgo. DO NOT EDIT.
package servercfg

const enumDefaultValue = int32(0)

type Configs struct {
	ConfigsExtension
	cfgoVersion                     string
	cfgoCommitHash                  string
	metaVersion                     string
	dataVersion                     string
	versionExtraInfos               map[string]string
	AchievementTable                *AchievementTable
	ActivityTable                   *ActivityTable
	ArenaBotTable                   *ArenaBotTable
	ArenaChallengeRewardTable       *ArenaChallengeRewardTable
	ArenaDailyRankRewardTable       *ArenaDailyRankRewardTable
	ArenaExtraChallengeCntTable     *ArenaExtraChallengeCntTable
	ArenaMatchTable                 *ArenaMatchTable
	ArenaRefreshTable               *ArenaRefreshTable
	ArenaScoreTable                 *ArenaScoreTable
	ArenaShopTable                  *ArenaShopTable
	ArenaWeeklyRankRewardTable      *ArenaWeeklyRankRewardTable
	AttributeHierarchyTable         *AttributeHierarchyTable
	AvatarFrameTable                *AvatarFrameTable
	AvatarTable                     *AvatarTable
	BattleAttributeTable            *BattleAttributeTable
	BattleModelTable                *BattleModelTable
	BenefitsCalcJustShowTable       *BenefitsCalcJustShowTable
	BenefitsCalcTable               *BenefitsCalcTable
	BenefitsTable                   *BenefitsTable
	BlackShopTable                  *BlackShopTable
	ChapterTaskMainTable            *ChapterTaskMainTable
	ChapterTaskTable                *ChapterTaskTable
	DailyTasksScoreTable            *DailyTasksScoreTable
	DailyTasksTable                 *DailyTasksTable
	DaveLevelTable                  *DaveLevelTable
	DropGroupTable                  *DropGroupTable
	DropMainTable                   *DropMainTable
	DungeonChapterLevelTable        *DungeonChapterLevelTable
	DungeonCoinLevelTable           *DungeonCoinLevelTable
	DungeonGeneLevelTable           *DungeonGeneLevelTable
	DungeonLordEquipLevelTable      *DungeonLordEquipLevelTable
	DungeonRefreshTable             *DungeonRefreshTable
	DungeonSunshineLevelTable       *DungeonSunshineLevelTable
	DungeonTypeTable                *DungeonTypeTable
	FunctionPreviewTable            *FunctionPreviewTable
	FunctionTable                   *FunctionTable
	GameConfigs                     *GameConfigs
	GemAffixQualityTable            *GemAffixQualityTable
	GemQualityTypeTable             *GemQualityTypeTable
	GoToTable                       *GoToTable
	GuildFlagTable                  *GuildFlagTable
	GuildHaggleTable                *GuildHaggleTable
	GuildLevelTable                 *GuildLevelTable
	GuildPermissionTable            *GuildPermissionTable
	GuildRankTable                  *GuildRankTable
	GuildShopTable                  *GuildShopTable
	GuildTaskTable                  *GuildTaskTable
	GuildTasksScoreTable            *GuildTasksScoreTable
	HeroBondsTable                  *HeroBondsTable
	HeroCareerTable                 *HeroCareerTable
	HeroConfigTable                 *HeroConfigTable
	HeroElementTable                *HeroElementTable
	HeroFragmentTable               *HeroFragmentTable
	HeroGeneFragmentTable           *HeroGeneFragmentTable
	HeroGeneTable                   *HeroGeneTable
	HeroLevelTable                  *HeroLevelTable
	HeroLotteryGroupTable           *HeroLotteryGroupTable
	HeroLotteryMustTable            *HeroLotteryMustTable
	HeroLotteryRandomGroupTable     *HeroLotteryRandomGroupTable
	HeroLotteryRandomTable          *HeroLotteryRandomTable
	HeroQualityTable                *HeroQualityTable
	HeroRestrainTable               *HeroRestrainTable
	HeroSkillAttrTable              *HeroSkillAttrTable
	HeroSkillAwakeTable             *HeroSkillAwakeTable
	HeroSkillBuffTable              *HeroSkillBuffTable
	HeroSkillBuffTypeTable          *HeroSkillBuffTypeTable
	HeroSkillEffectTable            *HeroSkillEffectTable
	HeroSkillGroupTable             *HeroSkillGroupTable
	HeroSkillTypeTable              *HeroSkillTypeTable
	HeroStarTable                   *HeroStarTable
	HeroTable                       *HeroTable
	HeroTypeTable                   *HeroTypeTable
	Iap1stTable                     *Iap1stTable
	Iap2XTable                      *Iap2XTable
	IapAdFreeTable                  *IapAdFreeTable
	IapBPTable                      *IapBPTable
	IapBpRewardTable                *IapBpRewardTable
	IapDailySaleFreeRewardTable     *IapDailySaleFreeRewardTable
	IapDailySaleRewardGroupTable    *IapDailySaleRewardGroupTable
	IapDailySaleRewardTable         *IapDailySaleRewardTable
	IapDailySaleTable               *IapDailySaleTable
	IapDealTable                    *IapDealTable
	IapLevelFundRewardTable         *IapLevelFundRewardTable
	IapLevelFundTable               *IapLevelFundTable
	IapLifeCardTable                *IapLifeCardTable
	IapMonthCardTable               *IapMonthCardTable
	IapPackageDiamondShopTable      *IapPackageDiamondShopTable
	IapPackageRewardTable           *IapPackageRewardTable
	IapPackageTable                 *IapPackageTable
	IapPriceTable                   *IapPriceTable
	IapRegularPackGroupTable        *IapRegularPackGroupTable
	IapRegularPackTable             *IapRegularPackTable
	IapShopMallTable                *IapShopMallTable
	IapSignRewardTable              *IapSignRewardTable
	IapSignTable                    *IapSignTable
	IapTriggerPackGroupTable        *IapTriggerPackGroupTable
	IapTriggerPackTable             *IapTriggerPackTable
	IapTurnPackTable                *IapTurnPackTable
	IdleMonsterTable                *IdleMonsterTable
	IdleRewardTable                 *IdleRewardTable
	IdleRewardTime                  *IdleRewardTime
	ItemQualityTable                *ItemQualityTable
	ItemSourceTable                 *ItemSourceTable
	ItemTable                       *ItemTable
	LanguageCnTable                 *LanguageCnTable
	LevelShopTable                  *LevelShopTable
	LoginOpenTable                  *LoginOpenTable
	LordEquipGradeTable             *LordEquipGradeTable
	LordEquipGradeTypeTable         *LordEquipGradeTypeTable
	LordEquipSlotsTable             *LordEquipSlotsTable
	LordEquipTable                  *LordEquipTable
	LordEquipTypeTable              *LordEquipTypeTable
	LordGemCraftTable               *LordGemCraftTable
	LordGemDropCntTable             *LordGemDropCntTable
	LordGemDropQualityTable         *LordGemDropQualityTable
	LordGemRandomGroupChanceTable   *LordGemRandomGroupChanceTable
	LordGemRandomGroupMustTable     *LordGemRandomGroupMustTable
	LordGemRandomGroupTable         *LordGemRandomGroupTable
	LordGemRandomRewardGroupTable   *LordGemRandomRewardGroupTable
	LordGemReforgeTable             *LordGemReforgeTable
	LordGemTable                    *LordGemTable
	MailTable                       *MailTable
	MainChapterLevelTable           *MainChapterLevelTable
	MainChapterTable                *MainChapterTable
	MainLevelPassRewardTable        *MainLevelPassRewardTable
	MainLevelRangeDmgTable          *MainLevelRangeDmgTable
	MainLevelRewardRatioTable       *MainLevelRewardRatioTable
	MainLevelRewardTable            *MainLevelRewardTable
	MainLevelRogueRewardWeightTable *MainLevelRogueRewardWeightTable
	MainLevelTable                  *MainLevelTable
	MainLineTasksTable              *MainLineTasksTable
	MapEventBuffTable               *MapEventBuffTable
	MapEventMonsterGroupTable       *MapEventMonsterGroupTable
	MapEventMonsterTable            *MapEventMonsterTable
	MapEventObstacleTable           *MapEventObstacleTable
	MapEventPropTable               *MapEventPropTable
	MapEventRewardTable             *MapEventRewardTable
	MapEventSkillTable              *MapEventSkillTable
	MapEventTable                   *MapEventTable
	MapRefreshMonsterEventTable     *MapRefreshMonsterEventTable
	ModifierTable                   *ModifierTable
	MonsterCareerTable              *MonsterCareerTable
	MonsterGradeTable               *MonsterGradeTable
	MonsterPosTypeTable             *MonsterPosTypeTable
	MonsterPreviewSchemeTable       *MonsterPreviewSchemeTable
	MonsterSkillTable               *MonsterSkillTable
	MonsterTable                    *MonsterTable
	MonsterTypeTable                *MonsterTypeTable
	NewbieTable                     *NewbieTable
	NpcDialogueTable                *NpcDialogueTable
	PhotovoltaicTable               *PhotovoltaicTable
	PresetsTable                    *PresetsTable
	RankMainTable                   *RankMainTable
	RankRewardTable                 *RankRewardTable
	RougeRefreshTable               *RougeRefreshTable
	RougeTabEffectTable             *RougeTabEffectTable
	RougeTabGroupRandomTable        *RougeTabGroupRandomTable
	RougeTabGroupTable              *RougeTabGroupTable
	RougeTabNewbieTable             *RougeTabNewbieTable
	RougeTabTable                   *RougeTabTable
	RougeWeightCoef                 *RougeWeightCoef
	SelectChestGroupTable           *SelectChestGroupTable
	SelectChestMainTable            *SelectChestMainTable
	SevenDayTasksScoreTable         *SevenDayTasksScoreTable
	SevenDayTasksTable              *SevenDayTasksTable
	ShopTable                       *ShopTable
	Sign7Table                      *Sign7Table
	SkillDmgTypeTable               *SkillDmgTypeTable
	TowerAILevelTable               *TowerAILevelTable
	TowerAITable                    *TowerAITable
	TowerTable                      *TowerTable
	TurnRewardTable                 *TurnRewardTable
	TurnScoreRewardTable            *TurnScoreRewardTable
	TurnTable                       *TurnTable
	VehicleTable                    *VehicleTable
}

func NewConfigs() *Configs {
	return &Configs{
		cfgoVersion:                     "1.0.5",
		cfgoCommitHash:                  "71e74f5",
		metaVersion:                     "8165",
		AchievementTable:                NewAchievementTable(),
		ActivityTable:                   NewActivityTable(),
		ArenaBotTable:                   NewArenaBotTable(),
		ArenaChallengeRewardTable:       NewArenaChallengeRewardTable(),
		ArenaDailyRankRewardTable:       NewArenaDailyRankRewardTable(),
		ArenaExtraChallengeCntTable:     NewArenaExtraChallengeCntTable(),
		ArenaMatchTable:                 NewArenaMatchTable(),
		ArenaRefreshTable:               NewArenaRefreshTable(),
		ArenaScoreTable:                 NewArenaScoreTable(),
		ArenaShopTable:                  NewArenaShopTable(),
		ArenaWeeklyRankRewardTable:      NewArenaWeeklyRankRewardTable(),
		AttributeHierarchyTable:         NewAttributeHierarchyTable(),
		AvatarFrameTable:                NewAvatarFrameTable(),
		AvatarTable:                     NewAvatarTable(),
		BattleAttributeTable:            NewBattleAttributeTable(),
		BattleModelTable:                NewBattleModelTable(),
		BenefitsCalcJustShowTable:       NewBenefitsCalcJustShowTable(),
		BenefitsCalcTable:               NewBenefitsCalcTable(),
		BenefitsTable:                   NewBenefitsTable(),
		BlackShopTable:                  NewBlackShopTable(),
		ChapterTaskMainTable:            NewChapterTaskMainTable(),
		ChapterTaskTable:                NewChapterTaskTable(),
		DailyTasksScoreTable:            NewDailyTasksScoreTable(),
		DailyTasksTable:                 NewDailyTasksTable(),
		DaveLevelTable:                  NewDaveLevelTable(),
		DropGroupTable:                  NewDropGroupTable(),
		DropMainTable:                   NewDropMainTable(),
		DungeonChapterLevelTable:        NewDungeonChapterLevelTable(),
		DungeonCoinLevelTable:           NewDungeonCoinLevelTable(),
		DungeonGeneLevelTable:           NewDungeonGeneLevelTable(),
		DungeonLordEquipLevelTable:      NewDungeonLordEquipLevelTable(),
		DungeonRefreshTable:             NewDungeonRefreshTable(),
		DungeonSunshineLevelTable:       NewDungeonSunshineLevelTable(),
		DungeonTypeTable:                NewDungeonTypeTable(),
		FunctionPreviewTable:            NewFunctionPreviewTable(),
		FunctionTable:                   NewFunctionTable(),
		GameConfigs:                     NewGameConfigs(),
		GemAffixQualityTable:            NewGemAffixQualityTable(),
		GemQualityTypeTable:             NewGemQualityTypeTable(),
		GoToTable:                       NewGoToTable(),
		GuildFlagTable:                  NewGuildFlagTable(),
		GuildHaggleTable:                NewGuildHaggleTable(),
		GuildLevelTable:                 NewGuildLevelTable(),
		GuildPermissionTable:            NewGuildPermissionTable(),
		GuildRankTable:                  NewGuildRankTable(),
		GuildShopTable:                  NewGuildShopTable(),
		GuildTaskTable:                  NewGuildTaskTable(),
		GuildTasksScoreTable:            NewGuildTasksScoreTable(),
		HeroBondsTable:                  NewHeroBondsTable(),
		HeroCareerTable:                 NewHeroCareerTable(),
		HeroConfigTable:                 NewHeroConfigTable(),
		HeroElementTable:                NewHeroElementTable(),
		HeroFragmentTable:               NewHeroFragmentTable(),
		HeroGeneFragmentTable:           NewHeroGeneFragmentTable(),
		HeroGeneTable:                   NewHeroGeneTable(),
		HeroLevelTable:                  NewHeroLevelTable(),
		HeroLotteryGroupTable:           NewHeroLotteryGroupTable(),
		HeroLotteryMustTable:            NewHeroLotteryMustTable(),
		HeroLotteryRandomGroupTable:     NewHeroLotteryRandomGroupTable(),
		HeroLotteryRandomTable:          NewHeroLotteryRandomTable(),
		HeroQualityTable:                NewHeroQualityTable(),
		HeroRestrainTable:               NewHeroRestrainTable(),
		HeroSkillAttrTable:              NewHeroSkillAttrTable(),
		HeroSkillAwakeTable:             NewHeroSkillAwakeTable(),
		HeroSkillBuffTable:              NewHeroSkillBuffTable(),
		HeroSkillBuffTypeTable:          NewHeroSkillBuffTypeTable(),
		HeroSkillEffectTable:            NewHeroSkillEffectTable(),
		HeroSkillGroupTable:             NewHeroSkillGroupTable(),
		HeroSkillTypeTable:              NewHeroSkillTypeTable(),
		HeroStarTable:                   NewHeroStarTable(),
		HeroTable:                       NewHeroTable(),
		HeroTypeTable:                   NewHeroTypeTable(),
		Iap1stTable:                     NewIap1stTable(),
		Iap2XTable:                      NewIap2XTable(),
		IapAdFreeTable:                  NewIapAdFreeTable(),
		IapBPTable:                      NewIapBPTable(),
		IapBpRewardTable:                NewIapBpRewardTable(),
		IapDailySaleFreeRewardTable:     NewIapDailySaleFreeRewardTable(),
		IapDailySaleRewardGroupTable:    NewIapDailySaleRewardGroupTable(),
		IapDailySaleRewardTable:         NewIapDailySaleRewardTable(),
		IapDailySaleTable:               NewIapDailySaleTable(),
		IapDealTable:                    NewIapDealTable(),
		IapLevelFundRewardTable:         NewIapLevelFundRewardTable(),
		IapLevelFundTable:               NewIapLevelFundTable(),
		IapLifeCardTable:                NewIapLifeCardTable(),
		IapMonthCardTable:               NewIapMonthCardTable(),
		IapPackageDiamondShopTable:      NewIapPackageDiamondShopTable(),
		IapPackageRewardTable:           NewIapPackageRewardTable(),
		IapPackageTable:                 NewIapPackageTable(),
		IapPriceTable:                   NewIapPriceTable(),
		IapRegularPackGroupTable:        NewIapRegularPackGroupTable(),
		IapRegularPackTable:             NewIapRegularPackTable(),
		IapShopMallTable:                NewIapShopMallTable(),
		IapSignRewardTable:              NewIapSignRewardTable(),
		IapSignTable:                    NewIapSignTable(),
		IapTriggerPackGroupTable:        NewIapTriggerPackGroupTable(),
		IapTriggerPackTable:             NewIapTriggerPackTable(),
		IapTurnPackTable:                NewIapTurnPackTable(),
		IdleMonsterTable:                NewIdleMonsterTable(),
		IdleRewardTable:                 NewIdleRewardTable(),
		IdleRewardTime:                  NewIdleRewardTime(),
		ItemQualityTable:                NewItemQualityTable(),
		ItemSourceTable:                 NewItemSourceTable(),
		ItemTable:                       NewItemTable(),
		LanguageCnTable:                 NewLanguageCnTable(),
		LevelShopTable:                  NewLevelShopTable(),
		LoginOpenTable:                  NewLoginOpenTable(),
		LordEquipGradeTable:             NewLordEquipGradeTable(),
		LordEquipGradeTypeTable:         NewLordEquipGradeTypeTable(),
		LordEquipSlotsTable:             NewLordEquipSlotsTable(),
		LordEquipTable:                  NewLordEquipTable(),
		LordEquipTypeTable:              NewLordEquipTypeTable(),
		LordGemCraftTable:               NewLordGemCraftTable(),
		LordGemDropCntTable:             NewLordGemDropCntTable(),
		LordGemDropQualityTable:         NewLordGemDropQualityTable(),
		LordGemRandomGroupChanceTable:   NewLordGemRandomGroupChanceTable(),
		LordGemRandomGroupMustTable:     NewLordGemRandomGroupMustTable(),
		LordGemRandomGroupTable:         NewLordGemRandomGroupTable(),
		LordGemRandomRewardGroupTable:   NewLordGemRandomRewardGroupTable(),
		LordGemReforgeTable:             NewLordGemReforgeTable(),
		LordGemTable:                    NewLordGemTable(),
		MailTable:                       NewMailTable(),
		MainChapterLevelTable:           NewMainChapterLevelTable(),
		MainChapterTable:                NewMainChapterTable(),
		MainLevelPassRewardTable:        NewMainLevelPassRewardTable(),
		MainLevelRangeDmgTable:          NewMainLevelRangeDmgTable(),
		MainLevelRewardRatioTable:       NewMainLevelRewardRatioTable(),
		MainLevelRewardTable:            NewMainLevelRewardTable(),
		MainLevelRogueRewardWeightTable: NewMainLevelRogueRewardWeightTable(),
		MainLevelTable:                  NewMainLevelTable(),
		MainLineTasksTable:              NewMainLineTasksTable(),
		MapEventBuffTable:               NewMapEventBuffTable(),
		MapEventMonsterGroupTable:       NewMapEventMonsterGroupTable(),
		MapEventMonsterTable:            NewMapEventMonsterTable(),
		MapEventObstacleTable:           NewMapEventObstacleTable(),
		MapEventPropTable:               NewMapEventPropTable(),
		MapEventRewardTable:             NewMapEventRewardTable(),
		MapEventSkillTable:              NewMapEventSkillTable(),
		MapEventTable:                   NewMapEventTable(),
		MapRefreshMonsterEventTable:     NewMapRefreshMonsterEventTable(),
		ModifierTable:                   NewModifierTable(),
		MonsterCareerTable:              NewMonsterCareerTable(),
		MonsterGradeTable:               NewMonsterGradeTable(),
		MonsterPosTypeTable:             NewMonsterPosTypeTable(),
		MonsterPreviewSchemeTable:       NewMonsterPreviewSchemeTable(),
		MonsterSkillTable:               NewMonsterSkillTable(),
		MonsterTable:                    NewMonsterTable(),
		MonsterTypeTable:                NewMonsterTypeTable(),
		NewbieTable:                     NewNewbieTable(),
		NpcDialogueTable:                NewNpcDialogueTable(),
		PhotovoltaicTable:               NewPhotovoltaicTable(),
		PresetsTable:                    NewPresetsTable(),
		RankMainTable:                   NewRankMainTable(),
		RankRewardTable:                 NewRankRewardTable(),
		RougeRefreshTable:               NewRougeRefreshTable(),
		RougeTabEffectTable:             NewRougeTabEffectTable(),
		RougeTabGroupRandomTable:        NewRougeTabGroupRandomTable(),
		RougeTabGroupTable:              NewRougeTabGroupTable(),
		RougeTabNewbieTable:             NewRougeTabNewbieTable(),
		RougeTabTable:                   NewRougeTabTable(),
		RougeWeightCoef:                 NewRougeWeightCoef(),
		SelectChestGroupTable:           NewSelectChestGroupTable(),
		SelectChestMainTable:            NewSelectChestMainTable(),
		SevenDayTasksScoreTable:         NewSevenDayTasksScoreTable(),
		SevenDayTasksTable:              NewSevenDayTasksTable(),
		ShopTable:                       NewShopTable(),
		Sign7Table:                      NewSign7Table(),
		SkillDmgTypeTable:               NewSkillDmgTypeTable(),
		TowerAILevelTable:               NewTowerAILevelTable(),
		TowerAITable:                    NewTowerAITable(),
		TowerTable:                      NewTowerTable(),
		TurnRewardTable:                 NewTurnRewardTable(),
		TurnScoreRewardTable:            NewTurnScoreRewardTable(),
		TurnTable:                       NewTurnTable(),
		VehicleTable:                    NewVehicleTable(),
	}
}

func (c *Configs) DataVersion() string {
	return c.dataVersion
}
