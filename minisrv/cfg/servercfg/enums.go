// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type AttrDefaultType int32 // 技能属性默认值类型

const (
	AttrDefaultType_Number AttrDefaultType = 1 // 数字
	AttrDefaultType_Model  AttrDefaultType = 2 // 模型
	AttrDefaultType_Buff   AttrDefaultType = 3 // Buff
	AttrDefaultType_Effect AttrDefaultType = 4 // Effect
)

const AttrDefaultTypeEnumCount = 4

var AttrDefaultTypeEnums = [...]AttrDefaultType{AttrDefaultType_Number, AttrDefaultType_Model, AttrDefaultType_Buff, AttrDefaultType_Effect}

func (t AttrDefaultType) String() string {
	switch t {
	case AttrDefaultType_Number:
		return "Number"
	case AttrDefaultType_Model:
		return "Model"
	case AttrDefaultType_Buff:
		return "Buff"
	case AttrDefaultType_Effect:
		return "Effect"
	default:
		return ""
	}
}

type BagType int32 // 背包页签

const (
	BagType_Plants      BagType = 1 // 植物
	BagType_Consumption BagType = 2 // 消耗
	BagType_Materials   BagType = 3 // 材料
)

const BagTypeEnumCount = 3

var BagTypeEnums = [...]BagType{BagType_Plants, BagType_Consumption, BagType_Materials}

func (t BagType) String() string {
	switch t {
	case BagType_Plants:
		return "Plants"
	case BagType_Consumption:
		return "Consumption"
	case BagType_Materials:
		return "Materials"
	default:
		return ""
	}
}

type BenefitCalcFormula int32 // 计算公式

const (
	BenefitCalcFormula_Formula1 BenefitCalcFormula = 1 // BaseValue+para1
	BenefitCalcFormula_Formula2 BenefitCalcFormula = 2 // BaseValue*(1+para1)
	BenefitCalcFormula_Formula3 BenefitCalcFormula = 3 // [BaseValue*(1+para1)+para2]*(1+para3)
	BenefitCalcFormula_Formula4 BenefitCalcFormula = 4 // (BaseValue+para1)*(1+para2)*(1+para3)
	BenefitCalcFormula_Formula5 BenefitCalcFormula = 5 // (BaseValue+para1)*(1+para2)
	BenefitCalcFormula_Formula6 BenefitCalcFormula = 6 // BaseValue/(1+para1)
	BenefitCalcFormula_Formula7 BenefitCalcFormula = 7 // (BaseValue+para1)/(1+para2)
	BenefitCalcFormula_Formula8 BenefitCalcFormula = 8 // BaseValue*(1+para1)/(1+para2)
	BenefitCalcFormula_Formula9 BenefitCalcFormula = 9 // BaseValue*(1-para1)
)

const BenefitCalcFormulaEnumCount = 9

var BenefitCalcFormulaEnums = [...]BenefitCalcFormula{BenefitCalcFormula_Formula1, BenefitCalcFormula_Formula2, BenefitCalcFormula_Formula3, BenefitCalcFormula_Formula4, BenefitCalcFormula_Formula5, BenefitCalcFormula_Formula6, BenefitCalcFormula_Formula7, BenefitCalcFormula_Formula8, BenefitCalcFormula_Formula9}

func (t BenefitCalcFormula) String() string {
	switch t {
	case BenefitCalcFormula_Formula1:
		return "Formula1"
	case BenefitCalcFormula_Formula2:
		return "Formula2"
	case BenefitCalcFormula_Formula3:
		return "Formula3"
	case BenefitCalcFormula_Formula4:
		return "Formula4"
	case BenefitCalcFormula_Formula5:
		return "Formula5"
	case BenefitCalcFormula_Formula6:
		return "Formula6"
	case BenefitCalcFormula_Formula7:
		return "Formula7"
	case BenefitCalcFormula_Formula8:
		return "Formula8"
	case BenefitCalcFormula_Formula9:
		return "Formula9"
	default:
		return ""
	}
}

type BuffOverlyingType int32 // buff叠加类型

const (
	BuffOverlyingType_NoOverlying     BuffOverlyingType = 1 // 不可叠加
	BuffOverlyingType_TimeOverlying   BuffOverlyingType = 2 // 时间叠加
	BuffOverlyingType_EffectOverlying BuffOverlyingType = 3 // 效果叠加
	BuffOverlyingType_EffectCover     BuffOverlyingType = 4 // 效果覆盖
	BuffOverlyingType_EffectReplace   BuffOverlyingType = 5 // 效果替换
	BuffOverlyingType_EffectMultiple  BuffOverlyingType = 6 // 效果影分身
)

const BuffOverlyingTypeEnumCount = 6

var BuffOverlyingTypeEnums = [...]BuffOverlyingType{BuffOverlyingType_NoOverlying, BuffOverlyingType_TimeOverlying, BuffOverlyingType_EffectOverlying, BuffOverlyingType_EffectCover, BuffOverlyingType_EffectReplace, BuffOverlyingType_EffectMultiple}

func (t BuffOverlyingType) String() string {
	switch t {
	case BuffOverlyingType_NoOverlying:
		return "NoOverlying"
	case BuffOverlyingType_TimeOverlying:
		return "TimeOverlying"
	case BuffOverlyingType_EffectOverlying:
		return "EffectOverlying"
	case BuffOverlyingType_EffectCover:
		return "EffectCover"
	case BuffOverlyingType_EffectReplace:
		return "EffectReplace"
	case BuffOverlyingType_EffectMultiple:
		return "EffectMultiple"
	default:
		return ""
	}
}

type BuffTarget int32 // Buff目标

const (
	BuffTarget_Own                           BuffTarget = 1  // 自己
	BuffTarget_OwnSide                       BuffTarget = 2  // 己方
	BuffTarget_OwnSideHeroDefense            BuffTarget = 3  // 己方防御型英雄
	BuffTarget_OwnSideHeroRanged             BuffTarget = 4  // 己方远程型英雄
	BuffTarget_OwnSideHeroSupport            BuffTarget = 5  // 己方功能型英雄
	BuffTarget_OwnSideHeroFront              BuffTarget = 6  // 己方前排英雄
	BuffTarget_OwnSideHeroBehind             BuffTarget = 7  // 己方后排英雄
	BuffTarget_OwnSideHeroHPLowest           BuffTarget = 8  // 己方生命值最低英雄（同值随机）
	BuffTarget_Opposite                      BuffTarget = 9  // 自动攻击目标
	BuffTarget_OppositeSide                  BuffTarget = 10 // 对方
	BuffTarget_OppositeSideHeroDefense       BuffTarget = 11 // 对方防御型英雄
	BuffTarget_OppositeSideHeroRanged        BuffTarget = 12 // 对方远程型英雄
	BuffTarget_OppositeSideHeroSupport       BuffTarget = 13 // 对方功能型英雄
	BuffTarget_OppoSideHeroFront             BuffTarget = 14 // 对方前排英雄
	BuffTarget_OppoSideHeroBehind            BuffTarget = 15 // 对方后排英雄
	BuffTarget_OppositeSideHeroHpLowest      BuffTarget = 16 // 对方生命值最低英雄（同值随机）
	BuffTarget_PreSkillEffectTarget          BuffTarget = 17 // 前一技能段影响目标
	BuffTarget_PreSkillEffectTargetElseBoss  BuffTarget = 18 // 前一技能段影响目标（除boss）
	BuffTarget_Boss                          BuffTarget = 19 // boss
	BuffTarget_Vehicle                       BuffTarget = 20 // 载具
	BuffTarget_OwnForward                    BuffTarget = 21 // 自己前方
	BuffTarget_OwnSideHeroMagic              BuffTarget = 22 // 己方魔法英雄
	BuffTarget_OwnSideHeroSuperPowers        BuffTarget = 23 // 己方异能英雄
	BuffTarget_OwnSideHeroTech               BuffTarget = 24 // 己方科技英雄
	BuffTarget_OppositeSideHeroMagic         BuffTarget = 25 // 对方魔法英雄
	BuffTarget_OppositeSideHeroSuperPowers   BuffTarget = 26 // 对方异能英雄
	BuffTarget_OppositeSideHeroTech          BuffTarget = 27 // 对方科技英雄
	BuffTarget_OwnSideHeroAtkHighest         BuffTarget = 28 // 己方攻击力最高英雄（同值随机）
	BuffTarget_OwnSideHeroBehindMagic        BuffTarget = 29 // 己方后排魔法英雄
	BuffTarget_OwnSideHeroBehindTech         BuffTarget = 30 // 己方后排科技英雄
	BuffTarget_OwnSideHeroFrontDefenseRandom BuffTarget = 31 // 己方随机前排防御型英雄
)

const BuffTargetEnumCount = 31

var BuffTargetEnums = [...]BuffTarget{BuffTarget_Own, BuffTarget_OwnSide, BuffTarget_OwnSideHeroDefense, BuffTarget_OwnSideHeroRanged, BuffTarget_OwnSideHeroSupport, BuffTarget_OwnSideHeroFront, BuffTarget_OwnSideHeroBehind, BuffTarget_OwnSideHeroHPLowest, BuffTarget_Opposite, BuffTarget_OppositeSide, BuffTarget_OppositeSideHeroDefense, BuffTarget_OppositeSideHeroRanged, BuffTarget_OppositeSideHeroSupport, BuffTarget_OppoSideHeroFront, BuffTarget_OppoSideHeroBehind, BuffTarget_OppositeSideHeroHpLowest, BuffTarget_PreSkillEffectTarget, BuffTarget_PreSkillEffectTargetElseBoss, BuffTarget_Boss, BuffTarget_Vehicle, BuffTarget_OwnForward, BuffTarget_OwnSideHeroMagic, BuffTarget_OwnSideHeroSuperPowers, BuffTarget_OwnSideHeroTech, BuffTarget_OppositeSideHeroMagic, BuffTarget_OppositeSideHeroSuperPowers, BuffTarget_OppositeSideHeroTech, BuffTarget_OwnSideHeroAtkHighest, BuffTarget_OwnSideHeroBehindMagic, BuffTarget_OwnSideHeroBehindTech, BuffTarget_OwnSideHeroFrontDefenseRandom}

func (t BuffTarget) String() string {
	switch t {
	case BuffTarget_Own:
		return "Own"
	case BuffTarget_OwnSide:
		return "OwnSide"
	case BuffTarget_OwnSideHeroDefense:
		return "OwnSideHeroDefense"
	case BuffTarget_OwnSideHeroRanged:
		return "OwnSideHeroRanged"
	case BuffTarget_OwnSideHeroSupport:
		return "OwnSideHeroSupport"
	case BuffTarget_OwnSideHeroFront:
		return "OwnSideHeroFront"
	case BuffTarget_OwnSideHeroBehind:
		return "OwnSideHeroBehind"
	case BuffTarget_OwnSideHeroHPLowest:
		return "OwnSideHeroHPLowest"
	case BuffTarget_Opposite:
		return "Opposite"
	case BuffTarget_OppositeSide:
		return "OppositeSide"
	case BuffTarget_OppositeSideHeroDefense:
		return "OppositeSideHeroDefense"
	case BuffTarget_OppositeSideHeroRanged:
		return "OppositeSideHeroRanged"
	case BuffTarget_OppositeSideHeroSupport:
		return "OppositeSideHeroSupport"
	case BuffTarget_OppoSideHeroFront:
		return "OppoSideHeroFront"
	case BuffTarget_OppoSideHeroBehind:
		return "OppoSideHeroBehind"
	case BuffTarget_OppositeSideHeroHpLowest:
		return "OppositeSideHeroHpLowest"
	case BuffTarget_PreSkillEffectTarget:
		return "PreSkillEffectTarget"
	case BuffTarget_PreSkillEffectTargetElseBoss:
		return "PreSkillEffectTargetElseBoss"
	case BuffTarget_Boss:
		return "Boss"
	case BuffTarget_Vehicle:
		return "Vehicle"
	case BuffTarget_OwnForward:
		return "OwnForward"
	case BuffTarget_OwnSideHeroMagic:
		return "OwnSideHeroMagic"
	case BuffTarget_OwnSideHeroSuperPowers:
		return "OwnSideHeroSuperPowers"
	case BuffTarget_OwnSideHeroTech:
		return "OwnSideHeroTech"
	case BuffTarget_OppositeSideHeroMagic:
		return "OppositeSideHeroMagic"
	case BuffTarget_OppositeSideHeroSuperPowers:
		return "OppositeSideHeroSuperPowers"
	case BuffTarget_OppositeSideHeroTech:
		return "OppositeSideHeroTech"
	case BuffTarget_OwnSideHeroAtkHighest:
		return "OwnSideHeroAtkHighest"
	case BuffTarget_OwnSideHeroBehindMagic:
		return "OwnSideHeroBehindMagic"
	case BuffTarget_OwnSideHeroBehindTech:
		return "OwnSideHeroBehindTech"
	case BuffTarget_OwnSideHeroFrontDefenseRandom:
		return "OwnSideHeroFrontDefenseRandom"
	default:
		return ""
	}
}

type BuffType int32 // Buff类型

const (
	BuffType_Buff   BuffType = 1 // 增益
	BuffType_Debuff BuffType = 2 // 减益
)

const BuffTypeEnumCount = 2

var BuffTypeEnums = [...]BuffType{BuffType_Buff, BuffType_Debuff}

func (t BuffType) String() string {
	switch t {
	case BuffType_Buff:
		return "Buff"
	case BuffType_Debuff:
		return "Debuff"
	default:
		return ""
	}
}

type CorrectType int32 // 修改类型

const (
	CorrectType_Overlying CorrectType = 1 // 叠加
	CorrectType_Cover     CorrectType = 2 // 覆盖
)

const CorrectTypeEnumCount = 2

var CorrectTypeEnums = [...]CorrectType{CorrectType_Overlying, CorrectType_Cover}

func (t CorrectType) String() string {
	switch t {
	case CorrectType_Overlying:
		return "Overlying"
	case CorrectType_Cover:
		return "Cover"
	default:
		return ""
	}
}

type DailyOrWeekly int32 // 日常或周常

const (
	DailyOrWeekly_Daily  DailyOrWeekly = 1 // 日常
	DailyOrWeekly_Weekly DailyOrWeekly = 2 // 周常
)

const DailyOrWeeklyEnumCount = 2

var DailyOrWeeklyEnums = [...]DailyOrWeekly{DailyOrWeekly_Daily, DailyOrWeekly_Weekly}

func (t DailyOrWeekly) String() string {
	switch t {
	case DailyOrWeekly_Daily:
		return "Daily"
	case DailyOrWeekly_Weekly:
		return "Weekly"
	default:
		return ""
	}
}

type DungeonType int32 // 副本类型

const (
	DungeonType_CoinDungeon      DungeonType = 1 // 金币副本
	DungeonType_GeneDungeon      DungeonType = 2 // 技能书副本
	DungeonType_LordEquipDungeon DungeonType = 3 // 培养仓齿轮副本
	DungeonType_SunshineDungeon  DungeonType = 4 // 阳光副本
)

const DungeonTypeEnumCount = 4

var DungeonTypeEnums = [...]DungeonType{DungeonType_CoinDungeon, DungeonType_GeneDungeon, DungeonType_LordEquipDungeon, DungeonType_SunshineDungeon}

func (t DungeonType) String() string {
	switch t {
	case DungeonType_CoinDungeon:
		return "CoinDungeon"
	case DungeonType_GeneDungeon:
		return "GeneDungeon"
	case DungeonType_LordEquipDungeon:
		return "LordEquipDungeon"
	case DungeonType_SunshineDungeon:
		return "SunshineDungeon"
	default:
		return ""
	}
}

type GemAffixQuality int32 // 宝石品质类型

const (
	GemAffixQuality_GemAffixQuality1 GemAffixQuality = 1 // 专属
	GemAffixQuality_GemAffixQuality2 GemAffixQuality = 2 // 稀有
	GemAffixQuality_GemAffixQuality3 GemAffixQuality = 3 // 基础
)

const GemAffixQualityEnumCount = 3

var GemAffixQualityEnums = [...]GemAffixQuality{GemAffixQuality_GemAffixQuality1, GemAffixQuality_GemAffixQuality2, GemAffixQuality_GemAffixQuality3}

func (t GemAffixQuality) String() string {
	switch t {
	case GemAffixQuality_GemAffixQuality1:
		return "GemAffixQuality1"
	case GemAffixQuality_GemAffixQuality2:
		return "GemAffixQuality2"
	case GemAffixQuality_GemAffixQuality3:
		return "GemAffixQuality3"
	default:
		return ""
	}
}

type GemQualityType int32 // 宝石品质类型

const (
	GemQualityType_GemQualityType1 GemQualityType = 1 // 破损
	GemQualityType_GemQualityType2 GemQualityType = 2 // 普通
	GemQualityType_GemQualityType3 GemQualityType = 3 // 优秀
	GemQualityType_GemQualityType4 GemQualityType = 4 // 史诗
	GemQualityType_GemQualityType5 GemQualityType = 5 // 传说
	GemQualityType_GemQualityType6 GemQualityType = 6 // 神话
	GemQualityType_GemQualityType7 GemQualityType = 7 // 至尊
)

const GemQualityTypeEnumCount = 7

var GemQualityTypeEnums = [...]GemQualityType{GemQualityType_GemQualityType1, GemQualityType_GemQualityType2, GemQualityType_GemQualityType3, GemQualityType_GemQualityType4, GemQualityType_GemQualityType5, GemQualityType_GemQualityType6, GemQualityType_GemQualityType7}

func (t GemQualityType) String() string {
	switch t {
	case GemQualityType_GemQualityType1:
		return "GemQualityType1"
	case GemQualityType_GemQualityType2:
		return "GemQualityType2"
	case GemQualityType_GemQualityType3:
		return "GemQualityType3"
	case GemQualityType_GemQualityType4:
		return "GemQualityType4"
	case GemQualityType_GemQualityType5:
		return "GemQualityType5"
	case GemQualityType_GemQualityType6:
		return "GemQualityType6"
	case GemQualityType_GemQualityType7:
		return "GemQualityType7"
	default:
		return ""
	}
}

type GuildFlagType int32 // 公会权限

const (
	GuildFlagType_Base  GuildFlagType = 1 // 底座
	GuildFlagType_Badge GuildFlagType = 2 // 纹章
)

const GuildFlagTypeEnumCount = 2

var GuildFlagTypeEnums = [...]GuildFlagType{GuildFlagType_Base, GuildFlagType_Badge}

func (t GuildFlagType) String() string {
	switch t {
	case GuildFlagType_Base:
		return "Base"
	case GuildFlagType_Badge:
		return "Badge"
	default:
		return ""
	}
}

type GuildPermission int32 // 公会权限

const (
	GuildPermission_ChangeGuildFlag       GuildPermission = 1  // 变更旗帜
	GuildPermission_ChangeGuildShortName  GuildPermission = 2  // 变更简称
	GuildPermission_ChangeGuildName       GuildPermission = 3  // 变更名称
	GuildPermission_EditNotice            GuildPermission = 4  // 编辑公告
	GuildPermission_ChangeRecruitSetting  GuildPermission = 5  // 变更招募设定
	GuildPermission_ManageJoinApplication GuildPermission = 6  // 管理入会申請
	GuildPermission_DisbandGuild          GuildPermission = 7  // 解散公会
	GuildPermission_TransferPresident     GuildPermission = 8  // 转让会长
	GuildPermission_RemoveMember          GuildPermission = 9  // 移除成员
	GuildPermission_ChangeMemberRank      GuildPermission = 10 // 变更成员阶级
	GuildPermission_ViewMemberInfo        GuildPermission = 11 // 查看成员信息
	GuildPermission_ChangeRankTitle       GuildPermission = 12 // 变更阶级头衔
	GuildPermission_ChangeLanguage        GuildPermission = 13 // 变更语言
	GuildPermission_ExitGuild             GuildPermission = 14 // 退出公会
)

const GuildPermissionEnumCount = 14

var GuildPermissionEnums = [...]GuildPermission{GuildPermission_ChangeGuildFlag, GuildPermission_ChangeGuildShortName, GuildPermission_ChangeGuildName, GuildPermission_EditNotice, GuildPermission_ChangeRecruitSetting, GuildPermission_ManageJoinApplication, GuildPermission_DisbandGuild, GuildPermission_TransferPresident, GuildPermission_RemoveMember, GuildPermission_ChangeMemberRank, GuildPermission_ViewMemberInfo, GuildPermission_ChangeRankTitle, GuildPermission_ChangeLanguage, GuildPermission_ExitGuild}

func (t GuildPermission) String() string {
	switch t {
	case GuildPermission_ChangeGuildFlag:
		return "ChangeGuildFlag"
	case GuildPermission_ChangeGuildShortName:
		return "ChangeGuildShortName"
	case GuildPermission_ChangeGuildName:
		return "ChangeGuildName"
	case GuildPermission_EditNotice:
		return "EditNotice"
	case GuildPermission_ChangeRecruitSetting:
		return "ChangeRecruitSetting"
	case GuildPermission_ManageJoinApplication:
		return "ManageJoinApplication"
	case GuildPermission_DisbandGuild:
		return "DisbandGuild"
	case GuildPermission_TransferPresident:
		return "TransferPresident"
	case GuildPermission_RemoveMember:
		return "RemoveMember"
	case GuildPermission_ChangeMemberRank:
		return "ChangeMemberRank"
	case GuildPermission_ViewMemberInfo:
		return "ViewMemberInfo"
	case GuildPermission_ChangeRankTitle:
		return "ChangeRankTitle"
	case GuildPermission_ChangeLanguage:
		return "ChangeLanguage"
	case GuildPermission_ExitGuild:
		return "ExitGuild"
	default:
		return ""
	}
}

type GuildRank int32 // 公会权限

const (
	GuildRank_Rank5 GuildRank = 1 // 5阶
	GuildRank_Rank4 GuildRank = 2 // 4阶
	GuildRank_Rank3 GuildRank = 3 // 3阶
	GuildRank_Rank2 GuildRank = 4 // 2阶
	GuildRank_Rank1 GuildRank = 5 // 1阶
)

const GuildRankEnumCount = 5

var GuildRankEnums = [...]GuildRank{GuildRank_Rank5, GuildRank_Rank4, GuildRank_Rank3, GuildRank_Rank2, GuildRank_Rank1}

func (t GuildRank) String() string {
	switch t {
	case GuildRank_Rank5:
		return "Rank5"
	case GuildRank_Rank4:
		return "Rank4"
	case GuildRank_Rank3:
		return "Rank3"
	case GuildRank_Rank2:
		return "Rank2"
	case GuildRank_Rank1:
		return "Rank1"
	default:
		return ""
	}
}

type HeroCareer int32 // 英雄职业

const (
	HeroCareer_HeroDefense HeroCareer = 1 // 防御型
	HeroCareer_HeroRanged  HeroCareer = 2 // 远程型
	HeroCareer_HeroSupport HeroCareer = 3 // 支援型
)

const HeroCareerEnumCount = 3

var HeroCareerEnums = [...]HeroCareer{HeroCareer_HeroDefense, HeroCareer_HeroRanged, HeroCareer_HeroSupport}

func (t HeroCareer) String() string {
	switch t {
	case HeroCareer_HeroDefense:
		return "HeroDefense"
	case HeroCareer_HeroRanged:
		return "HeroRanged"
	case HeroCareer_HeroSupport:
		return "HeroSupport"
	default:
		return ""
	}
}

type HeroConfig int32 // 英雄位置

const (
	HeroConfig_HeroConfig1 HeroConfig = 1 // 前排左
	HeroConfig_HeroConfig2 HeroConfig = 2 // 后排中
	HeroConfig_HeroConfig3 HeroConfig = 3 // 后排左
	HeroConfig_HeroConfig4 HeroConfig = 4 // 前排右
	HeroConfig_HeroConfig5 HeroConfig = 5 // 后排右
)

const HeroConfigEnumCount = 5

var HeroConfigEnums = [...]HeroConfig{HeroConfig_HeroConfig1, HeroConfig_HeroConfig2, HeroConfig_HeroConfig3, HeroConfig_HeroConfig4, HeroConfig_HeroConfig5}

func (t HeroConfig) String() string {
	switch t {
	case HeroConfig_HeroConfig1:
		return "HeroConfig1"
	case HeroConfig_HeroConfig2:
		return "HeroConfig2"
	case HeroConfig_HeroConfig3:
		return "HeroConfig3"
	case HeroConfig_HeroConfig4:
		return "HeroConfig4"
	case HeroConfig_HeroConfig5:
		return "HeroConfig5"
	default:
		return ""
	}
}

type HeroLevelUpPlan int32 // 英雄升级方案

const (
	HeroLevelUpPlan_HeroLevelPlanLegendaryDefense HeroLevelUpPlan = 1 // ssr肉盾升星方案
	HeroLevelUpPlan_HeroLevelPlanLegendaryRanged  HeroLevelUpPlan = 2 // ssr远程升星方案
	HeroLevelUpPlan_HeroLevelPlanLegendarySupport HeroLevelUpPlan = 3 // ssr支援升星方案
	HeroLevelUpPlan_HeroLevelPlanEpicDefense      HeroLevelUpPlan = 4 // sr肉盾升星方案
	HeroLevelUpPlan_HeroLevelPlanEpicRanged       HeroLevelUpPlan = 5 // sr远程升星方案
	HeroLevelUpPlan_HeroLevelPlanEpicSupport      HeroLevelUpPlan = 6 // sr支援升星方案
	HeroLevelUpPlan_HeroLevelPlanRareDefense      HeroLevelUpPlan = 7 // r肉盾升星方案
	HeroLevelUpPlan_HeroLevelPlanRareRanged       HeroLevelUpPlan = 8 // r远程升星方案
	HeroLevelUpPlan_HeroLevelPlanRareSupport      HeroLevelUpPlan = 9 // r支援升星方案
)

const HeroLevelUpPlanEnumCount = 9

var HeroLevelUpPlanEnums = [...]HeroLevelUpPlan{HeroLevelUpPlan_HeroLevelPlanLegendaryDefense, HeroLevelUpPlan_HeroLevelPlanLegendaryRanged, HeroLevelUpPlan_HeroLevelPlanLegendarySupport, HeroLevelUpPlan_HeroLevelPlanEpicDefense, HeroLevelUpPlan_HeroLevelPlanEpicRanged, HeroLevelUpPlan_HeroLevelPlanEpicSupport, HeroLevelUpPlan_HeroLevelPlanRareDefense, HeroLevelUpPlan_HeroLevelPlanRareRanged, HeroLevelUpPlan_HeroLevelPlanRareSupport}

func (t HeroLevelUpPlan) String() string {
	switch t {
	case HeroLevelUpPlan_HeroLevelPlanLegendaryDefense:
		return "HeroLevelPlanLegendaryDefense"
	case HeroLevelUpPlan_HeroLevelPlanLegendaryRanged:
		return "HeroLevelPlanLegendaryRanged"
	case HeroLevelUpPlan_HeroLevelPlanLegendarySupport:
		return "HeroLevelPlanLegendarySupport"
	case HeroLevelUpPlan_HeroLevelPlanEpicDefense:
		return "HeroLevelPlanEpicDefense"
	case HeroLevelUpPlan_HeroLevelPlanEpicRanged:
		return "HeroLevelPlanEpicRanged"
	case HeroLevelUpPlan_HeroLevelPlanEpicSupport:
		return "HeroLevelPlanEpicSupport"
	case HeroLevelUpPlan_HeroLevelPlanRareDefense:
		return "HeroLevelPlanRareDefense"
	case HeroLevelUpPlan_HeroLevelPlanRareRanged:
		return "HeroLevelPlanRareRanged"
	case HeroLevelUpPlan_HeroLevelPlanRareSupport:
		return "HeroLevelPlanRareSupport"
	default:
		return ""
	}
}

type HeroQuality int32 // 英雄品质

const (
	HeroQuality_HeroLegendary HeroQuality = 1 // 传说
	HeroQuality_HeroEpic      HeroQuality = 2 // 史诗
	HeroQuality_HeroRare      HeroQuality = 3 // 稀有
)

const HeroQualityEnumCount = 3

var HeroQualityEnums = [...]HeroQuality{HeroQuality_HeroLegendary, HeroQuality_HeroEpic, HeroQuality_HeroRare}

func (t HeroQuality) String() string {
	switch t {
	case HeroQuality_HeroLegendary:
		return "HeroLegendary"
	case HeroQuality_HeroEpic:
		return "HeroEpic"
	case HeroQuality_HeroRare:
		return "HeroRare"
	default:
		return ""
	}
}

type HeroSkillBuffType int32 // 英雄技能Buff类型

const (
	HeroSkillBuffType_Benefit                 HeroSkillBuffType = 1  // Benefit
	HeroSkillBuffType_Silent                  HeroSkillBuffType = 2  // 沉默
	HeroSkillBuffType_Stun                    HeroSkillBuffType = 3  // 眩晕
	HeroSkillBuffType_Paralysis               HeroSkillBuffType = 4  // 麻痹
	HeroSkillBuffType_Sleep                   HeroSkillBuffType = 5  // 睡眠
	HeroSkillBuffType_Bind                    HeroSkillBuffType = 6  // 束缚
	HeroSkillBuffType_Immortal                HeroSkillBuffType = 7  // 不死
	HeroSkillBuffType_Veil                    HeroSkillBuffType = 8  // 伤害无效化
	HeroSkillBuffType_Stealth                 HeroSkillBuffType = 9  // 隐身
	HeroSkillBuffType_Curse                   HeroSkillBuffType = 10 // 诅咒
	HeroSkillBuffType_Dot_bleed               HeroSkillBuffType = 11 // dot_流血
	HeroSkillBuffType_Dot_poison              HeroSkillBuffType = 12 // dot_中毒
	HeroSkillBuffType_Dot_frostbite           HeroSkillBuffType = 13 // dot_冻伤
	HeroSkillBuffType_Dot_burn                HeroSkillBuffType = 14 // dot_灼烧
	HeroSkillBuffType_Block                   HeroSkillBuffType = 15 // 格挡
	HeroSkillBuffType_Unrevive                HeroSkillBuffType = 16 // 无法复活
	HeroSkillBuffType_EternalSlumber          HeroSkillBuffType = 17 // 长睡不醒
	HeroSkillBuffType_Tense                   HeroSkillBuffType = 18 // 硬直
	HeroSkillBuffType_Immunity                HeroSkillBuffType = 19 // 免疫
	HeroSkillBuffType_Shield                  HeroSkillBuffType = 20 // 护盾
	HeroSkillBuffType_HalfAsleep              HeroSkillBuffType = 21 // 半睡半醒
	HeroSkillBuffType_Nightmare               HeroSkillBuffType = 22 // 梦魇
	HeroSkillBuffType_LifeSteal               HeroSkillBuffType = 23 // 吸血
	HeroSkillBuffType_Revive                  HeroSkillBuffType = 24 // 复活
	HeroSkillBuffType_HpRecovery              HeroSkillBuffType = 25 // 回血
	HeroSkillBuffType_SkillSwitch             HeroSkillBuffType = 26 // 技能转换
	HeroSkillBuffType_Taunted                 HeroSkillBuffType = 27 // 挑衅
	HeroSkillBuffType_Dmg                     HeroSkillBuffType = 28 // 伤害
	HeroSkillBuffType_RemoveBuff              HeroSkillBuffType = 29 // 移除buff
	HeroSkillBuffType_OnSideExplosion         HeroSkillBuffType = 30 // 死后爆炸
	HeroSkillBuffType_Frozen                  HeroSkillBuffType = 31 // 冻结
	HeroSkillBuffType_Repel                   HeroSkillBuffType = 32 // 击退
	HeroSkillBuffType_Pull                    HeroSkillBuffType = 33 // 牵引
	HeroSkillBuffType_LightingStruck          HeroSkillBuffType = 34 // 招雷劈
	HeroSkillBuffType_Vulnerability           HeroSkillBuffType = 35 // 易伤
	HeroSkillBuffType_Lame                    HeroSkillBuffType = 36 // 减速
	HeroSkillBuffType_Rampage                 HeroSkillBuffType = 37 // 爆发移速
	HeroSkillBuffType_BuffDelay               HeroSkillBuffType = 38 // Buff延时
	HeroSkillBuffType_CoolingOff              HeroSkillBuffType = 39 // 冷静期
	HeroSkillBuffType_RecoveryDown            HeroSkillBuffType = 40 // 重伤
	HeroSkillBuffType_Armour                  HeroSkillBuffType = 41 // 护甲
	HeroSkillBuffType_FrozenHpRecovery        HeroSkillBuffType = 42 // 冻结回血
	HeroSkillBuffType_ElectrostaticSputtering HeroSkillBuffType = 43 // 电系溅射
	HeroSkillBuffType_InjuryHealing           HeroSkillBuffType = 44 // 受伤回血
	HeroSkillBuffType_Summon                  HeroSkillBuffType = 45 // 召唤
	HeroSkillBuffType_CrabWalk                HeroSkillBuffType = 46 // 螃蟹步
	HeroSkillBuffType_Invulnerable            HeroSkillBuffType = 47 // 无敌
	HeroSkillBuffType_Split                   HeroSkillBuffType = 48 // 分裂
	HeroSkillBuffType_Trigger                 HeroSkillBuffType = 49 // 触发器
	HeroSkillBuffType_Excavation              HeroSkillBuffType = 50 // 钻地
	HeroSkillBuffType_TombStone               HeroSkillBuffType = 51 // 墓碑
	HeroSkillBuffType_InstantDeath            HeroSkillBuffType = 52 // 即死
	HeroSkillBuffType_RangeRampage            HeroSkillBuffType = 53 // 群体爆发移速
	HeroSkillBuffType_Dot_wind                HeroSkillBuffType = 54 // dot_风蚀
	HeroSkillBuffType_ConditionTrigger        HeroSkillBuffType = 55 // 条件触发器
	HeroSkillBuffType_Immolate                HeroSkillBuffType = 56 // 献祭
	HeroSkillBuffType_HpSwitch                HeroSkillBuffType = 57 // 生命交换机
	HeroSkillBuffType_StepingStone            HeroSkillBuffType = 58 // 嫁衣
	HeroSkillBuffType_EDER                    HeroSkillBuffType = 59 // 早死早托生
	HeroSkillBuffType_Shippuden               HeroSkillBuffType = 60 // 疾风
)

const HeroSkillBuffTypeEnumCount = 60

var HeroSkillBuffTypeEnums = [...]HeroSkillBuffType{HeroSkillBuffType_Benefit, HeroSkillBuffType_Silent, HeroSkillBuffType_Stun, HeroSkillBuffType_Paralysis, HeroSkillBuffType_Sleep, HeroSkillBuffType_Bind, HeroSkillBuffType_Immortal, HeroSkillBuffType_Veil, HeroSkillBuffType_Stealth, HeroSkillBuffType_Curse, HeroSkillBuffType_Dot_bleed, HeroSkillBuffType_Dot_poison, HeroSkillBuffType_Dot_frostbite, HeroSkillBuffType_Dot_burn, HeroSkillBuffType_Block, HeroSkillBuffType_Unrevive, HeroSkillBuffType_EternalSlumber, HeroSkillBuffType_Tense, HeroSkillBuffType_Immunity, HeroSkillBuffType_Shield, HeroSkillBuffType_HalfAsleep, HeroSkillBuffType_Nightmare, HeroSkillBuffType_LifeSteal, HeroSkillBuffType_Revive, HeroSkillBuffType_HpRecovery, HeroSkillBuffType_SkillSwitch, HeroSkillBuffType_Taunted, HeroSkillBuffType_Dmg, HeroSkillBuffType_RemoveBuff, HeroSkillBuffType_OnSideExplosion, HeroSkillBuffType_Frozen, HeroSkillBuffType_Repel, HeroSkillBuffType_Pull, HeroSkillBuffType_LightingStruck, HeroSkillBuffType_Vulnerability, HeroSkillBuffType_Lame, HeroSkillBuffType_Rampage, HeroSkillBuffType_BuffDelay, HeroSkillBuffType_CoolingOff, HeroSkillBuffType_RecoveryDown, HeroSkillBuffType_Armour, HeroSkillBuffType_FrozenHpRecovery, HeroSkillBuffType_ElectrostaticSputtering, HeroSkillBuffType_InjuryHealing, HeroSkillBuffType_Summon, HeroSkillBuffType_CrabWalk, HeroSkillBuffType_Invulnerable, HeroSkillBuffType_Split, HeroSkillBuffType_Trigger, HeroSkillBuffType_Excavation, HeroSkillBuffType_TombStone, HeroSkillBuffType_InstantDeath, HeroSkillBuffType_RangeRampage, HeroSkillBuffType_Dot_wind, HeroSkillBuffType_ConditionTrigger, HeroSkillBuffType_Immolate, HeroSkillBuffType_HpSwitch, HeroSkillBuffType_StepingStone, HeroSkillBuffType_EDER, HeroSkillBuffType_Shippuden}

func (t HeroSkillBuffType) String() string {
	switch t {
	case HeroSkillBuffType_Benefit:
		return "Benefit"
	case HeroSkillBuffType_Silent:
		return "Silent"
	case HeroSkillBuffType_Stun:
		return "Stun"
	case HeroSkillBuffType_Paralysis:
		return "Paralysis"
	case HeroSkillBuffType_Sleep:
		return "Sleep"
	case HeroSkillBuffType_Bind:
		return "Bind"
	case HeroSkillBuffType_Immortal:
		return "Immortal"
	case HeroSkillBuffType_Veil:
		return "Veil"
	case HeroSkillBuffType_Stealth:
		return "Stealth"
	case HeroSkillBuffType_Curse:
		return "Curse"
	case HeroSkillBuffType_Dot_bleed:
		return "Dot_bleed"
	case HeroSkillBuffType_Dot_poison:
		return "Dot_poison"
	case HeroSkillBuffType_Dot_frostbite:
		return "Dot_frostbite"
	case HeroSkillBuffType_Dot_burn:
		return "Dot_burn"
	case HeroSkillBuffType_Block:
		return "Block"
	case HeroSkillBuffType_Unrevive:
		return "Unrevive"
	case HeroSkillBuffType_EternalSlumber:
		return "EternalSlumber"
	case HeroSkillBuffType_Tense:
		return "Tense"
	case HeroSkillBuffType_Immunity:
		return "Immunity"
	case HeroSkillBuffType_Shield:
		return "Shield"
	case HeroSkillBuffType_HalfAsleep:
		return "HalfAsleep"
	case HeroSkillBuffType_Nightmare:
		return "Nightmare"
	case HeroSkillBuffType_LifeSteal:
		return "LifeSteal"
	case HeroSkillBuffType_Revive:
		return "Revive"
	case HeroSkillBuffType_HpRecovery:
		return "HpRecovery"
	case HeroSkillBuffType_SkillSwitch:
		return "SkillSwitch"
	case HeroSkillBuffType_Taunted:
		return "Taunted"
	case HeroSkillBuffType_Dmg:
		return "Dmg"
	case HeroSkillBuffType_RemoveBuff:
		return "RemoveBuff"
	case HeroSkillBuffType_OnSideExplosion:
		return "OnSideExplosion"
	case HeroSkillBuffType_Frozen:
		return "Frozen"
	case HeroSkillBuffType_Repel:
		return "Repel"
	case HeroSkillBuffType_Pull:
		return "Pull"
	case HeroSkillBuffType_LightingStruck:
		return "LightingStruck"
	case HeroSkillBuffType_Vulnerability:
		return "Vulnerability"
	case HeroSkillBuffType_Lame:
		return "Lame"
	case HeroSkillBuffType_Rampage:
		return "Rampage"
	case HeroSkillBuffType_BuffDelay:
		return "BuffDelay"
	case HeroSkillBuffType_CoolingOff:
		return "CoolingOff"
	case HeroSkillBuffType_RecoveryDown:
		return "RecoveryDown"
	case HeroSkillBuffType_Armour:
		return "Armour"
	case HeroSkillBuffType_FrozenHpRecovery:
		return "FrozenHpRecovery"
	case HeroSkillBuffType_ElectrostaticSputtering:
		return "ElectrostaticSputtering"
	case HeroSkillBuffType_InjuryHealing:
		return "InjuryHealing"
	case HeroSkillBuffType_Summon:
		return "Summon"
	case HeroSkillBuffType_CrabWalk:
		return "CrabWalk"
	case HeroSkillBuffType_Invulnerable:
		return "Invulnerable"
	case HeroSkillBuffType_Split:
		return "Split"
	case HeroSkillBuffType_Trigger:
		return "Trigger"
	case HeroSkillBuffType_Excavation:
		return "Excavation"
	case HeroSkillBuffType_TombStone:
		return "TombStone"
	case HeroSkillBuffType_InstantDeath:
		return "InstantDeath"
	case HeroSkillBuffType_RangeRampage:
		return "RangeRampage"
	case HeroSkillBuffType_Dot_wind:
		return "Dot_wind"
	case HeroSkillBuffType_ConditionTrigger:
		return "ConditionTrigger"
	case HeroSkillBuffType_Immolate:
		return "Immolate"
	case HeroSkillBuffType_HpSwitch:
		return "HpSwitch"
	case HeroSkillBuffType_StepingStone:
		return "StepingStone"
	case HeroSkillBuffType_EDER:
		return "EDER"
	case HeroSkillBuffType_Shippuden:
		return "Shippuden"
	default:
		return ""
	}
}

type HeroSkillDmgType int32 // 英雄技能伤害类型

const (
	HeroSkillDmgType_DmgValue            HeroSkillDmgType = 1 // 伤害值
	HeroSkillDmgType_DmgRatio            HeroSkillDmgType = 2 // 伤害系数
	HeroSkillDmgType_MaxHpPerDmg         HeroSkillDmgType = 3 // 最大生命百分比伤害
	HeroSkillDmgType_CurHpPerDmg         HeroSkillDmgType = 4 // 当前生命百分比伤害
	HeroSkillDmgType_LossHpPerDmg        HeroSkillDmgType = 5 // 损失生命百分比伤害
	HeroSkillDmgType_InheritedSkillRatio HeroSkillDmgType = 6 // 继承原技能的技能系数
)

const HeroSkillDmgTypeEnumCount = 6

var HeroSkillDmgTypeEnums = [...]HeroSkillDmgType{HeroSkillDmgType_DmgValue, HeroSkillDmgType_DmgRatio, HeroSkillDmgType_MaxHpPerDmg, HeroSkillDmgType_CurHpPerDmg, HeroSkillDmgType_LossHpPerDmg, HeroSkillDmgType_InheritedSkillRatio}

func (t HeroSkillDmgType) String() string {
	switch t {
	case HeroSkillDmgType_DmgValue:
		return "DmgValue"
	case HeroSkillDmgType_DmgRatio:
		return "DmgRatio"
	case HeroSkillDmgType_MaxHpPerDmg:
		return "MaxHpPerDmg"
	case HeroSkillDmgType_CurHpPerDmg:
		return "CurHpPerDmg"
	case HeroSkillDmgType_LossHpPerDmg:
		return "LossHpPerDmg"
	case HeroSkillDmgType_InheritedSkillRatio:
		return "InheritedSkillRatio"
	default:
		return ""
	}
}

type HeroSkillEffectType int32 // 技能效果类型

const (
	HeroSkillEffectType_Buff                HeroSkillEffectType = 1  // Buff
	HeroSkillEffectType_GuideLaser          HeroSkillEffectType = 2  // 玉米加特林
	HeroSkillEffectType_BulletFire          HeroSkillEffectType = 3  // 火箭
	HeroSkillEffectType_IceRose             HeroSkillEffectType = 4  // 冰霜玫瑰
	HeroSkillEffectType_BulletWind          HeroSkillEffectType = 5  // 空气刀
	HeroSkillEffectType_Laser               HeroSkillEffectType = 6  // 激光
	HeroSkillEffectType_ElectricFierce      HeroSkillEffectType = 7  // 电刺
	HeroSkillEffectType_Airdrop             HeroSkillEffectType = 8  // 空投
	HeroSkillEffectType_ElectricArc         HeroSkillEffectType = 9  // 电弧
	HeroSkillEffectType_BulletIce           HeroSkillEffectType = 10 // 冰弹
	HeroSkillEffectType_DragonFlame         HeroSkillEffectType = 11 // 龙焰
	HeroSkillEffectType_Car                 HeroSkillEffectType = 12 // 遥控车
	HeroSkillEffectType_Shrapnel            HeroSkillEffectType = 13 // 霰弹
	HeroSkillEffectType_Cyclone             HeroSkillEffectType = 14 // 旋风
	HeroSkillEffectType_BulletPea           HeroSkillEffectType = 15 // 豌豆弹
	HeroSkillEffectType_HandSword           HeroSkillEffectType = 16 // 手里剑
	HeroSkillEffectType_Missile             HeroSkillEffectType = 17 // 导弹
	HeroSkillEffectType_MeleeAtk            HeroSkillEffectType = 18 // 近战攻击
	HeroSkillEffectType_SummonMonster       HeroSkillEffectType = 19 // 召唤怪物
	HeroSkillEffectType_Aoe                 HeroSkillEffectType = 20 // 范围伤害
	HeroSkillEffectType_UnlockTabLevelLimit HeroSkillEffectType = 21 // 解锁选项卡等级限制
	HeroSkillEffectType_GetTab              HeroSkillEffectType = 22 // 直接获得选项卡
	HeroSkillEffectType_UnlockTab           HeroSkillEffectType = 23 // 选项卡直接进卡池
	HeroSkillEffectType_HomelanderLaser     HeroSkillEffectType = 24 // 祖国人激光
	HeroSkillEffectType_ReplacedTab         HeroSkillEffectType = 25 // 替换选项卡
	HeroSkillEffectType_BossEarLaser        HeroSkillEffectType = 26 // Boss耳朵激光
	HeroSkillEffectType_BossMouseLaser      HeroSkillEffectType = 27 // Boss嘴巴激光
	HeroSkillEffectType_Boss2               HeroSkillEffectType = 28 // Boss2
	HeroSkillEffectType_MultipleMeleeAtk    HeroSkillEffectType = 29 // 多段近战攻击
	HeroSkillEffectType_SuicideBombing      HeroSkillEffectType = 30 // 自爆
	HeroSkillEffectType_Phase2Boss1         HeroSkillEffectType = 31 // Phase2Boss1
	HeroSkillEffectType_Phase2Boss6         HeroSkillEffectType = 32 // Phase2Boss4
)

const HeroSkillEffectTypeEnumCount = 32

var HeroSkillEffectTypeEnums = [...]HeroSkillEffectType{HeroSkillEffectType_Buff, HeroSkillEffectType_GuideLaser, HeroSkillEffectType_BulletFire, HeroSkillEffectType_IceRose, HeroSkillEffectType_BulletWind, HeroSkillEffectType_Laser, HeroSkillEffectType_ElectricFierce, HeroSkillEffectType_Airdrop, HeroSkillEffectType_ElectricArc, HeroSkillEffectType_BulletIce, HeroSkillEffectType_DragonFlame, HeroSkillEffectType_Car, HeroSkillEffectType_Shrapnel, HeroSkillEffectType_Cyclone, HeroSkillEffectType_BulletPea, HeroSkillEffectType_HandSword, HeroSkillEffectType_Missile, HeroSkillEffectType_MeleeAtk, HeroSkillEffectType_SummonMonster, HeroSkillEffectType_Aoe, HeroSkillEffectType_UnlockTabLevelLimit, HeroSkillEffectType_GetTab, HeroSkillEffectType_UnlockTab, HeroSkillEffectType_HomelanderLaser, HeroSkillEffectType_ReplacedTab, HeroSkillEffectType_BossEarLaser, HeroSkillEffectType_BossMouseLaser, HeroSkillEffectType_Boss2, HeroSkillEffectType_MultipleMeleeAtk, HeroSkillEffectType_SuicideBombing, HeroSkillEffectType_Phase2Boss1, HeroSkillEffectType_Phase2Boss6}

func (t HeroSkillEffectType) String() string {
	switch t {
	case HeroSkillEffectType_Buff:
		return "Buff"
	case HeroSkillEffectType_GuideLaser:
		return "GuideLaser"
	case HeroSkillEffectType_BulletFire:
		return "BulletFire"
	case HeroSkillEffectType_IceRose:
		return "IceRose"
	case HeroSkillEffectType_BulletWind:
		return "BulletWind"
	case HeroSkillEffectType_Laser:
		return "Laser"
	case HeroSkillEffectType_ElectricFierce:
		return "ElectricFierce"
	case HeroSkillEffectType_Airdrop:
		return "Airdrop"
	case HeroSkillEffectType_ElectricArc:
		return "ElectricArc"
	case HeroSkillEffectType_BulletIce:
		return "BulletIce"
	case HeroSkillEffectType_DragonFlame:
		return "DragonFlame"
	case HeroSkillEffectType_Car:
		return "Car"
	case HeroSkillEffectType_Shrapnel:
		return "Shrapnel"
	case HeroSkillEffectType_Cyclone:
		return "Cyclone"
	case HeroSkillEffectType_BulletPea:
		return "BulletPea"
	case HeroSkillEffectType_HandSword:
		return "HandSword"
	case HeroSkillEffectType_Missile:
		return "Missile"
	case HeroSkillEffectType_MeleeAtk:
		return "MeleeAtk"
	case HeroSkillEffectType_SummonMonster:
		return "SummonMonster"
	case HeroSkillEffectType_Aoe:
		return "Aoe"
	case HeroSkillEffectType_UnlockTabLevelLimit:
		return "UnlockTabLevelLimit"
	case HeroSkillEffectType_GetTab:
		return "GetTab"
	case HeroSkillEffectType_UnlockTab:
		return "UnlockTab"
	case HeroSkillEffectType_HomelanderLaser:
		return "HomelanderLaser"
	case HeroSkillEffectType_ReplacedTab:
		return "ReplacedTab"
	case HeroSkillEffectType_BossEarLaser:
		return "BossEarLaser"
	case HeroSkillEffectType_BossMouseLaser:
		return "BossMouseLaser"
	case HeroSkillEffectType_Boss2:
		return "Boss2"
	case HeroSkillEffectType_MultipleMeleeAtk:
		return "MultipleMeleeAtk"
	case HeroSkillEffectType_SuicideBombing:
		return "SuicideBombing"
	case HeroSkillEffectType_Phase2Boss1:
		return "Phase2Boss1"
	case HeroSkillEffectType_Phase2Boss6:
		return "Phase2Boss6"
	default:
		return ""
	}
}

type HeroSkillHpRecoveryType int32 // 英雄技能回血类型

const (
	HeroSkillHpRecoveryType_HpRecoveryValue   HeroSkillHpRecoveryType = 1 // 回血值
	HeroSkillHpRecoveryType_MaxHpPerRecovery  HeroSkillHpRecoveryType = 2 // 最大生命百分比回血
	HeroSkillHpRecoveryType_CurHpPerRecovery  HeroSkillHpRecoveryType = 3 // 当前生命百分比回血
	HeroSkillHpRecoveryType_LossHpPerRecovery HeroSkillHpRecoveryType = 4 // 损失生命百分比回血
	HeroSkillHpRecoveryType_DmgPerRecovery    HeroSkillHpRecoveryType = 5 // 造成伤害量百分比回血
)

const HeroSkillHpRecoveryTypeEnumCount = 5

var HeroSkillHpRecoveryTypeEnums = [...]HeroSkillHpRecoveryType{HeroSkillHpRecoveryType_HpRecoveryValue, HeroSkillHpRecoveryType_MaxHpPerRecovery, HeroSkillHpRecoveryType_CurHpPerRecovery, HeroSkillHpRecoveryType_LossHpPerRecovery, HeroSkillHpRecoveryType_DmgPerRecovery}

func (t HeroSkillHpRecoveryType) String() string {
	switch t {
	case HeroSkillHpRecoveryType_HpRecoveryValue:
		return "HpRecoveryValue"
	case HeroSkillHpRecoveryType_MaxHpPerRecovery:
		return "MaxHpPerRecovery"
	case HeroSkillHpRecoveryType_CurHpPerRecovery:
		return "CurHpPerRecovery"
	case HeroSkillHpRecoveryType_LossHpPerRecovery:
		return "LossHpPerRecovery"
	case HeroSkillHpRecoveryType_DmgPerRecovery:
		return "DmgPerRecovery"
	default:
		return ""
	}
}

type HeroSkillRangePolygon int32 // 英雄技能范围形状

const (
	HeroSkillRangePolygon_Rectangle HeroSkillRangePolygon = 1 // 矩形
	HeroSkillRangePolygon_Circular  HeroSkillRangePolygon = 2 // 圆形
	HeroSkillRangePolygon_Sector    HeroSkillRangePolygon = 3 // 扇形
)

const HeroSkillRangePolygonEnumCount = 3

var HeroSkillRangePolygonEnums = [...]HeroSkillRangePolygon{HeroSkillRangePolygon_Rectangle, HeroSkillRangePolygon_Circular, HeroSkillRangePolygon_Sector}

func (t HeroSkillRangePolygon) String() string {
	switch t {
	case HeroSkillRangePolygon_Rectangle:
		return "Rectangle"
	case HeroSkillRangePolygon_Circular:
		return "Circular"
	case HeroSkillRangePolygon_Sector:
		return "Sector"
	default:
		return ""
	}
}

type HeroSkillType int32 // 英雄技能类型

const (
	HeroSkillType_HitSkill      HeroSkillType = 1 // 主动
	HeroSkillType_NegativeSkill HeroSkillType = 2 // 被动
	HeroSkillType_GiftSkill     HeroSkillType = 3 // 天赋
)

const HeroSkillTypeEnumCount = 3

var HeroSkillTypeEnums = [...]HeroSkillType{HeroSkillType_HitSkill, HeroSkillType_NegativeSkill, HeroSkillType_GiftSkill}

func (t HeroSkillType) String() string {
	switch t {
	case HeroSkillType_HitSkill:
		return "HitSkill"
	case HeroSkillType_NegativeSkill:
		return "NegativeSkill"
	case HeroSkillType_GiftSkill:
		return "GiftSkill"
	default:
		return ""
	}
}

type HeroStarUpPlan int32 // 英雄升星方案

const (
	HeroStarUpPlan_HeroStarPlanLegendary HeroStarUpPlan = 1 // ssr升星方案
	HeroStarUpPlan_HeroStarPlanEpic      HeroStarUpPlan = 2 // sr升星方案
	HeroStarUpPlan_HeroStarPlanRare      HeroStarUpPlan = 3 // r升星方案
)

const HeroStarUpPlanEnumCount = 3

var HeroStarUpPlanEnums = [...]HeroStarUpPlan{HeroStarUpPlan_HeroStarPlanLegendary, HeroStarUpPlan_HeroStarPlanEpic, HeroStarUpPlan_HeroStarPlanRare}

func (t HeroStarUpPlan) String() string {
	switch t {
	case HeroStarUpPlan_HeroStarPlanLegendary:
		return "HeroStarPlanLegendary"
	case HeroStarUpPlan_HeroStarPlanEpic:
		return "HeroStarPlanEpic"
	case HeroStarUpPlan_HeroStarPlanRare:
		return "HeroStarPlanRare"
	default:
		return ""
	}
}

type HeroType int32 // 英雄类型

const (
	HeroType_Magic       HeroType = 1 // 魔法
	HeroType_SuperPowers HeroType = 2 // 异能
	HeroType_Tech        HeroType = 3 // 科技
)

const HeroTypeEnumCount = 3

var HeroTypeEnums = [...]HeroType{HeroType_Magic, HeroType_SuperPowers, HeroType_Tech}

func (t HeroType) String() string {
	switch t {
	case HeroType_Magic:
		return "Magic"
	case HeroType_SuperPowers:
		return "SuperPowers"
	case HeroType_Tech:
		return "Tech"
	default:
		return ""
	}
}

type IapBoothType int32 // 展位类型

const (
	IapBoothType_DiamondShop IapBoothType = 1  // 钻石商店
	IapBoothType_DailySale   IapBoothType = 2  // 每日特惠
	IapBoothType_RegularPack IapBoothType = 3  // 常规礼包
	IapBoothType_RegularBp   IapBoothType = 4  // 常规BP
	IapBoothType_NoAds       IapBoothType = 5  // 免广告特权
	IapBoothType_2X          IapBoothType = 6  // 加速特权
	IapBoothType_MonthCard   IapBoothType = 7  // 月卡
	IapBoothType_Fund        IapBoothType = 8  // 成长基金
	IapBoothType_Sign        IapBoothType = 9  // 登录好礼
	IapBoothType_TurnTable   IapBoothType = 10 // 英雄转盘
	IapBoothType_Sign7       IapBoothType = 11 // 7日签到
	IapBoothType_Life        IapBoothType = 12 // 终身卡
)

const IapBoothTypeEnumCount = 12

var IapBoothTypeEnums = [...]IapBoothType{IapBoothType_DiamondShop, IapBoothType_DailySale, IapBoothType_RegularPack, IapBoothType_RegularBp, IapBoothType_NoAds, IapBoothType_2X, IapBoothType_MonthCard, IapBoothType_Fund, IapBoothType_Sign, IapBoothType_TurnTable, IapBoothType_Sign7, IapBoothType_Life}

func (t IapBoothType) String() string {
	switch t {
	case IapBoothType_DiamondShop:
		return "DiamondShop"
	case IapBoothType_DailySale:
		return "DailySale"
	case IapBoothType_RegularPack:
		return "RegularPack"
	case IapBoothType_RegularBp:
		return "RegularBp"
	case IapBoothType_NoAds:
		return "NoAds"
	case IapBoothType_2X:
		return "2X"
	case IapBoothType_MonthCard:
		return "MonthCard"
	case IapBoothType_Fund:
		return "Fund"
	case IapBoothType_Sign:
		return "Sign"
	case IapBoothType_TurnTable:
		return "TurnTable"
	case IapBoothType_Sign7:
		return "Sign7"
	case IapBoothType_Life:
		return "Life"
	default:
		return ""
	}
}

type IapPackageType int32 // 礼包类型

const (
	IapPackageType_Diamond      IapPackageType = 1  // 钻石商店
	IapPackageType_First        IapPackageType = 2  // 首充
	IapPackageType_MonthCard    IapPackageType = 3  // 月卡
	IapPackageType_Fund         IapPackageType = 4  // 成长基金
	IapPackageType_Regular      IapPackageType = 5  // 常规礼包
	IapPackageType_DailySale    IapPackageType = 6  // 每日特惠
	IapPackageType_AdFree       IapPackageType = 7  // 免广告卡
	IapPackageType_Life         IapPackageType = 8  // 终身卡
	IapPackageType_SignBp       IapPackageType = 9  // 登录好礼
	IapPackageType_TurnActivity IapPackageType = 10 // 转盘活动
	IapPackageType_Trigger      IapPackageType = 11 // 触发礼包
)

const IapPackageTypeEnumCount = 11

var IapPackageTypeEnums = [...]IapPackageType{IapPackageType_Diamond, IapPackageType_First, IapPackageType_MonthCard, IapPackageType_Fund, IapPackageType_Regular, IapPackageType_DailySale, IapPackageType_AdFree, IapPackageType_Life, IapPackageType_SignBp, IapPackageType_TurnActivity, IapPackageType_Trigger}

func (t IapPackageType) String() string {
	switch t {
	case IapPackageType_Diamond:
		return "Diamond"
	case IapPackageType_First:
		return "First"
	case IapPackageType_MonthCard:
		return "MonthCard"
	case IapPackageType_Fund:
		return "Fund"
	case IapPackageType_Regular:
		return "Regular"
	case IapPackageType_DailySale:
		return "DailySale"
	case IapPackageType_AdFree:
		return "AdFree"
	case IapPackageType_Life:
		return "Life"
	case IapPackageType_SignBp:
		return "SignBp"
	case IapPackageType_TurnActivity:
		return "TurnActivity"
	case IapPackageType_Trigger:
		return "Trigger"
	default:
		return ""
	}
}

type ItemType int32 // 道具类型

const (
	ItemType_Chest                          ItemType = 1  // 宝箱
	ItemType_ChestSelfSelect                ItemType = 2  // 自选箱
	ItemType_Diamond                        ItemType = 3  // 钻石
	ItemType_Doughnut                       ItemType = 4  // 甜甜圈
	ItemType_SunShine                       ItemType = 5  // 阳光
	ItemType_SummonCard                     ItemType = 6  // 招募卡
	ItemType_SeedBagCommon                  ItemType = 7  // 普通种子袋
	ItemType_SeedBagRare                    ItemType = 8  // 稀有种子袋
	ItemType_SeedBagEpic                    ItemType = 9  // 史诗种子袋
	ItemType_SeedBagLegendary               ItemType = 10 // 传说种子袋
	ItemType_SeedBagMyth                    ItemType = 11 // 神话种子袋
	ItemType_LegendarySkillBook             ItemType = 12 // 传说技能书
	ItemType_EpicSkillBook                  ItemType = 13 // 史诗技能书
	ItemType_RareSkillBook                  ItemType = 14 // 稀有技能书
	ItemType_UniversalLegendaryHeroFragment ItemType = 15 // 通用传说英雄碎片
	ItemType_UniversalEpicHeroFragment      ItemType = 16 // 通用史诗英雄碎片
	ItemType_UniversalRareHeroFragment      ItemType = 17 // 通用稀有英雄碎片
	ItemType_RandomLegendaryHeroFragment    ItemType = 18 // 随机传说英雄碎片
	ItemType_RandomEpicHeroFragment         ItemType = 19 // 随机史诗英雄碎片
	ItemType_RandomRareHeroFragment         ItemType = 20 // 随机稀有英雄碎片
	ItemType_Hero                           ItemType = 21 // 英雄
	ItemType_HeroFragment                   ItemType = 22 // 英雄碎片
	ItemType_Avatar                         ItemType = 23 // 头像
	ItemType_AvatarFrame                    ItemType = 24 // 头像框
	ItemType_RougeExp                       ItemType = 25 // 肉鸽经验
	ItemType_SkillBook                      ItemType = 26 // 技能书
	ItemType_Energy                         ItemType = 27 // 体力
	ItemType_HeroGeneFragment               ItemType = 28 // 英雄基因碎片
	ItemType_Coin                           ItemType = 29 // 金币
	ItemType_HeroGeneralGeneFragment        ItemType = 30 // 英雄通用基因碎片
	ItemType_GemRandom                      ItemType = 31 // 随机宝石
	ItemType_Gem                            ItemType = 32 // 宝石
	ItemType_GemReforge                     ItemType = 33 // 宝石洗练药剂
	ItemType_LordEquipManual                ItemType = 34 // 装备图纸
	ItemType_LordEquipRandomManual          ItemType = 35 // 随机装备图纸
	ItemType_GemDraw                        ItemType = 36 // 宝箱钥匙
	ItemType_GuildExp                       ItemType = 37 // 公会经验
	ItemType_GuildCoin                      ItemType = 38 // 公会币
	ItemType_ArenaCoin                      ItemType = 39 // 竞技币
	ItemType_TowerKey                       ItemType = 40 // 爬塔钥匙
	ItemType_CoinDungeonKey                 ItemType = 41 // 金币副本钥匙
	ItemType_GeneDungeonKey                 ItemType = 42 // 基因副本钥匙
	ItemType_LordEquipDungenKey             ItemType = 43 // 领主装备副本钥匙
	ItemType_LordEquipGradeUpManual         ItemType = 44 // 装备进阶图纸
	ItemType_RegularBpKey1                  ItemType = 45 // 常规BP钥匙1
	ItemType_FreeAd                         ItemType = 46 // 免广告特权
	ItemType_2X                             ItemType = 47 // 2倍速特权
	ItemType_SignKey1                       ItemType = 48 // 签到钥匙1
	ItemType_FundKey1                       ItemType = 49 // 基金钥匙1
	ItemType_MonthCard1                     ItemType = 50 // 月卡1
	ItemType_MonthCard2                     ItemType = 51 // 月卡2
	ItemType_turntablecoin                  ItemType = 52 // 转盘币
	ItemType_LordEquipMaterial              ItemType = 53 // 装备材料
	ItemType_HeroQualityUp                  ItemType = 54 // 英雄升品材料
	ItemType_SevenDayTasksScore             ItemType = 55 // 7日任务积分
	ItemType_StarUpCommonItem               ItemType = 56 // 英雄通用升星材料
)

const ItemTypeEnumCount = 56

var ItemTypeEnums = [...]ItemType{ItemType_Chest, ItemType_ChestSelfSelect, ItemType_Diamond, ItemType_Doughnut, ItemType_SunShine, ItemType_SummonCard, ItemType_SeedBagCommon, ItemType_SeedBagRare, ItemType_SeedBagEpic, ItemType_SeedBagLegendary, ItemType_SeedBagMyth, ItemType_LegendarySkillBook, ItemType_EpicSkillBook, ItemType_RareSkillBook, ItemType_UniversalLegendaryHeroFragment, ItemType_UniversalEpicHeroFragment, ItemType_UniversalRareHeroFragment, ItemType_RandomLegendaryHeroFragment, ItemType_RandomEpicHeroFragment, ItemType_RandomRareHeroFragment, ItemType_Hero, ItemType_HeroFragment, ItemType_Avatar, ItemType_AvatarFrame, ItemType_RougeExp, ItemType_SkillBook, ItemType_Energy, ItemType_HeroGeneFragment, ItemType_Coin, ItemType_HeroGeneralGeneFragment, ItemType_GemRandom, ItemType_Gem, ItemType_GemReforge, ItemType_LordEquipManual, ItemType_LordEquipRandomManual, ItemType_GemDraw, ItemType_GuildExp, ItemType_GuildCoin, ItemType_ArenaCoin, ItemType_TowerKey, ItemType_CoinDungeonKey, ItemType_GeneDungeonKey, ItemType_LordEquipDungenKey, ItemType_LordEquipGradeUpManual, ItemType_RegularBpKey1, ItemType_FreeAd, ItemType_2X, ItemType_SignKey1, ItemType_FundKey1, ItemType_MonthCard1, ItemType_MonthCard2, ItemType_turntablecoin, ItemType_LordEquipMaterial, ItemType_HeroQualityUp, ItemType_SevenDayTasksScore, ItemType_StarUpCommonItem}

func (t ItemType) String() string {
	switch t {
	case ItemType_Chest:
		return "Chest"
	case ItemType_ChestSelfSelect:
		return "ChestSelfSelect"
	case ItemType_Diamond:
		return "Diamond"
	case ItemType_Doughnut:
		return "Doughnut"
	case ItemType_SunShine:
		return "SunShine"
	case ItemType_SummonCard:
		return "SummonCard"
	case ItemType_SeedBagCommon:
		return "SeedBagCommon"
	case ItemType_SeedBagRare:
		return "SeedBagRare"
	case ItemType_SeedBagEpic:
		return "SeedBagEpic"
	case ItemType_SeedBagLegendary:
		return "SeedBagLegendary"
	case ItemType_SeedBagMyth:
		return "SeedBagMyth"
	case ItemType_LegendarySkillBook:
		return "LegendarySkillBook"
	case ItemType_EpicSkillBook:
		return "EpicSkillBook"
	case ItemType_RareSkillBook:
		return "RareSkillBook"
	case ItemType_UniversalLegendaryHeroFragment:
		return "UniversalLegendaryHeroFragment"
	case ItemType_UniversalEpicHeroFragment:
		return "UniversalEpicHeroFragment"
	case ItemType_UniversalRareHeroFragment:
		return "UniversalRareHeroFragment"
	case ItemType_RandomLegendaryHeroFragment:
		return "RandomLegendaryHeroFragment"
	case ItemType_RandomEpicHeroFragment:
		return "RandomEpicHeroFragment"
	case ItemType_RandomRareHeroFragment:
		return "RandomRareHeroFragment"
	case ItemType_Hero:
		return "Hero"
	case ItemType_HeroFragment:
		return "HeroFragment"
	case ItemType_Avatar:
		return "Avatar"
	case ItemType_AvatarFrame:
		return "AvatarFrame"
	case ItemType_RougeExp:
		return "RougeExp"
	case ItemType_SkillBook:
		return "SkillBook"
	case ItemType_Energy:
		return "Energy"
	case ItemType_HeroGeneFragment:
		return "HeroGeneFragment"
	case ItemType_Coin:
		return "Coin"
	case ItemType_HeroGeneralGeneFragment:
		return "HeroGeneralGeneFragment"
	case ItemType_GemRandom:
		return "GemRandom"
	case ItemType_Gem:
		return "Gem"
	case ItemType_GemReforge:
		return "GemReforge"
	case ItemType_LordEquipManual:
		return "LordEquipManual"
	case ItemType_LordEquipRandomManual:
		return "LordEquipRandomManual"
	case ItemType_GemDraw:
		return "GemDraw"
	case ItemType_GuildExp:
		return "GuildExp"
	case ItemType_GuildCoin:
		return "GuildCoin"
	case ItemType_ArenaCoin:
		return "ArenaCoin"
	case ItemType_TowerKey:
		return "TowerKey"
	case ItemType_CoinDungeonKey:
		return "CoinDungeonKey"
	case ItemType_GeneDungeonKey:
		return "GeneDungeonKey"
	case ItemType_LordEquipDungenKey:
		return "LordEquipDungenKey"
	case ItemType_LordEquipGradeUpManual:
		return "LordEquipGradeUpManual"
	case ItemType_RegularBpKey1:
		return "RegularBpKey1"
	case ItemType_FreeAd:
		return "FreeAd"
	case ItemType_2X:
		return "2X"
	case ItemType_SignKey1:
		return "SignKey1"
	case ItemType_FundKey1:
		return "FundKey1"
	case ItemType_MonthCard1:
		return "MonthCard1"
	case ItemType_MonthCard2:
		return "MonthCard2"
	case ItemType_turntablecoin:
		return "turntablecoin"
	case ItemType_LordEquipMaterial:
		return "LordEquipMaterial"
	case ItemType_HeroQualityUp:
		return "HeroQualityUp"
	case ItemType_SevenDayTasksScore:
		return "SevenDayTasksScore"
	case ItemType_StarUpCommonItem:
		return "StarUpCommonItem"
	default:
		return ""
	}
}

type LaserTarget int32 // 激光目标

const (
	LaserTarget_Opposite                     LaserTarget = 1  // 自动攻击目标
	LaserTarget_OppositeSideHeroDefense      LaserTarget = 2  // 对方防御型英雄
	LaserTarget_OppositeSideHeroRanged       LaserTarget = 3  // 对方远程型英雄
	LaserTarget_OppositeSideHeroSupport      LaserTarget = 4  // 对方功能型英雄
	LaserTarget_OppoSideHeroFront            LaserTarget = 5  // 对方前排英雄
	LaserTarget_OppoSideHeroBehind           LaserTarget = 6  // 对方后排英雄
	LaserTarget_OppositeSideHeroHpLowest     LaserTarget = 7  // 对方生命值最低英雄（同值随机）
	LaserTarget_PreSkillEffectTarget         LaserTarget = 8  // 前一技能段影响目标
	LaserTarget_PreSkillEffectTargetElseBoss LaserTarget = 9  // 前一技能段影响目标（除boss）
	LaserTarget_Boss                         LaserTarget = 10 // boss
	LaserTarget_OwnForward                   LaserTarget = 11 // 自己前方
)

const LaserTargetEnumCount = 11

var LaserTargetEnums = [...]LaserTarget{LaserTarget_Opposite, LaserTarget_OppositeSideHeroDefense, LaserTarget_OppositeSideHeroRanged, LaserTarget_OppositeSideHeroSupport, LaserTarget_OppoSideHeroFront, LaserTarget_OppoSideHeroBehind, LaserTarget_OppositeSideHeroHpLowest, LaserTarget_PreSkillEffectTarget, LaserTarget_PreSkillEffectTargetElseBoss, LaserTarget_Boss, LaserTarget_OwnForward}

func (t LaserTarget) String() string {
	switch t {
	case LaserTarget_Opposite:
		return "Opposite"
	case LaserTarget_OppositeSideHeroDefense:
		return "OppositeSideHeroDefense"
	case LaserTarget_OppositeSideHeroRanged:
		return "OppositeSideHeroRanged"
	case LaserTarget_OppositeSideHeroSupport:
		return "OppositeSideHeroSupport"
	case LaserTarget_OppoSideHeroFront:
		return "OppoSideHeroFront"
	case LaserTarget_OppoSideHeroBehind:
		return "OppoSideHeroBehind"
	case LaserTarget_OppositeSideHeroHpLowest:
		return "OppositeSideHeroHpLowest"
	case LaserTarget_PreSkillEffectTarget:
		return "PreSkillEffectTarget"
	case LaserTarget_PreSkillEffectTargetElseBoss:
		return "PreSkillEffectTargetElseBoss"
	case LaserTarget_Boss:
		return "Boss"
	case LaserTarget_OwnForward:
		return "OwnForward"
	default:
		return ""
	}
}

type LevelType int32 // 关卡类型

const (
	LevelType_TowerDefense LevelType = 1 // 塔防
	LevelType_ParkOur      LevelType = 2 // 跑酷
)

const LevelTypeEnumCount = 2

var LevelTypeEnums = [...]LevelType{LevelType_TowerDefense, LevelType_ParkOur}

func (t LevelType) String() string {
	switch t {
	case LevelType_TowerDefense:
		return "TowerDefense"
	case LevelType_ParkOur:
		return "ParkOur"
	default:
		return ""
	}
}

type LogicType int32 // 逻辑关系

const (
	LogicType_And    LogicType = 1 // 数字
	LogicType_Or     LogicType = 2 // 模型
	LogicType_Invert LogicType = 3 // Buff
)

const LogicTypeEnumCount = 3

var LogicTypeEnums = [...]LogicType{LogicType_And, LogicType_Or, LogicType_Invert}

func (t LogicType) String() string {
	switch t {
	case LogicType_And:
		return "And"
	case LogicType_Or:
		return "Or"
	case LogicType_Invert:
		return "Invert"
	default:
		return ""
	}
}

type LordEquipGradeType int32 // 领主装备阶级类型

const (
	LordEquipGradeType_LordEquipGrade1 LordEquipGradeType = 1 // 破损
	LordEquipGradeType_LordEquipGrade2 LordEquipGradeType = 2 // 普通
	LordEquipGradeType_LordEquipGrade3 LordEquipGradeType = 3 // 优秀
	LordEquipGradeType_LordEquipGrade4 LordEquipGradeType = 4 // 史诗
	LordEquipGradeType_LordEquipGrade5 LordEquipGradeType = 5 // 传说
	LordEquipGradeType_LordEquipGrade6 LordEquipGradeType = 6 // 神话
	LordEquipGradeType_LordEquipGrade7 LordEquipGradeType = 7 // 至尊
)

const LordEquipGradeTypeEnumCount = 7

var LordEquipGradeTypeEnums = [...]LordEquipGradeType{LordEquipGradeType_LordEquipGrade1, LordEquipGradeType_LordEquipGrade2, LordEquipGradeType_LordEquipGrade3, LordEquipGradeType_LordEquipGrade4, LordEquipGradeType_LordEquipGrade5, LordEquipGradeType_LordEquipGrade6, LordEquipGradeType_LordEquipGrade7}

func (t LordEquipGradeType) String() string {
	switch t {
	case LordEquipGradeType_LordEquipGrade1:
		return "LordEquipGrade1"
	case LordEquipGradeType_LordEquipGrade2:
		return "LordEquipGrade2"
	case LordEquipGradeType_LordEquipGrade3:
		return "LordEquipGrade3"
	case LordEquipGradeType_LordEquipGrade4:
		return "LordEquipGrade4"
	case LordEquipGradeType_LordEquipGrade5:
		return "LordEquipGrade5"
	case LordEquipGradeType_LordEquipGrade6:
		return "LordEquipGrade6"
	case LordEquipGradeType_LordEquipGrade7:
		return "LordEquipGrade7"
	default:
		return ""
	}
}

type LordEquipType int32 // 领主装备类型

const (
	LordEquipType_LordEquipType1 LordEquipType = 1 // 头盔
	LordEquipType_LordEquipType2 LordEquipType = 2 // 护臂
	LordEquipType_LordEquipType3 LordEquipType = 3 // 衣服
	LordEquipType_LordEquipType4 LordEquipType = 4 // 裤子
	LordEquipType_LordEquipType5 LordEquipType = 5 // 足具
	LordEquipType_LordEquipType6 LordEquipType = 6 // 手套
)

const LordEquipTypeEnumCount = 6

var LordEquipTypeEnums = [...]LordEquipType{LordEquipType_LordEquipType1, LordEquipType_LordEquipType2, LordEquipType_LordEquipType3, LordEquipType_LordEquipType4, LordEquipType_LordEquipType5, LordEquipType_LordEquipType6}

func (t LordEquipType) String() string {
	switch t {
	case LordEquipType_LordEquipType1:
		return "LordEquipType1"
	case LordEquipType_LordEquipType2:
		return "LordEquipType2"
	case LordEquipType_LordEquipType3:
		return "LordEquipType3"
	case LordEquipType_LordEquipType4:
		return "LordEquipType4"
	case LordEquipType_LordEquipType5:
		return "LordEquipType5"
	case LordEquipType_LordEquipType6:
		return "LordEquipType6"
	default:
		return ""
	}
}

type MapEventType int32 // 地图事件类型

const (
	MapEventType_Monster  MapEventType = 1 // 怪物
	MapEventType_Prop     MapEventType = 2 // 道具
	MapEventType_Buff     MapEventType = 3 // BUff
	MapEventType_Obstacle MapEventType = 4 // 障碍
	MapEventType_Reward   MapEventType = 5 // 奖励
)

const MapEventTypeEnumCount = 5

var MapEventTypeEnums = [...]MapEventType{MapEventType_Monster, MapEventType_Prop, MapEventType_Buff, MapEventType_Obstacle, MapEventType_Reward}

func (t MapEventType) String() string {
	switch t {
	case MapEventType_Monster:
		return "Monster"
	case MapEventType_Prop:
		return "Prop"
	case MapEventType_Buff:
		return "Buff"
	case MapEventType_Obstacle:
		return "Obstacle"
	case MapEventType_Reward:
		return "Reward"
	default:
		return ""
	}
}

type MissileTarget int32 // 导弹目标

const (
	MissileTarget_Opposite                     MissileTarget = 1  // 自动攻击目标
	MissileTarget_OppositeSideHeroDefense      MissileTarget = 2  // 对方防御型英雄
	MissileTarget_OppositeSideHeroRanged       MissileTarget = 3  // 对方远程型英雄
	MissileTarget_OppositeSideHeroSupport      MissileTarget = 4  // 对方功能型英雄
	MissileTarget_OppoSideHeroFront            MissileTarget = 5  // 对方前排英雄
	MissileTarget_OppoSideHeroBehind           MissileTarget = 6  // 对方后排英雄
	MissileTarget_OppositeSideHeroHpLowest     MissileTarget = 7  // 对方生命值最低英雄（同值随机）
	MissileTarget_PreSkillEffectTarget         MissileTarget = 8  // 前一技能段影响目标
	MissileTarget_PreSkillEffectTargetElseBoss MissileTarget = 9  // 前一技能段影响目标（除boss）
	MissileTarget_Boss                         MissileTarget = 10 // boss
)

const MissileTargetEnumCount = 10

var MissileTargetEnums = [...]MissileTarget{MissileTarget_Opposite, MissileTarget_OppositeSideHeroDefense, MissileTarget_OppositeSideHeroRanged, MissileTarget_OppositeSideHeroSupport, MissileTarget_OppoSideHeroFront, MissileTarget_OppoSideHeroBehind, MissileTarget_OppositeSideHeroHpLowest, MissileTarget_PreSkillEffectTarget, MissileTarget_PreSkillEffectTargetElseBoss, MissileTarget_Boss}

func (t MissileTarget) String() string {
	switch t {
	case MissileTarget_Opposite:
		return "Opposite"
	case MissileTarget_OppositeSideHeroDefense:
		return "OppositeSideHeroDefense"
	case MissileTarget_OppositeSideHeroRanged:
		return "OppositeSideHeroRanged"
	case MissileTarget_OppositeSideHeroSupport:
		return "OppositeSideHeroSupport"
	case MissileTarget_OppoSideHeroFront:
		return "OppoSideHeroFront"
	case MissileTarget_OppoSideHeroBehind:
		return "OppoSideHeroBehind"
	case MissileTarget_OppositeSideHeroHpLowest:
		return "OppositeSideHeroHpLowest"
	case MissileTarget_PreSkillEffectTarget:
		return "PreSkillEffectTarget"
	case MissileTarget_PreSkillEffectTargetElseBoss:
		return "PreSkillEffectTargetElseBoss"
	case MissileTarget_Boss:
		return "Boss"
	default:
		return ""
	}
}

type MonsterCareerType int32 // 怪物职业类型

const (
	MonsterCareerType_Melee      MonsterCareerType = 1 // 近战
	MonsterCareerType_Ranger     MonsterCareerType = 2 // 射手
	MonsterCareerType_Tank       MonsterCareerType = 3 // 坦克
	MonsterCareerType_Assassin   MonsterCareerType = 4 // 狂奔
	MonsterCareerType_AirMelee   MonsterCareerType = 5 // 浮空近战
	MonsterCareerType_AirRanger  MonsterCareerType = 6 // 浮空射手
	MonsterCareerType_Suicide    MonsterCareerType = 7 // 自爆
	MonsterCareerType_LongRanger MonsterCareerType = 8 // 远射
)

const MonsterCareerTypeEnumCount = 8

var MonsterCareerTypeEnums = [...]MonsterCareerType{MonsterCareerType_Melee, MonsterCareerType_Ranger, MonsterCareerType_Tank, MonsterCareerType_Assassin, MonsterCareerType_AirMelee, MonsterCareerType_AirRanger, MonsterCareerType_Suicide, MonsterCareerType_LongRanger}

func (t MonsterCareerType) String() string {
	switch t {
	case MonsterCareerType_Melee:
		return "Melee"
	case MonsterCareerType_Ranger:
		return "Ranger"
	case MonsterCareerType_Tank:
		return "Tank"
	case MonsterCareerType_Assassin:
		return "Assassin"
	case MonsterCareerType_AirMelee:
		return "AirMelee"
	case MonsterCareerType_AirRanger:
		return "AirRanger"
	case MonsterCareerType_Suicide:
		return "Suicide"
	case MonsterCareerType_LongRanger:
		return "LongRanger"
	default:
		return ""
	}
}

type MonsterGrade int32 // 怪物阶级

const (
	MonsterGrade_Common MonsterGrade = 1 // 小怪
	MonsterGrade_Elite  MonsterGrade = 2 // 精英
	MonsterGrade_Boss   MonsterGrade = 3 // 首领
)

const MonsterGradeEnumCount = 3

var MonsterGradeEnums = [...]MonsterGrade{MonsterGrade_Common, MonsterGrade_Elite, MonsterGrade_Boss}

func (t MonsterGrade) String() string {
	switch t {
	case MonsterGrade_Common:
		return "Common"
	case MonsterGrade_Elite:
		return "Elite"
	case MonsterGrade_Boss:
		return "Boss"
	default:
		return ""
	}
}

type MonsterPosType int32 // 怪物位置类型

const (
	MonsterPosType_Ground MonsterPosType = 1 // 地面
	MonsterPosType_Air    MonsterPosType = 2 // 空中
)

const MonsterPosTypeEnumCount = 2

var MonsterPosTypeEnums = [...]MonsterPosType{MonsterPosType_Ground, MonsterPosType_Air}

func (t MonsterPosType) String() string {
	switch t {
	case MonsterPosType_Ground:
		return "Ground"
	case MonsterPosType_Air:
		return "Air"
	default:
		return ""
	}
}

type MonsterRefreshType int32 // 刷怪类型

const (
	MonsterRefreshType_InitialRefresh          MonsterRefreshType = 1 // 开局刷新
	MonsterRefreshType_DelayRefresh            MonsterRefreshType = 2 // 延时刷新
	MonsterRefreshType_UpstreamDeathCntRefresh MonsterRefreshType = 3 // 上次怪死亡x只后刷新
	MonsterRefreshType_PassPlotsRefresh        MonsterRefreshType = 4 // 玩家路过地块x秒后刷新
	MonsterRefreshType_AfterRefreshing         MonsterRefreshType = 5 // 上波刷完x秒后刷新下一波
	MonsterRefreshType_AfterTheGameStarts      MonsterRefreshType = 6 // 游戏开始x秒后刷新
)

const MonsterRefreshTypeEnumCount = 6

var MonsterRefreshTypeEnums = [...]MonsterRefreshType{MonsterRefreshType_InitialRefresh, MonsterRefreshType_DelayRefresh, MonsterRefreshType_UpstreamDeathCntRefresh, MonsterRefreshType_PassPlotsRefresh, MonsterRefreshType_AfterRefreshing, MonsterRefreshType_AfterTheGameStarts}

func (t MonsterRefreshType) String() string {
	switch t {
	case MonsterRefreshType_InitialRefresh:
		return "InitialRefresh"
	case MonsterRefreshType_DelayRefresh:
		return "DelayRefresh"
	case MonsterRefreshType_UpstreamDeathCntRefresh:
		return "UpstreamDeathCntRefresh"
	case MonsterRefreshType_PassPlotsRefresh:
		return "PassPlotsRefresh"
	case MonsterRefreshType_AfterRefreshing:
		return "AfterRefreshing"
	case MonsterRefreshType_AfterTheGameStarts:
		return "AfterTheGameStarts"
	default:
		return ""
	}
}

type PurchaseLimitType int32 // 限购类型

const (
	PurchaseLimitType_DailyLimit   PurchaseLimitType = 1 // 每日限购
	PurchaseLimitType_WeeklyLimit  PurchaseLimitType = 2 // 每周限购
	PurchaseLimitType_MonthlyLimit PurchaseLimitType = 3 // 每月限购
	PurchaseLimitType_LifeLimit    PurchaseLimitType = 4 // 终身限购
	PurchaseLimitType_UnLimit      PurchaseLimitType = 5 // 无限制
)

const PurchaseLimitTypeEnumCount = 5

var PurchaseLimitTypeEnums = [...]PurchaseLimitType{PurchaseLimitType_DailyLimit, PurchaseLimitType_WeeklyLimit, PurchaseLimitType_MonthlyLimit, PurchaseLimitType_LifeLimit, PurchaseLimitType_UnLimit}

func (t PurchaseLimitType) String() string {
	switch t {
	case PurchaseLimitType_DailyLimit:
		return "DailyLimit"
	case PurchaseLimitType_WeeklyLimit:
		return "WeeklyLimit"
	case PurchaseLimitType_MonthlyLimit:
		return "MonthlyLimit"
	case PurchaseLimitType_LifeLimit:
		return "LifeLimit"
	case PurchaseLimitType_UnLimit:
		return "UnLimit"
	default:
		return ""
	}
}

type RougeTabType int32 // 选项卡类型

const (
	RougeTabType_EffectTab RougeTabType = 1 // 效果卡-对应效果
	RougeTabType_UnlockTab RougeTabType = 2 // 解锁卡-将指定卡放入卡池
	RougeTabType_ConfigTab RougeTabType = 3 // 上阵卡
)

const RougeTabTypeEnumCount = 3

var RougeTabTypeEnums = [...]RougeTabType{RougeTabType_EffectTab, RougeTabType_UnlockTab, RougeTabType_ConfigTab}

func (t RougeTabType) String() string {
	switch t {
	case RougeTabType_EffectTab:
		return "EffectTab"
	case RougeTabType_UnlockTab:
		return "UnlockTab"
	case RougeTabType_ConfigTab:
		return "ConfigTab"
	default:
		return ""
	}
}

type ShopType int32 // 商店类型

const (
	ShopType_GuildShop ShopType = 1 // 公会商店
	ShopType_ArenaShop ShopType = 2 // 竞技商店
	ShopType_BlackShop ShopType = 3 // 常规商店
	ShopType_LevelShop ShopType = 4 // 关卡商店
)

const ShopTypeEnumCount = 4

var ShopTypeEnums = [...]ShopType{ShopType_GuildShop, ShopType_ArenaShop, ShopType_BlackShop, ShopType_LevelShop}

func (t ShopType) String() string {
	switch t {
	case ShopType_GuildShop:
		return "GuildShop"
	case ShopType_ArenaShop:
		return "ArenaShop"
	case ShopType_BlackShop:
		return "BlackShop"
	case ShopType_LevelShop:
		return "LevelShop"
	default:
		return ""
	}
}

type SkillAttrOverlyingType int32 // 技能属性叠加类型

const (
	SkillAttrOverlyingType_AddOverlying  SkillAttrOverlyingType = 1 // 加法叠加
	SkillAttrOverlyingType_MulOverlying  SkillAttrOverlyingType = 2 // 乘法叠加
	SkillAttrOverlyingType_EnumOverlying SkillAttrOverlyingType = 3 // 枚举叠加
	SkillAttrOverlyingType_NoOverlying   SkillAttrOverlyingType = 4 // 不可叠加
)

const SkillAttrOverlyingTypeEnumCount = 4

var SkillAttrOverlyingTypeEnums = [...]SkillAttrOverlyingType{SkillAttrOverlyingType_AddOverlying, SkillAttrOverlyingType_MulOverlying, SkillAttrOverlyingType_EnumOverlying, SkillAttrOverlyingType_NoOverlying}

func (t SkillAttrOverlyingType) String() string {
	switch t {
	case SkillAttrOverlyingType_AddOverlying:
		return "AddOverlying"
	case SkillAttrOverlyingType_MulOverlying:
		return "MulOverlying"
	case SkillAttrOverlyingType_EnumOverlying:
		return "EnumOverlying"
	case SkillAttrOverlyingType_NoOverlying:
		return "NoOverlying"
	default:
		return ""
	}
}

type SkillDmgType int32 // 技能伤害类型

const (
	SkillDmgType_Electrical SkillDmgType = 1 // 电系
	SkillDmgType_Wind       SkillDmgType = 2 // 风系
	SkillDmgType_Light      SkillDmgType = 3 // 光系
	SkillDmgType_Fire       SkillDmgType = 4 // 火系
	SkillDmgType_Ice        SkillDmgType = 5 // 冰系
	SkillDmgType_Physical   SkillDmgType = 6 // 物理系
)

const SkillDmgTypeEnumCount = 6

var SkillDmgTypeEnums = [...]SkillDmgType{SkillDmgType_Electrical, SkillDmgType_Wind, SkillDmgType_Light, SkillDmgType_Fire, SkillDmgType_Ice, SkillDmgType_Physical}

func (t SkillDmgType) String() string {
	switch t {
	case SkillDmgType_Electrical:
		return "Electrical"
	case SkillDmgType_Wind:
		return "Wind"
	case SkillDmgType_Light:
		return "Light"
	case SkillDmgType_Fire:
		return "Fire"
	case SkillDmgType_Ice:
		return "Ice"
	case SkillDmgType_Physical:
		return "Physical"
	default:
		return ""
	}
}

type SkillType int32 // 技能类型

const (
	SkillType_ActiveSkill  SkillType = 1 // 主动技能
	SkillType_PassiveSkill SkillType = 2 // 被动技能
	SkillType_AuraSkill    SkillType = 3 // 光环技能
)

const SkillTypeEnumCount = 3

var SkillTypeEnums = [...]SkillType{SkillType_ActiveSkill, SkillType_PassiveSkill, SkillType_AuraSkill}

func (t SkillType) String() string {
	switch t {
	case SkillType_ActiveSkill:
		return "ActiveSkill"
	case SkillType_PassiveSkill:
		return "PassiveSkill"
	case SkillType_AuraSkill:
		return "AuraSkill"
	default:
		return ""
	}
}

type TaskCounterType int32 // 任务计数类型

const (
	TaskCounterType_Reset TaskCounterType = 1 // 重置
	TaskCounterType_Total TaskCounterType = 2 // 累计
)

const TaskCounterTypeEnumCount = 2

var TaskCounterTypeEnums = [...]TaskCounterType{TaskCounterType_Reset, TaskCounterType_Total}

func (t TaskCounterType) String() string {
	switch t {
	case TaskCounterType_Reset:
		return "Reset"
	case TaskCounterType_Total:
		return "Total"
	default:
		return ""
	}
}

type TaskType int32 // 任务类型

const (
	TaskType_Login                   TaskType = 1  // 每日登录
	TaskType_TotalLogin              TaskType = 2  // 累计登录
	TaskType_LevelBegin              TaskType = 3  // 关卡挑战
	TaskType_LevelPass               TaskType = 4  // 通关
	TaskType_LevelPassTo             TaskType = 5  // 通过指定关卡
	TaskType_ItemBurn                TaskType = 6  // 消耗道具
	TaskType_TotalItemBurn           TaskType = 7  // 累计消耗道具
	TaskType_HeroLevelUp             TaskType = 8  // 英雄升级
	TaskType_HeroLevelUpTo           TaskType = 9  // 英雄升至等级
	TaskType_HeroStarUp              TaskType = 10 // 英雄升星
	TaskType_HeroStarUpTo            TaskType = 11 // 英雄升至星级
	TaskType_HeroSkillUp             TaskType = 12 // 英雄技能升级
	TaskType_HeroSkillUpTo           TaskType = 13 // 英雄技能升至等级
	TaskType_HeroGeneUp              TaskType = 14 // 英雄天赋升级
	TaskType_HeroGeneUpTo            TaskType = 15 // 英雄天赋升至等级
	TaskType_KillMonster             TaskType = 16 // 击杀怪物
	TaskType_TotalKillMonster        TaskType = 17 // 累计击杀怪物
	TaskType_claim_idle_reward       TaskType = 18 // 领取挂机奖励
	TaskType_claim_pass_level_reward TaskType = 19 // 领取通关奖励
	TaskType_Chat                    TaskType = 20 // 聊天
	TaskType_Nigger                  TaskType = 21 // 好友
	TaskType_Rename                  TaskType = 22 // 改名
	TaskType_Avatar                  TaskType = 23 // 换头像
	TaskType_JoinGuild               TaskType = 24 // 加入公会
	TaskType_Sweep                   TaskType = 25 // 挂机扫荡
	TaskType_HeroSummon              TaskType = 26 // 英雄招募
	TaskType_HeroConfig              TaskType = 27 // 英雄上阵
	TaskType_LordEquipLvlUp          TaskType = 28 // 培养仓强化次数
	TaskType_LordEquipLvlUpTo        TaskType = 29 // 培养仓强化到
	TaskType_GemCraft                TaskType = 30 // 突变基因合成
	TaskType_GemSummon               TaskType = 31 // 提炼基因
	TaskType_Shopping                TaskType = 32 // 商店购物任务
	TaskType_DungeonChallenge        TaskType = 33 // 副本挑战任务
	TaskType_DungeonSweep            TaskType = 34 // 副本扫荡任务
	TaskType_ArenaChallenge          TaskType = 35 // 竞技场挑战任务
	TaskType_TotalActivateHero       TaskType = 36 // 累计激活英雄
	TaskType_TotalQualityHero        TaskType = 37 // 拥有指定品质英雄
	TaskType_TotalMainStar           TaskType = 38 // 主线总星
	TaskType_DailyScore              TaskType = 39 // 领取每日宝箱
	TaskType_WeeklyScore             TaskType = 40 // 领取每周宝箱
	TaskType_DungeonLevel            TaskType = 41 // 资源本难度
	TaskType_GemQuality              TaskType = 42 // 突变基因品质
	TaskType_LordEquipGrade          TaskType = 43 // 培养仓升品次数
	TaskType_LordEquipGradeTo        TaskType = 44 // 培养仓升品到
	TaskType_EnergyFactory           TaskType = 45 // 光伏领体力
	TaskType_TotalHeroLevelUp        TaskType = 46 // 累计英雄升级
	TaskType_TotalHeroStarUp         TaskType = 47 // 累计英雄升星
	TaskType_TotalHeroSkillUp        TaskType = 48 // 累计技能升级
	TaskType_TotalHeroGeneUp         TaskType = 49 // 累计天赋升级
	TaskType_TotalHeroSummon         TaskType = 50 // 累计英雄招募
	TaskType_TotalGemSummon          TaskType = 51 // 累计提炼基因
	TaskType_TotalGemCraft           TaskType = 52 // 累计突变基因合成
	TaskType_TotalLordEquipLvlUp     TaskType = 53 // 累计培养仓强化
	TaskType_TotalLordEquipGrade     TaskType = 54 // 累计培养仓升品
	TaskType_CompleteTask            TaskType = 55 // 完成子任务
	TaskType_TotalGemSummon_1        TaskType = 56 // 累计提炼普通基因
	TaskType_TotalGemSummon_2        TaskType = 57 // 累计提炼高级基因
	TaskType_ChapterTaskComplete     TaskType = 58 // 完成指定章节任务
)

const TaskTypeEnumCount = 58

var TaskTypeEnums = [...]TaskType{TaskType_Login, TaskType_TotalLogin, TaskType_LevelBegin, TaskType_LevelPass, TaskType_LevelPassTo, TaskType_ItemBurn, TaskType_TotalItemBurn, TaskType_HeroLevelUp, TaskType_HeroLevelUpTo, TaskType_HeroStarUp, TaskType_HeroStarUpTo, TaskType_HeroSkillUp, TaskType_HeroSkillUpTo, TaskType_HeroGeneUp, TaskType_HeroGeneUpTo, TaskType_KillMonster, TaskType_TotalKillMonster, TaskType_claim_idle_reward, TaskType_claim_pass_level_reward, TaskType_Chat, TaskType_Nigger, TaskType_Rename, TaskType_Avatar, TaskType_JoinGuild, TaskType_Sweep, TaskType_HeroSummon, TaskType_HeroConfig, TaskType_LordEquipLvlUp, TaskType_LordEquipLvlUpTo, TaskType_GemCraft, TaskType_GemSummon, TaskType_Shopping, TaskType_DungeonChallenge, TaskType_DungeonSweep, TaskType_ArenaChallenge, TaskType_TotalActivateHero, TaskType_TotalQualityHero, TaskType_TotalMainStar, TaskType_DailyScore, TaskType_WeeklyScore, TaskType_DungeonLevel, TaskType_GemQuality, TaskType_LordEquipGrade, TaskType_LordEquipGradeTo, TaskType_EnergyFactory, TaskType_TotalHeroLevelUp, TaskType_TotalHeroStarUp, TaskType_TotalHeroSkillUp, TaskType_TotalHeroGeneUp, TaskType_TotalHeroSummon, TaskType_TotalGemSummon, TaskType_TotalGemCraft, TaskType_TotalLordEquipLvlUp, TaskType_TotalLordEquipGrade, TaskType_CompleteTask, TaskType_TotalGemSummon_1, TaskType_TotalGemSummon_2, TaskType_ChapterTaskComplete}

func (t TaskType) String() string {
	switch t {
	case TaskType_Login:
		return "Login"
	case TaskType_TotalLogin:
		return "TotalLogin"
	case TaskType_LevelBegin:
		return "LevelBegin"
	case TaskType_LevelPass:
		return "LevelPass"
	case TaskType_LevelPassTo:
		return "LevelPassTo"
	case TaskType_ItemBurn:
		return "ItemBurn"
	case TaskType_TotalItemBurn:
		return "TotalItemBurn"
	case TaskType_HeroLevelUp:
		return "HeroLevelUp"
	case TaskType_HeroLevelUpTo:
		return "HeroLevelUpTo"
	case TaskType_HeroStarUp:
		return "HeroStarUp"
	case TaskType_HeroStarUpTo:
		return "HeroStarUpTo"
	case TaskType_HeroSkillUp:
		return "HeroSkillUp"
	case TaskType_HeroSkillUpTo:
		return "HeroSkillUpTo"
	case TaskType_HeroGeneUp:
		return "HeroGeneUp"
	case TaskType_HeroGeneUpTo:
		return "HeroGeneUpTo"
	case TaskType_KillMonster:
		return "KillMonster"
	case TaskType_TotalKillMonster:
		return "TotalKillMonster"
	case TaskType_claim_idle_reward:
		return "claim_idle_reward"
	case TaskType_claim_pass_level_reward:
		return "claim_pass_level_reward"
	case TaskType_Chat:
		return "Chat"
	case TaskType_Nigger:
		return "Nigger"
	case TaskType_Rename:
		return "Rename"
	case TaskType_Avatar:
		return "Avatar"
	case TaskType_JoinGuild:
		return "JoinGuild"
	case TaskType_Sweep:
		return "Sweep"
	case TaskType_HeroSummon:
		return "HeroSummon"
	case TaskType_HeroConfig:
		return "HeroConfig"
	case TaskType_LordEquipLvlUp:
		return "LordEquipLvlUp"
	case TaskType_LordEquipLvlUpTo:
		return "LordEquipLvlUpTo"
	case TaskType_GemCraft:
		return "GemCraft"
	case TaskType_GemSummon:
		return "GemSummon"
	case TaskType_Shopping:
		return "Shopping"
	case TaskType_DungeonChallenge:
		return "DungeonChallenge"
	case TaskType_DungeonSweep:
		return "DungeonSweep"
	case TaskType_ArenaChallenge:
		return "ArenaChallenge"
	case TaskType_TotalActivateHero:
		return "TotalActivateHero"
	case TaskType_TotalQualityHero:
		return "TotalQualityHero"
	case TaskType_TotalMainStar:
		return "TotalMainStar"
	case TaskType_DailyScore:
		return "DailyScore"
	case TaskType_WeeklyScore:
		return "WeeklyScore"
	case TaskType_DungeonLevel:
		return "DungeonLevel"
	case TaskType_GemQuality:
		return "GemQuality"
	case TaskType_LordEquipGrade:
		return "LordEquipGrade"
	case TaskType_LordEquipGradeTo:
		return "LordEquipGradeTo"
	case TaskType_EnergyFactory:
		return "EnergyFactory"
	case TaskType_TotalHeroLevelUp:
		return "TotalHeroLevelUp"
	case TaskType_TotalHeroStarUp:
		return "TotalHeroStarUp"
	case TaskType_TotalHeroSkillUp:
		return "TotalHeroSkillUp"
	case TaskType_TotalHeroGeneUp:
		return "TotalHeroGeneUp"
	case TaskType_TotalHeroSummon:
		return "TotalHeroSummon"
	case TaskType_TotalGemSummon:
		return "TotalGemSummon"
	case TaskType_TotalGemCraft:
		return "TotalGemCraft"
	case TaskType_TotalLordEquipLvlUp:
		return "TotalLordEquipLvlUp"
	case TaskType_TotalLordEquipGrade:
		return "TotalLordEquipGrade"
	case TaskType_CompleteTask:
		return "CompleteTask"
	case TaskType_TotalGemSummon_1:
		return "TotalGemSummon_1"
	case TaskType_TotalGemSummon_2:
		return "TotalGemSummon_2"
	case TaskType_ChapterTaskComplete:
		return "ChapterTaskComplete"
	default:
		return ""
	}
}

type TriggerPackType int32 // 触发礼包类型

const (
	TriggerPackType_LevelPass  TriggerPackType = 1 // 主线通关纪念
	TriggerPackType_GemDraw    TriggerPackType = 2 // 宝石抽取
	TriggerPackType_HeroSummon TriggerPackType = 3 // 英雄招募
)

const TriggerPackTypeEnumCount = 3

var TriggerPackTypeEnums = [...]TriggerPackType{TriggerPackType_LevelPass, TriggerPackType_GemDraw, TriggerPackType_HeroSummon}

func (t TriggerPackType) String() string {
	switch t {
	case TriggerPackType_LevelPass:
		return "LevelPass"
	case TriggerPackType_GemDraw:
		return "GemDraw"
	case TriggerPackType_HeroSummon:
		return "HeroSummon"
	default:
		return ""
	}
}
