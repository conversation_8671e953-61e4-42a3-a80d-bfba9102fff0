// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type ChapterLevelTableCfg struct {
	Id int32 `json:"Id"` // Id
}

func NewChapterLevelTableCfg() *ChapterLevelTableCfg {
	return &ChapterLevelTableCfg{
		Id: 0,
	}
}

type ChapterLevelTable struct {
	records  map[int32]*ChapterLevelTableCfg
	localIds map[int32]struct{}
}

func NewChapterLevelTable() *ChapterLevelTable {
	return &ChapterLevelTable{
		records:  map[int32]*ChapterLevelTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *ChapterLevelTable) Get(key int32) *ChapterLevelTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *ChapterLevelTable) GetAll() map[int32]*ChapterLevelTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *ChapterLevelTable) put(key int32, value *ChapterLevelTableCfg, local bool) *ChapterLevelTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *ChapterLevelTable) Range(f func(v *ChapterLevelTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *ChapterLevelTable) Filter(filterFuncs ...func(v *ChapterLevelTableCfg) bool) map[int32]*ChapterLevelTableCfg {
	filtered := map[int32]*ChapterLevelTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *ChapterLevelTable) FilterSlice(filterFuncs ...func(v *ChapterLevelTableCfg) bool) []*ChapterLevelTableCfg {
	filtered := []*ChapterLevelTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *ChapterLevelTable) FilterKeys(filterFuncs ...func(v *ChapterLevelTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *ChapterLevelTable) satisfied(v *ChapterLevelTableCfg, filterFuncs ...func(v *ChapterLevelTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *ChapterLevelTable) setupIndexes() error {
	return nil
}

func (t *ChapterLevelTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *ChapterLevelTableCfg) bindRefs(c *Configs) {
}
