{"2001": {"Id": 2001, "Dungeon": 2, "Level": 1, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 80}, {"RewardType": 87, "RewardValue": 500}]}, "2002": {"Id": 2002, "Dungeon": 2, "Level": 2, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 80}, {"RewardType": 87, "RewardValue": 650}]}, "2003": {"Id": 2003, "Dungeon": 2, "Level": 3, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 80}, {"RewardType": 87, "RewardValue": 800}]}, "2004": {"Id": 2004, "Dungeon": 2, "Level": 4, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 80}, {"RewardType": 87, "RewardValue": 900}]}, "2005": {"Id": 2005, "Dungeon": 2, "Level": 5, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 80}, {"RewardType": 87, "RewardValue": 1000}]}, "2006": {"Id": 2006, "Dungeon": 2, "Level": 6, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 80}, {"RewardType": 87, "RewardValue": 1100}]}, "2007": {"Id": 2007, "Dungeon": 2, "Level": 7, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 80}, {"RewardType": 87, "RewardValue": 1200}]}, "2008": {"Id": 2008, "Dungeon": 2, "Level": 8, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 80}, {"RewardType": 87, "RewardValue": 1300}]}, "2009": {"Id": 2009, "Dungeon": 2, "Level": 9, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 80}, {"RewardType": 87, "RewardValue": 1400}]}, "2010": {"Id": 2010, "Dungeon": 2, "Level": 10, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 80}, {"RewardType": 87, "RewardValue": 1500}]}, "2011": {"Id": 2011, "Dungeon": 2, "Level": 11, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 95}, {"RewardType": 87, "RewardValue": 1600}]}, "2012": {"Id": 2012, "Dungeon": 2, "Level": 12, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 95}, {"RewardType": 87, "RewardValue": 1700}]}, "2013": {"Id": 2013, "Dungeon": 2, "Level": 13, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 95}, {"RewardType": 87, "RewardValue": 1800}]}, "2014": {"Id": 2014, "Dungeon": 2, "Level": 14, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 95}, {"RewardType": 87, "RewardValue": 1900}]}, "2015": {"Id": 2015, "Dungeon": 2, "Level": 15, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 95}, {"RewardType": 87, "RewardValue": 2000}]}, "2016": {"Id": 2016, "Dungeon": 2, "Level": 16, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 95}, {"RewardType": 87, "RewardValue": 2100}]}, "2017": {"Id": 2017, "Dungeon": 2, "Level": 17, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 95}, {"RewardType": 87, "RewardValue": 2200}]}, "2018": {"Id": 2018, "Dungeon": 2, "Level": 18, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 95}, {"RewardType": 87, "RewardValue": 2300}]}, "2019": {"Id": 2019, "Dungeon": 2, "Level": 19, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 95}, {"RewardType": 87, "RewardValue": 2400}]}, "2020": {"Id": 2020, "Dungeon": 2, "Level": 20, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 95}, {"RewardType": 87, "RewardValue": 2500}]}, "2021": {"Id": 2021, "Dungeon": 2, "Level": 21, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 105}, {"RewardType": 87, "RewardValue": 2600}]}, "2022": {"Id": 2022, "Dungeon": 2, "Level": 22, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 105}, {"RewardType": 87, "RewardValue": 2700}]}, "2023": {"Id": 2023, "Dungeon": 2, "Level": 23, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 105}, {"RewardType": 87, "RewardValue": 2800}]}, "2024": {"Id": 2024, "Dungeon": 2, "Level": 24, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 105}, {"RewardType": 87, "RewardValue": 2900}]}, "2025": {"Id": 2025, "Dungeon": 2, "Level": 25, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 105}, {"RewardType": 87, "RewardValue": 3000}]}, "2026": {"Id": 2026, "Dungeon": 2, "Level": 26, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 105}, {"RewardType": 87, "RewardValue": 3100}]}, "2027": {"Id": 2027, "Dungeon": 2, "Level": 27, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 105}, {"RewardType": 87, "RewardValue": 3200}]}, "2028": {"Id": 2028, "Dungeon": 2, "Level": 28, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 105}, {"RewardType": 87, "RewardValue": 3300}]}, "2029": {"Id": 2029, "Dungeon": 2, "Level": 29, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 105}, {"RewardType": 87, "RewardValue": 3400}]}, "2030": {"Id": 2030, "Dungeon": 2, "Level": 30, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 105}, {"RewardType": 87, "RewardValue": 3500}]}, "2031": {"Id": 2031, "Dungeon": 2, "Level": 31, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 110}, {"RewardType": 87, "RewardValue": 3600}]}, "2032": {"Id": 2032, "Dungeon": 2, "Level": 32, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 110}, {"RewardType": 87, "RewardValue": 3700}]}, "2033": {"Id": 2033, "Dungeon": 2, "Level": 33, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 110}, {"RewardType": 87, "RewardValue": 3800}]}, "2034": {"Id": 2034, "Dungeon": 2, "Level": 34, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 110}, {"RewardType": 87, "RewardValue": 3900}]}, "2035": {"Id": 2035, "Dungeon": 2, "Level": 35, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 110}, {"RewardType": 87, "RewardValue": 4000}]}, "2036": {"Id": 2036, "Dungeon": 2, "Level": 36, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 110}, {"RewardType": 87, "RewardValue": 4100}]}, "2037": {"Id": 2037, "Dungeon": 2, "Level": 37, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 110}, {"RewardType": 87, "RewardValue": 4200}]}, "2038": {"Id": 2038, "Dungeon": 2, "Level": 38, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 110}, {"RewardType": 87, "RewardValue": 4300}]}, "2039": {"Id": 2039, "Dungeon": 2, "Level": 39, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 110}, {"RewardType": 87, "RewardValue": 4400}]}, "2040": {"Id": 2040, "Dungeon": 2, "Level": 40, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 110}, {"RewardType": 87, "RewardValue": 4500}]}, "2041": {"Id": 2041, "Dungeon": 2, "Level": 41, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 115}, {"RewardType": 87, "RewardValue": 4550}]}, "2042": {"Id": 2042, "Dungeon": 2, "Level": 42, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 115}, {"RewardType": 87, "RewardValue": 4600}]}, "2043": {"Id": 2043, "Dungeon": 2, "Level": 43, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 115}, {"RewardType": 87, "RewardValue": 4650}]}, "2044": {"Id": 2044, "Dungeon": 2, "Level": 44, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 115}, {"RewardType": 87, "RewardValue": 4700}]}, "2045": {"Id": 2045, "Dungeon": 2, "Level": 45, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 115}, {"RewardType": 87, "RewardValue": 4750}]}, "2046": {"Id": 2046, "Dungeon": 2, "Level": 46, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 115}, {"RewardType": 87, "RewardValue": 4800}]}, "2047": {"Id": 2047, "Dungeon": 2, "Level": 47, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 115}, {"RewardType": 87, "RewardValue": 4850}]}, "2048": {"Id": 2048, "Dungeon": 2, "Level": 48, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 115}, {"RewardType": 87, "RewardValue": 4900}]}, "2049": {"Id": 2049, "Dungeon": 2, "Level": 49, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 115}, {"RewardType": 87, "RewardValue": 4950}]}, "2050": {"Id": 2050, "Dungeon": 2, "Level": 50, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 115}, {"RewardType": 87, "RewardValue": 5000}]}, "2051": {"Id": 2051, "Dungeon": 2, "Level": 51, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 120}, {"RewardType": 87, "RewardValue": 5050}]}, "2052": {"Id": 2052, "Dungeon": 2, "Level": 52, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 120}, {"RewardType": 87, "RewardValue": 5100}]}, "2053": {"Id": 2053, "Dungeon": 2, "Level": 53, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 120}, {"RewardType": 87, "RewardValue": 5150}]}, "2054": {"Id": 2054, "Dungeon": 2, "Level": 54, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 120}, {"RewardType": 87, "RewardValue": 5200}]}, "2055": {"Id": 2055, "Dungeon": 2, "Level": 55, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 120}, {"RewardType": 87, "RewardValue": 5250}]}, "2056": {"Id": 2056, "Dungeon": 2, "Level": 56, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 120}, {"RewardType": 87, "RewardValue": 5300}]}, "2057": {"Id": 2057, "Dungeon": 2, "Level": 57, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 120}, {"RewardType": 87, "RewardValue": 5350}]}, "2058": {"Id": 2058, "Dungeon": 2, "Level": 58, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 120}, {"RewardType": 87, "RewardValue": 5400}]}, "2059": {"Id": 2059, "Dungeon": 2, "Level": 59, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 120}, {"RewardType": 87, "RewardValue": 5450}]}, "2060": {"Id": 2060, "Dungeon": 2, "Level": 60, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 120}, {"RewardType": 87, "RewardValue": 5500}]}, "2061": {"Id": 2061, "Dungeon": 2, "Level": 61, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 125}, {"RewardType": 87, "RewardValue": 5550}]}, "2062": {"Id": 2062, "Dungeon": 2, "Level": 62, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 125}, {"RewardType": 87, "RewardValue": 5600}]}, "2063": {"Id": 2063, "Dungeon": 2, "Level": 63, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 125}, {"RewardType": 87, "RewardValue": 5650}]}, "2064": {"Id": 2064, "Dungeon": 2, "Level": 64, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 125}, {"RewardType": 87, "RewardValue": 5700}]}, "2065": {"Id": 2065, "Dungeon": 2, "Level": 65, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 125}, {"RewardType": 87, "RewardValue": 5750}]}, "2066": {"Id": 2066, "Dungeon": 2, "Level": 66, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 125}, {"RewardType": 87, "RewardValue": 5800}]}, "2067": {"Id": 2067, "Dungeon": 2, "Level": 67, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 125}, {"RewardType": 87, "RewardValue": 5850}]}, "2068": {"Id": 2068, "Dungeon": 2, "Level": 68, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 125}, {"RewardType": 87, "RewardValue": 5900}]}, "2069": {"Id": 2069, "Dungeon": 2, "Level": 69, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 125}, {"RewardType": 87, "RewardValue": 5950}]}, "2070": {"Id": 2070, "Dungeon": 2, "Level": 70, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 125}, {"RewardType": 87, "RewardValue": 6000}]}, "2071": {"Id": 2071, "Dungeon": 2, "Level": 71, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 130}, {"RewardType": 87, "RewardValue": 6050}]}, "2072": {"Id": 2072, "Dungeon": 2, "Level": 72, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 130}, {"RewardType": 87, "RewardValue": 6100}]}, "2073": {"Id": 2073, "Dungeon": 2, "Level": 73, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 130}, {"RewardType": 87, "RewardValue": 6150}]}, "2074": {"Id": 2074, "Dungeon": 2, "Level": 74, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 130}, {"RewardType": 87, "RewardValue": 6200}]}, "2075": {"Id": 2075, "Dungeon": 2, "Level": 75, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 130}, {"RewardType": 87, "RewardValue": 6250}]}, "2076": {"Id": 2076, "Dungeon": 2, "Level": 76, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 130}, {"RewardType": 87, "RewardValue": 6300}]}, "2077": {"Id": 2077, "Dungeon": 2, "Level": 77, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 130}, {"RewardType": 87, "RewardValue": 6350}]}, "2078": {"Id": 2078, "Dungeon": 2, "Level": 78, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 130}, {"RewardType": 87, "RewardValue": 6400}]}, "2079": {"Id": 2079, "Dungeon": 2, "Level": 79, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 130}, {"RewardType": 87, "RewardValue": 6450}]}, "2080": {"Id": 2080, "Dungeon": 2, "Level": 80, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 130}, {"RewardType": 87, "RewardValue": 6500}]}, "2081": {"Id": 2081, "Dungeon": 2, "Level": 81, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 135}, {"RewardType": 87, "RewardValue": 6550}]}, "2082": {"Id": 2082, "Dungeon": 2, "Level": 82, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 135}, {"RewardType": 87, "RewardValue": 6600}]}, "2083": {"Id": 2083, "Dungeon": 2, "Level": 83, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 135}, {"RewardType": 87, "RewardValue": 6650}]}, "2084": {"Id": 2084, "Dungeon": 2, "Level": 84, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 135}, {"RewardType": 87, "RewardValue": 6700}]}, "2085": {"Id": 2085, "Dungeon": 2, "Level": 85, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 135}, {"RewardType": 87, "RewardValue": 6750}]}, "2086": {"Id": 2086, "Dungeon": 2, "Level": 86, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 135}, {"RewardType": 87, "RewardValue": 6800}]}, "2087": {"Id": 2087, "Dungeon": 2, "Level": 87, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 135}, {"RewardType": 87, "RewardValue": 6850}]}, "2088": {"Id": 2088, "Dungeon": 2, "Level": 88, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 135}, {"RewardType": 87, "RewardValue": 6900}]}, "2089": {"Id": 2089, "Dungeon": 2, "Level": 89, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 135}, {"RewardType": 87, "RewardValue": 6950}]}, "2090": {"Id": 2090, "Dungeon": 2, "Level": 90, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 135}, {"RewardType": 87, "RewardValue": 7000}]}, "2091": {"Id": 2091, "Dungeon": 2, "Level": 91, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 140}, {"RewardType": 87, "RewardValue": 7050}]}, "2092": {"Id": 2092, "Dungeon": 2, "Level": 92, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 140}, {"RewardType": 87, "RewardValue": 7100}]}, "2093": {"Id": 2093, "Dungeon": 2, "Level": 93, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 140}, {"RewardType": 87, "RewardValue": 7150}]}, "2094": {"Id": 2094, "Dungeon": 2, "Level": 94, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 140}, {"RewardType": 87, "RewardValue": 7200}]}, "2095": {"Id": 2095, "Dungeon": 2, "Level": 95, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 140}, {"RewardType": 87, "RewardValue": 7250}]}, "2096": {"Id": 2096, "Dungeon": 2, "Level": 96, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 140}, {"RewardType": 87, "RewardValue": 7300}]}, "2097": {"Id": 2097, "Dungeon": 2, "Level": 97, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 140}, {"RewardType": 87, "RewardValue": 7350}]}, "2098": {"Id": 2098, "Dungeon": 2, "Level": 98, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 140}, {"RewardType": 87, "RewardValue": 7400}]}, "2099": {"Id": 2099, "Dungeon": 2, "Level": 99, "ChooseNum": 5, "IsMax": false, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 140}, {"RewardType": 87, "RewardValue": 7450}]}, "2100": {"Id": 2100, "Dungeon": 2, "Level": 100, "ChooseNum": 5, "IsMax": true, "FirstReward": {"RewardType": 0, "RewardValue": 0}, "Reward": [{"RewardType": 97, "RewardValue": 140}, {"RewardType": 87, "RewardValue": 7500}]}}