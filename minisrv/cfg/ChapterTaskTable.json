{"101": {"Id": 101, "StringId": "chapter_1_task_1", "Chapter": 1, "TaskType": 16, "TaskCounterType": 1, "Formula": "", "Value": 50, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "102": {"Id": 102, "StringId": "chapter_1_task_2", "Chapter": 1, "TaskType": 36, "TaskCounterType": 1, "Formula": "", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "103": {"Id": 103, "StringId": "chapter_1_task_3", "Chapter": 1, "TaskType": 5, "TaskCounterType": 1, "Formula": "level>=3", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "201": {"Id": 201, "StringId": "chapter_2_task_1", "Chapter": 2, "TaskType": 50, "TaskCounterType": 1, "Formula": "", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "202": {"Id": 202, "StringId": "chapter_2_task_2", "Chapter": 2, "TaskType": 39, "TaskCounterType": 1, "Formula": "", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "203": {"Id": 203, "StringId": "chapter_2_task_3", "Chapter": 2, "TaskType": 9, "TaskCounterType": 1, "Formula": "level=10", "Value": 2, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "204": {"Id": 204, "StringId": "chapter_2_task_4", "Chapter": 2, "TaskType": 5, "TaskCounterType": 1, "Formula": "level>=13", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "301": {"Id": 301, "StringId": "chapter_3_task_1", "Chapter": 3, "TaskType": 53, "TaskCounterType": 1, "Formula": "level>=10", "Value": 3, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "302": {"Id": 302, "StringId": "chapter_3_task_2", "Chapter": 3, "TaskType": 45, "TaskCounterType": 1, "Formula": "", "Value": 50, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "303": {"Id": 303, "StringId": "chapter_3_task_3", "Chapter": 3, "TaskType": 36, "TaskCounterType": 1, "Formula": "", "Value": 8, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "304": {"Id": 304, "StringId": "chapter_3_task_4", "Chapter": 3, "TaskType": 41, "TaskCounterType": 1, "Formula": "type=4", "Value": 3, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "305": {"Id": 305, "StringId": "chapter_3_task_5", "Chapter": 3, "TaskType": 5, "TaskCounterType": 1, "Formula": "level>=23", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "401": {"Id": 401, "StringId": "chapter_4_task_1", "Chapter": 4, "TaskType": 44, "TaskCounterType": 1, "Formula": "quality>=2", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "402": {"Id": 402, "StringId": "chapter_4_task_2", "Chapter": 4, "TaskType": 9, "TaskCounterType": 1, "Formula": "level>=40", "Value": 3, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "403": {"Id": 403, "StringId": "chapter_4_task_3", "Chapter": 4, "TaskType": 37, "TaskCounterType": 1, "Formula": "quality=1", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "404": {"Id": 404, "StringId": "chapter_4_task_4", "Chapter": 4, "TaskType": 41, "TaskCounterType": 1, "Formula": "type=2", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "405": {"Id": 405, "StringId": "chapter_4_task_5", "Chapter": 4, "TaskType": 5, "TaskCounterType": 1, "Formula": "level>=33", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "501": {"Id": 501, "StringId": "chapter_5_task_1", "Chapter": 5, "TaskType": 38, "TaskCounterType": 1, "Formula": "", "Value": 100, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "502": {"Id": 502, "StringId": "chapter_5_task_2", "Chapter": 5, "TaskType": 21, "TaskCounterType": 1, "Formula": "", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "503": {"Id": 503, "StringId": "chapter_5_task_3", "Chapter": 5, "TaskType": 42, "TaskCounterType": 1, "Formula": "quality>=4", "Value": 3, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "504": {"Id": 504, "StringId": "chapter_5_task_4", "Chapter": 5, "TaskType": 41, "TaskCounterType": 1, "Formula": "type=3", "Value": 8, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "505": {"Id": 505, "StringId": "chapter_5_task_5", "Chapter": 5, "TaskType": 5, "TaskCounterType": 1, "Formula": "level>=43", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "601": {"Id": 601, "StringId": "chapter_6_task_1", "Chapter": 6, "TaskType": 47, "TaskCounterType": 1, "Formula": "", "Value": 50, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "602": {"Id": 602, "StringId": "chapter_6_task_2", "Chapter": 6, "TaskType": 51, "TaskCounterType": 1, "Formula": "", "Value": 50, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "603": {"Id": 603, "StringId": "chapter_6_task_3", "Chapter": 6, "TaskType": 40, "TaskCounterType": 1, "Formula": "", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "604": {"Id": 604, "StringId": "chapter_6_task_4", "Chapter": 6, "TaskType": 41, "TaskCounterType": 1, "Formula": "type=1", "Value": 15, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "605": {"Id": 605, "StringId": "chapter_6_task_5", "Chapter": 6, "TaskType": 5, "TaskCounterType": 1, "Formula": "level>=53", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "701": {"Id": 701, "StringId": "chapter_7_task_1", "Chapter": 7, "TaskType": 53, "TaskCounterType": 1, "Formula": "level>=30", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "702": {"Id": 702, "StringId": "chapter_7_task_2", "Chapter": 7, "TaskType": 45, "TaskCounterType": 1, "Formula": "", "Value": 100, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "703": {"Id": 703, "StringId": "chapter_7_task_3", "Chapter": 7, "TaskType": 36, "TaskCounterType": 1, "Formula": "", "Value": 12, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "704": {"Id": 704, "StringId": "chapter_7_task_4", "Chapter": 7, "TaskType": 41, "TaskCounterType": 1, "Formula": "type=4", "Value": 20, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "705": {"Id": 705, "StringId": "chapter_7_task_5", "Chapter": 7, "TaskType": 5, "TaskCounterType": 1, "Formula": "level>=63", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "801": {"Id": 801, "StringId": "chapter_8_task_1", "Chapter": 8, "TaskType": 44, "TaskCounterType": 1, "Formula": "quality>=3", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "802": {"Id": 802, "StringId": "chapter_8_task_2", "Chapter": 8, "TaskType": 9, "TaskCounterType": 1, "Formula": "level>=50", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "803": {"Id": 803, "StringId": "chapter_8_task_3", "Chapter": 8, "TaskType": 37, "TaskCounterType": 1, "Formula": "quality=1", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "804": {"Id": 804, "StringId": "chapter_8_task_4", "Chapter": 8, "TaskType": 41, "TaskCounterType": 1, "Formula": "type=2", "Value": 25, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "805": {"Id": 805, "StringId": "chapter_8_task_5", "Chapter": 8, "TaskType": 5, "TaskCounterType": 1, "Formula": "level>=73", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "901": {"Id": 901, "StringId": "chapter_9_task_1", "Chapter": 9, "TaskType": 38, "TaskCounterType": 1, "Formula": "", "Value": 150, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "902": {"Id": 902, "StringId": "chapter_9_task_2", "Chapter": 9, "TaskType": 15, "TaskCounterType": 1, "Formula": "level>=10", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "903": {"Id": 903, "StringId": "chapter_9_task_3", "Chapter": 9, "TaskType": 42, "TaskCounterType": 1, "Formula": "quality>=5", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "904": {"Id": 904, "StringId": "chapter_9_task_4", "Chapter": 9, "TaskType": 41, "TaskCounterType": 1, "Formula": "type=3", "Value": 30, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "905": {"Id": 905, "StringId": "chapter_9_task_5", "Chapter": 9, "TaskType": 5, "TaskCounterType": 1, "Formula": "level>=83", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "1001": {"Id": 1001, "StringId": "chapter_10_task_1", "Chapter": 10, "TaskType": 47, "TaskCounterType": 1, "Formula": "", "Value": 150, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "1002": {"Id": 1002, "StringId": "chapter_10_task_2", "Chapter": 10, "TaskType": 51, "TaskCounterType": 1, "Formula": "", "Value": 100, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "1003": {"Id": 1003, "StringId": "chapter_10_task_3", "Chapter": 10, "TaskType": 40, "TaskCounterType": 1, "Formula": "", "Value": 15, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "1004": {"Id": 1004, "StringId": "chapter_10_task_4", "Chapter": 10, "TaskType": 41, "TaskCounterType": 1, "Formula": "type=1", "Value": 40, "Reward": [{"RewardType": 1, "RewardValue": 10}]}, "1005": {"Id": 1005, "StringId": "chapter_10_task_5", "Chapter": 10, "TaskType": 5, "TaskCounterType": 1, "Formula": "level>=93", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 10}]}}