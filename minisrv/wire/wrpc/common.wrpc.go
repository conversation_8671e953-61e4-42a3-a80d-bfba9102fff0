// Code generated by wrpc. DO NOT EDIT.

package wrpc

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"runtime/debug"
	"strconv"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"gitlab-ee.funplus.io/mg-server/mgsl/transport/bus"
	"gitlab-ee.funplus.io/mg-server/mgsl/uuid"
	"gitlab-ee.funplus.io/watcher/watcher/misc"
	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
	"gitlab-ee.funplus.io/watcher/watcher/misc/live"
	"gitlab-ee.funplus.io/watcher/watcher/misc/wlog"
	"gitlab-ee.funplus.io/watcher/watcher/misc/xctx"
	"gitlab-ee.funplus.io/watcher/watcher/misc/zap"
	"gitlab-ee.funplus.io/watcher/watcher/stargate/gkit"
	"gitlab-ee.funplus.io/watcher/watcher/stargate/gkit/olcache"
	"gitlab-ee.funplus.io/watcher/watcher/stargate/proto"
	"gitlab-ee.funplus.io/watcher/watcher/wrpc/client"
	"gitlab-ee.funplus.io/watcher/watcher/wrpc/codec"
	"gitlab-ee.funplus.io/watcher/watcher/wrpc/server"
	"gitlab-ee.funplus.io/watcher/watcher/wrpc/wire/enum"
	"gitlab-ee.funplus.io/watcher/watcher/wrpc/wire/pb"
	"gitlab-ee.funplus.io/watcher/watcher/wrpc/wnet"

	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
)

var (
	_ = context.WithTimeout
	_ = strconv.Itoa
	_ = errors.New
	_ = fmt.Sprintf
	_ = client.NewConn
	_ = codec.BasicCodecNames
	_ = server.NewConn
	_ = enum.Msg_Invalid
	_ = xctx.WithTimeout
	_ = pb.ErrIntOverflowPb
	_ = gkit.ServerEtcdTTL
	_ = olcache.NewOLCache
	_ = wlog.Debug
	_ = zap.New
	_ = time.Now
	_ = debug.Stack
	_ = bus.DefaultQueue
	_ = uuid.Split
	_ = proto.Label
)

type RPC int32

const (
	RPC_SimpleError                   RPC = 4
	RPC_MulticastError                RPC = 9
	RPC_UserOnline                    RPC = 100
	RPC_UpdateUserTopics              RPC = 101
	RPC_KickOut                       RPC = 103
	RPC_UserOffline                   RPC = 104
	RPC_WantUserOfflineNtf            RPC = 105
	RPC_UpdateUserConnWarehouse       RPC = 106
	RPC_Echo                          RPC = 107
	RPC_PerformUserConnHealthcheck    RPC = 108
	RPC_SyncServerLoad                RPC = 109
	RPC_RequestDispatcher             RPC = 501
	RPC_RegisterUser                  RPC = 1001
	RPC_Login                         RPC = 1002
	RPC_HelloWorld                    RPC = 1003
	RPC_VerifyUser                    RPC = 1004
	RPC_AddBuilding                   RPC = 1005
	RPC_UpgradeBuilding               RPC = 1006
	RPC_HeroLottery                   RPC = 1007
	RPC_HeroUpgradeLevel              RPC = 1008
	RPC_HeroUpgradeStar               RPC = 1009
	RPC_DebugAddItem                  RPC = 1010
	RPC_BuildWorkHero                 RPC = 1011
	RPC_CollectResource               RPC = 1012
	RPC_SetHeroBattlePos              RPC = 1013
	RPC_SetHeroTroop                  RPC = 1014
	RPC_SetDefaultBattlePos           RPC = 1015
	RPC_CollectHeroLotteryAccReward   RPC = 1016
	RPC_CollectHeroLotteryLevelReward RPC = 1017
	RPC_RandomHeroDice                RPC = 1018
	RPC_LockHeroDice                  RPC = 1019
	RPC_TrainTroops                   RPC = 1020
	RPC_UserItem                      RPC = 1021
	RPC_BuildingWorkVillage           RPC = 1022
	RPC_FinishDungeonStage            RPC = 1023
	RPC_CollectFirstPassReward        RPC = 1024
	RPC_StartResearch                 RPC = 1025
	RPC_CancerResearch                RPC = 1026
	RPC_CollectMainReward             RPC = 1028
	RPC_CollectMapChapterReward       RPC = 1029
	RPC_FinishBuilding                RPC = 1030
	RPC_FinishResearch                RPC = 1031
	RPC_FinishTrain                   RPC = 1032
	RPC_FinishMapEvent                RPC = 1033
	RPC_Debug                         RPC = 1034
	RPC_OnBackCity                    RPC = 1035
	RPC_UpgradeSkillLevel             RPC = 1036
	RPC_UpgradeDaveLevel              RPC = 1037
	RPC_FinishMainStage               RPC = 1038
	RPC_CollectIdleReward             RPC = 1039
	RPC_ResetMainStage                RPC = 1040
	RPC_KillMonster                   RPC = 1041
	RPC_StartMainStage                RPC = 1042
	RPC_GetIdleReward                 RPC = 1043
	RPC_GetManifest                   RPC = 1044
	RPC_UploadDeviceInfo              RPC = 1045
	RPC_RefreshRougeSkill             RPC = 1046
	RPC_HeartBeat                     RPC = 1047
	RPC_SelectRougeSkill              RPC = 1048
	RPC_CollectStageReward            RPC = 1049
	RPC_GetGiftList                   RPC = 1050
	RPC_PrepareOrder                  RPC = 1051
	RPC_HeroUpgradeGene               RPC = 1052
	RPC_SelectEliteRougeSkill         RPC = 1053
	RPC_GetStageRankInfo              RPC = 1054
	RPC_GetStageRankInfoByStageId     RPC = 1055
	RPC_CollectStageLevelRewards      RPC = 1056
	RPC_GetPhotovoltaicReward         RPC = 1057
	RPC_CollectPhotovoltaicReward     RPC = 1058
	RPC_SweepMainStage                RPC = 1059
	RPC_GetAllStageRankInfo           RPC = 1060
	RPC_ChangeName                    RPC = 1061
	RPC_ChangeAvatar                  RPC = 1062
	RPC_CollectMonsterBookReward      RPC = 1063
	RPC_HeroBeKilled                  RPC = 1064
	RPC_GetDailyTask                  RPC = 1065
	RPC_CollectDailyTaskReward        RPC = 1066
	RPC_CollectDailyChestReward       RPC = 1067
	RPC_GetMailList                   RPC = 1068
	RPC_GetAllianceInfo               RPC = 1069
	RPC_CreateAlliance                RPC = 1070
	RPC_ApplyJoinAlliance             RPC = 1071
	RPC_GetAllianceMembersInfos       RPC = 1072
	RPC_LeaveAlliance                 RPC = 1073
	RPC_EditAllianceName              RPC = 1074
	RPC_EditAllianceAcronym           RPC = 1075
	RPC_EditRecruitSetting            RPC = 1076
	RPC_EditAllianceStepName          RPC = 1077
	RPC_EditAllianceFlag              RPC = 1078
	RPC_TransferPresident             RPC = 1079
	RPC_RemoveMember                  RPC = 1080
	RPC_ChangeMemberStep              RPC = 1081
	RPC_GetAllianceAppList            RPC = 1082
	RPC_HandleAllianceApp             RPC = 1083
	RPC_DisbandAlliance               RPC = 1084
	RPC_EditAllianceNotice            RPC = 1085
	RPC_GetAllianceList               RPC = 1086
	RPC_CancerJoinAlliance            RPC = 1087
	RPC_BuyAllianceShop               RPC = 1088
	RPC_CollectAllianceTaskReward     RPC = 1089
	RPC_CollectAllianceChestReward    RPC = 1090
	RPC_GetUserInfoList               RPC = 1091
	RPC_AddFriends                    RPC = 1092
	RPC_DelFriends                    RPC = 1093
	RPC_GetFriendsList                RPC = 1094
	RPC_AddBlackList                  RPC = 1095
	RPC_DelBlackList                  RPC = 1096
	RPC_GetBlackList                  RPC = 1097
	RPC_GetFriendRecommendationList   RPC = 1098
	RPC_GetFriendAppList              RPC = 1099
	RPC_HandleFriendApp               RPC = 1100
	RPC_SettingAddFriendCondition     RPC = 1101
	RPC_HookSendMessage               RPC = 1102
	RPC_SubmitOrder                   RPC = 1103
	RPC_GetPowerRankInfo              RPC = 1104
	RPC_UpgradeLordEquipLevel         RPC = 1105
	RPC_GemCraft                      RPC = 1106
	RPC_EnhanceGem                    RPC = 1107
	RPC_EquipGem                      RPC = 1108
	RPC_UnEquipGem                    RPC = 1109
	RPC_UpgradeLordEquipGrade         RPC = 1110
	RPC_LockGem                       RPC = 1111
	RPC_UnlockGem                     RPC = 1112
	RPC_SwitchEquipGem                RPC = 1113
	RPC_LordGemRandom                 RPC = 1114
	RPC_HeroUpgradeQuality            RPC = 1115
	RPC_SweepDungeon                  RPC = 1116
	RPC_SelectDungeonRougeTab         RPC = 1117
	RPC_StartDungeon                  RPC = 1118
	RPC_RefreshDungeonRougeTab        RPC = 1119
	RPC_SaveFunctionOpen              RPC = 1120
	RPC_SaveGuideProgress             RPC = 1121
	RPC_GetActivityList               RPC = 1122
	RPC_CollectSign7Reward            RPC = 1123
	RPC_CollectDay7Reward             RPC = 1124
	RPC_CollectDay7ChestReward        RPC = 1125
	RPC_SelectDay7Reward              RPC = 1126
	RPC_CollectMailReward             RPC = 1127
	RPC_ReadMail                      RPC = 1128
	RPC_DelMail                       RPC = 1129
	RPC_DeleteAllReadMail             RPC = 1130
	RPC_ReadAndCollectAllMail         RPC = 1131
	RPC_CollectGrowthFundReward       RPC = 1132
	RPC_GetFirstChargeReward          RPC = 1133
	RPC_ReceiveMonthCardReward        RPC = 1134
	RPC_CollectDailySaleFreeReward    RPC = 1135
	RPC_CollectDailyWeekTaskChest     RPC = 1136
	RPC_InitSymbiotic                 RPC = 1137
	RPC_SwitchSymbioticHero           RPC = 1138
	RPC_CollectAchievementTaskReward  RPC = 1139
	RPC_OneKeyUpgradeHero             RPC = 1140
	RPC_OneKeyEquipGem                RPC = 1141
	RPC_CollectGroupReward            RPC = 1142
	RPC_PushMsgs                      RPC = 6001
	RPC_PushPBMsgs                    RPC = 6004
	RPC_PushPBTopicMsg                RPC = 6005
	RPC_PushOrderFinish               RPC = 6006
)

var autofit codec.Autofit

var (
	fakeConn = func() wnet.Conn {
		c := wnet.NewFakeConn(nil)
		c.Opts = server.DefaultConnOptsB
		return c
	}()
)

var (
	liveHelper = live.NewHelper(nil)
)

var ID2Name = map[int32]string{
	4:    "SimpleError",
	9:    "MulticastError",
	100:  "UserOnline",
	101:  "UpdateUserTopics",
	103:  "KickOut",
	104:  "UserOffline",
	105:  "WantUserOfflineNtf",
	106:  "UpdateUserConnWarehouse",
	107:  "Echo",
	108:  "PerformUserConnHealthcheck",
	109:  "SyncServerLoad",
	501:  "RequestDispatcher",
	1001: "RegisterUser",
	1002: "Login",
	1003: "HelloWorld",
	1004: "VerifyUser",
	1005: "AddBuilding",
	1006: "UpgradeBuilding",
	1007: "HeroLottery",
	1008: "HeroUpgradeLevel",
	1009: "HeroUpgradeStar",
	1010: "DebugAddItem",
	1011: "BuildWorkHero",
	1012: "CollectResource",
	1013: "SetHeroBattlePos",
	1014: "SetHeroTroop",
	1015: "SetDefaultBattlePos",
	1016: "CollectHeroLotteryAccReward",
	1017: "CollectHeroLotteryLevelReward",
	1018: "RandomHeroDice",
	1019: "LockHeroDice",
	1020: "TrainTroops",
	1021: "UserItem",
	1022: "BuildingWorkVillage",
	1023: "FinishDungeonStage",
	1024: "CollectFirstPassReward",
	1025: "StartResearch",
	1026: "CancerResearch",
	1028: "CollectMainReward",
	1029: "CollectMapChapterReward",
	1030: "FinishBuilding",
	1031: "FinishResearch",
	1032: "FinishTrain",
	1033: "FinishMapEvent",
	1034: "Debug",
	1035: "OnBackCity",
	1036: "UpgradeSkillLevel",
	1037: "UpgradeDaveLevel",
	1038: "FinishMainStage",
	1039: "CollectIdleReward",
	1040: "ResetMainStage",
	1041: "KillMonster",
	1042: "StartMainStage",
	1043: "GetIdleReward",
	1044: "GetManifest",
	1045: "UploadDeviceInfo",
	1046: "RefreshRougeSkill",
	1047: "HeartBeat",
	1048: "SelectRougeSkill",
	1049: "CollectStageReward",
	1050: "GetGiftList",
	1051: "PrepareOrder",
	1052: "HeroUpgradeGene",
	1053: "SelectEliteRougeSkill",
	1054: "GetStageRankInfo",
	1055: "GetStageRankInfoByStageId",
	1056: "CollectStageLevelRewards",
	1057: "GetPhotovoltaicReward",
	1058: "CollectPhotovoltaicReward",
	1059: "SweepMainStage",
	1060: "GetAllStageRankInfo",
	1061: "ChangeName",
	1062: "ChangeAvatar",
	1063: "CollectMonsterBookReward",
	1064: "HeroBeKilled",
	1065: "GetDailyTask",
	1066: "CollectDailyTaskReward",
	1067: "CollectDailyChestReward",
	1068: "GetMailList",
	1069: "GetAllianceInfo",
	1070: "CreateAlliance",
	1071: "ApplyJoinAlliance",
	1072: "GetAllianceMembersInfos",
	1073: "LeaveAlliance",
	1074: "EditAllianceName",
	1075: "EditAllianceAcronym",
	1076: "EditRecruitSetting",
	1077: "EditAllianceStepName",
	1078: "EditAllianceFlag",
	1079: "TransferPresident",
	1080: "RemoveMember",
	1081: "ChangeMemberStep",
	1082: "GetAllianceAppList",
	1083: "HandleAllianceApp",
	1084: "DisbandAlliance",
	1085: "EditAllianceNotice",
	1086: "GetAllianceList",
	1087: "CancerJoinAlliance",
	1088: "BuyAllianceShop",
	1089: "CollectAllianceTaskReward",
	1090: "CollectAllianceChestReward",
	1091: "GetUserInfoList",
	1092: "AddFriends",
	1093: "DelFriends",
	1094: "GetFriendsList",
	1095: "AddBlackList",
	1096: "DelBlackList",
	1097: "GetBlackList",
	1098: "GetFriendRecommendationList",
	1099: "GetFriendAppList",
	1100: "HandleFriendApp",
	1101: "SettingAddFriendCondition",
	1102: "HookSendMessage",
	1103: "SubmitOrder",
	1104: "GetPowerRankInfo",
	1105: "UpgradeLordEquipLevel",
	1106: "GemCraft",
	1107: "EnhanceGem",
	1108: "EquipGem",
	1109: "UnEquipGem",
	1110: "UpgradeLordEquipGrade",
	1111: "LockGem",
	1112: "UnlockGem",
	1113: "SwitchEquipGem",
	1114: "LordGemRandom",
	1115: "HeroUpgradeQuality",
	1116: "SweepDungeon",
	1117: "SelectDungeonRougeTab",
	1118: "StartDungeon",
	1119: "RefreshDungeonRougeTab",
	1120: "SaveFunctionOpen",
	1121: "SaveGuideProgress",
	1122: "GetActivityList",
	1123: "CollectSign7Reward",
	1124: "CollectDay7Reward",
	1125: "CollectDay7ChestReward",
	1126: "SelectDay7Reward",
	1127: "CollectMailReward",
	1128: "ReadMail",
	1129: "DelMail",
	1130: "DeleteAllReadMail",
	1131: "ReadAndCollectAllMail",
	1132: "CollectGrowthFundReward",
	1133: "GetFirstChargeReward",
	1134: "ReceiveMonthCardReward",
	1135: "CollectDailySaleFreeReward",
	1136: "CollectDailyWeekTaskChest",
	1137: "InitSymbiotic",
	1138: "SwitchSymbioticHero",
	1139: "CollectAchievementTaskReward",
	1140: "OneKeyUpgradeHero",
	1141: "OneKeyEquipGem",
	1142: "CollectGroupReward",
	6001: "PushMsgs",
	6004: "PushPBMsgs",
	6005: "PushPBTopicMsg",
	6006: "PushOrderFinish",
}

var Name2ID = map[string]int32{
	"SimpleError":                   4,
	"MulticastError":                9,
	"UserOnline":                    100,
	"UpdateUserTopics":              101,
	"KickOut":                       103,
	"UserOffline":                   104,
	"WantUserOfflineNtf":            105,
	"UpdateUserConnWarehouse":       106,
	"Echo":                          107,
	"PerformUserConnHealthcheck":    108,
	"SyncServerLoad":                109,
	"RequestDispatcher":             501,
	"RegisterUser":                  1001,
	"Login":                         1002,
	"HelloWorld":                    1003,
	"VerifyUser":                    1004,
	"AddBuilding":                   1005,
	"UpgradeBuilding":               1006,
	"HeroLottery":                   1007,
	"HeroUpgradeLevel":              1008,
	"HeroUpgradeStar":               1009,
	"DebugAddItem":                  1010,
	"BuildWorkHero":                 1011,
	"CollectResource":               1012,
	"SetHeroBattlePos":              1013,
	"SetHeroTroop":                  1014,
	"SetDefaultBattlePos":           1015,
	"CollectHeroLotteryAccReward":   1016,
	"CollectHeroLotteryLevelReward": 1017,
	"RandomHeroDice":                1018,
	"LockHeroDice":                  1019,
	"TrainTroops":                   1020,
	"UserItem":                      1021,
	"BuildingWorkVillage":           1022,
	"FinishDungeonStage":            1023,
	"CollectFirstPassReward":        1024,
	"StartResearch":                 1025,
	"CancerResearch":                1026,
	"CollectMainReward":             1028,
	"CollectMapChapterReward":       1029,
	"FinishBuilding":                1030,
	"FinishResearch":                1031,
	"FinishTrain":                   1032,
	"FinishMapEvent":                1033,
	"Debug":                         1034,
	"OnBackCity":                    1035,
	"UpgradeSkillLevel":             1036,
	"UpgradeDaveLevel":              1037,
	"FinishMainStage":               1038,
	"CollectIdleReward":             1039,
	"ResetMainStage":                1040,
	"KillMonster":                   1041,
	"StartMainStage":                1042,
	"GetIdleReward":                 1043,
	"GetManifest":                   1044,
	"UploadDeviceInfo":              1045,
	"RefreshRougeSkill":             1046,
	"HeartBeat":                     1047,
	"SelectRougeSkill":              1048,
	"CollectStageReward":            1049,
	"GetGiftList":                   1050,
	"PrepareOrder":                  1051,
	"HeroUpgradeGene":               1052,
	"SelectEliteRougeSkill":         1053,
	"GetStageRankInfo":              1054,
	"GetStageRankInfoByStageId":     1055,
	"CollectStageLevelRewards":      1056,
	"GetPhotovoltaicReward":         1057,
	"CollectPhotovoltaicReward":     1058,
	"SweepMainStage":                1059,
	"GetAllStageRankInfo":           1060,
	"ChangeName":                    1061,
	"ChangeAvatar":                  1062,
	"CollectMonsterBookReward":      1063,
	"HeroBeKilled":                  1064,
	"GetDailyTask":                  1065,
	"CollectDailyTaskReward":        1066,
	"CollectDailyChestReward":       1067,
	"GetMailList":                   1068,
	"GetAllianceInfo":               1069,
	"CreateAlliance":                1070,
	"ApplyJoinAlliance":             1071,
	"GetAllianceMembersInfos":       1072,
	"LeaveAlliance":                 1073,
	"EditAllianceName":              1074,
	"EditAllianceAcronym":           1075,
	"EditRecruitSetting":            1076,
	"EditAllianceStepName":          1077,
	"EditAllianceFlag":              1078,
	"TransferPresident":             1079,
	"RemoveMember":                  1080,
	"ChangeMemberStep":              1081,
	"GetAllianceAppList":            1082,
	"HandleAllianceApp":             1083,
	"DisbandAlliance":               1084,
	"EditAllianceNotice":            1085,
	"GetAllianceList":               1086,
	"CancerJoinAlliance":            1087,
	"BuyAllianceShop":               1088,
	"CollectAllianceTaskReward":     1089,
	"CollectAllianceChestReward":    1090,
	"GetUserInfoList":               1091,
	"AddFriends":                    1092,
	"DelFriends":                    1093,
	"GetFriendsList":                1094,
	"AddBlackList":                  1095,
	"DelBlackList":                  1096,
	"GetBlackList":                  1097,
	"GetFriendRecommendationList":   1098,
	"GetFriendAppList":              1099,
	"HandleFriendApp":               1100,
	"SettingAddFriendCondition":     1101,
	"HookSendMessage":               1102,
	"SubmitOrder":                   1103,
	"GetPowerRankInfo":              1104,
	"UpgradeLordEquipLevel":         1105,
	"GemCraft":                      1106,
	"EnhanceGem":                    1107,
	"EquipGem":                      1108,
	"UnEquipGem":                    1109,
	"UpgradeLordEquipGrade":         1110,
	"LockGem":                       1111,
	"UnlockGem":                     1112,
	"SwitchEquipGem":                1113,
	"LordGemRandom":                 1114,
	"HeroUpgradeQuality":            1115,
	"SweepDungeon":                  1116,
	"SelectDungeonRougeTab":         1117,
	"StartDungeon":                  1118,
	"RefreshDungeonRougeTab":        1119,
	"SaveFunctionOpen":              1120,
	"SaveGuideProgress":             1121,
	"GetActivityList":               1122,
	"CollectSign7Reward":            1123,
	"CollectDay7Reward":             1124,
	"CollectDay7ChestReward":        1125,
	"SelectDay7Reward":              1126,
	"CollectMailReward":             1127,
	"ReadMail":                      1128,
	"DelMail":                       1129,
	"DeleteAllReadMail":             1130,
	"ReadAndCollectAllMail":         1131,
	"CollectGrowthFundReward":       1132,
	"GetFirstChargeReward":          1133,
	"ReceiveMonthCardReward":        1134,
	"CollectDailySaleFreeReward":    1135,
	"CollectDailyWeekTaskChest":     1136,
	"InitSymbiotic":                 1137,
	"SwitchSymbioticHero":           1138,
	"CollectAchievementTaskReward":  1139,
	"OneKeyUpgradeHero":             1140,
	"OneKeyEquipGem":                1141,
	"CollectGroupReward":            1142,
	"PushMsgs":                      6001,
	"PushPBMsgs":                    6004,
	"PushPBTopicMsg":                6005,
	"PushOrderFinish":               6006,
}

func (x RPC) String() string {
	if name, ok := ID2Name[int32(x)]; ok {
		return name
	}
	return strconv.Itoa(int(x))
}

func LookupModifier(rpc int32) enum.Modifier {
	switch RPC(rpc) {
	case RPC_SimpleError:
		return enum.Modifier_GS
	case RPC_MulticastError:
		return enum.Modifier_GS
	case RPC_UserOnline:
		return enum.Modifier_SG
	case RPC_UpdateUserTopics:
		return enum.Modifier_SG
	case RPC_KickOut:
		return enum.Modifier_SG
	case RPC_UserOffline:
		return enum.Modifier_GS
	case RPC_WantUserOfflineNtf:
		return enum.Modifier_SG
	case RPC_UpdateUserConnWarehouse:
		return enum.Modifier_SG
	case RPC_Echo:
		return enum.Modifier_SG
	case RPC_PerformUserConnHealthcheck:
		return enum.Modifier_SG
	case RPC_SyncServerLoad:
		return enum.Modifier_BC
	case RPC_RequestDispatcher:
		return enum.Modifier_CS
	case RPC_RegisterUser:
		return enum.Modifier_CS
	case RPC_Login:
		return enum.Modifier_CS
	case RPC_HelloWorld:
		return enum.Modifier_CS
	case RPC_VerifyUser:
		return enum.Modifier_CS
	case RPC_AddBuilding:
		return enum.Modifier_CS
	case RPC_UpgradeBuilding:
		return enum.Modifier_CS
	case RPC_HeroLottery:
		return enum.Modifier_CS
	case RPC_HeroUpgradeLevel:
		return enum.Modifier_CS
	case RPC_HeroUpgradeStar:
		return enum.Modifier_CS
	case RPC_DebugAddItem:
		return enum.Modifier_CS
	case RPC_BuildWorkHero:
		return enum.Modifier_CS
	case RPC_CollectResource:
		return enum.Modifier_CS
	case RPC_SetHeroBattlePos:
		return enum.Modifier_CS
	case RPC_SetHeroTroop:
		return enum.Modifier_CS
	case RPC_SetDefaultBattlePos:
		return enum.Modifier_CS
	case RPC_CollectHeroLotteryAccReward:
		return enum.Modifier_CS
	case RPC_CollectHeroLotteryLevelReward:
		return enum.Modifier_CS
	case RPC_RandomHeroDice:
		return enum.Modifier_CS
	case RPC_LockHeroDice:
		return enum.Modifier_CS
	case RPC_TrainTroops:
		return enum.Modifier_CS
	case RPC_UserItem:
		return enum.Modifier_CS
	case RPC_BuildingWorkVillage:
		return enum.Modifier_CS
	case RPC_FinishDungeonStage:
		return enum.Modifier_CS
	case RPC_CollectFirstPassReward:
		return enum.Modifier_CS
	case RPC_StartResearch:
		return enum.Modifier_CS
	case RPC_CancerResearch:
		return enum.Modifier_CS
	case RPC_CollectMainReward:
		return enum.Modifier_CS
	case RPC_CollectMapChapterReward:
		return enum.Modifier_CS
	case RPC_FinishBuilding:
		return enum.Modifier_CS
	case RPC_FinishResearch:
		return enum.Modifier_CS
	case RPC_FinishTrain:
		return enum.Modifier_CS
	case RPC_FinishMapEvent:
		return enum.Modifier_CS
	case RPC_Debug:
		return enum.Modifier_CS
	case RPC_OnBackCity:
		return enum.Modifier_CS
	case RPC_UpgradeSkillLevel:
		return enum.Modifier_CS
	case RPC_UpgradeDaveLevel:
		return enum.Modifier_CS
	case RPC_FinishMainStage:
		return enum.Modifier_CS
	case RPC_CollectIdleReward:
		return enum.Modifier_CS
	case RPC_ResetMainStage:
		return enum.Modifier_CS
	case RPC_KillMonster:
		return enum.Modifier_CS
	case RPC_StartMainStage:
		return enum.Modifier_CS
	case RPC_GetIdleReward:
		return enum.Modifier_CS
	case RPC_GetManifest:
		return enum.Modifier_CS
	case RPC_UploadDeviceInfo:
		return enum.Modifier_CS
	case RPC_RefreshRougeSkill:
		return enum.Modifier_CS
	case RPC_HeartBeat:
		return enum.Modifier_CS
	case RPC_SelectRougeSkill:
		return enum.Modifier_CS
	case RPC_CollectStageReward:
		return enum.Modifier_CS
	case RPC_GetGiftList:
		return enum.Modifier_CS
	case RPC_PrepareOrder:
		return enum.Modifier_CS
	case RPC_HeroUpgradeGene:
		return enum.Modifier_CS
	case RPC_SelectEliteRougeSkill:
		return enum.Modifier_CS
	case RPC_GetStageRankInfo:
		return enum.Modifier_CS
	case RPC_GetStageRankInfoByStageId:
		return enum.Modifier_CS
	case RPC_CollectStageLevelRewards:
		return enum.Modifier_CS
	case RPC_GetPhotovoltaicReward:
		return enum.Modifier_CS
	case RPC_CollectPhotovoltaicReward:
		return enum.Modifier_CS
	case RPC_SweepMainStage:
		return enum.Modifier_CS
	case RPC_GetAllStageRankInfo:
		return enum.Modifier_CS
	case RPC_ChangeName:
		return enum.Modifier_CS
	case RPC_ChangeAvatar:
		return enum.Modifier_CS
	case RPC_CollectMonsterBookReward:
		return enum.Modifier_CS
	case RPC_HeroBeKilled:
		return enum.Modifier_CS
	case RPC_GetDailyTask:
		return enum.Modifier_CS
	case RPC_CollectDailyTaskReward:
		return enum.Modifier_CS
	case RPC_CollectDailyChestReward:
		return enum.Modifier_CS
	case RPC_GetMailList:
		return enum.Modifier_CS
	case RPC_GetAllianceInfo:
		return enum.Modifier_CS
	case RPC_CreateAlliance:
		return enum.Modifier_CS
	case RPC_ApplyJoinAlliance:
		return enum.Modifier_CS
	case RPC_GetAllianceMembersInfos:
		return enum.Modifier_CS
	case RPC_LeaveAlliance:
		return enum.Modifier_CS
	case RPC_EditAllianceName:
		return enum.Modifier_CS
	case RPC_EditAllianceAcronym:
		return enum.Modifier_CS
	case RPC_EditRecruitSetting:
		return enum.Modifier_CS
	case RPC_EditAllianceStepName:
		return enum.Modifier_CS
	case RPC_EditAllianceFlag:
		return enum.Modifier_CS
	case RPC_TransferPresident:
		return enum.Modifier_CS
	case RPC_RemoveMember:
		return enum.Modifier_CS
	case RPC_ChangeMemberStep:
		return enum.Modifier_CS
	case RPC_GetAllianceAppList:
		return enum.Modifier_CS
	case RPC_HandleAllianceApp:
		return enum.Modifier_CS
	case RPC_DisbandAlliance:
		return enum.Modifier_CS
	case RPC_EditAllianceNotice:
		return enum.Modifier_CS
	case RPC_GetAllianceList:
		return enum.Modifier_CS
	case RPC_CancerJoinAlliance:
		return enum.Modifier_CS
	case RPC_BuyAllianceShop:
		return enum.Modifier_CS
	case RPC_CollectAllianceTaskReward:
		return enum.Modifier_CS
	case RPC_CollectAllianceChestReward:
		return enum.Modifier_CS
	case RPC_GetUserInfoList:
		return enum.Modifier_CS
	case RPC_AddFriends:
		return enum.Modifier_CS
	case RPC_DelFriends:
		return enum.Modifier_CS
	case RPC_GetFriendsList:
		return enum.Modifier_CS
	case RPC_AddBlackList:
		return enum.Modifier_CS
	case RPC_DelBlackList:
		return enum.Modifier_CS
	case RPC_GetBlackList:
		return enum.Modifier_CS
	case RPC_GetFriendRecommendationList:
		return enum.Modifier_CS
	case RPC_GetFriendAppList:
		return enum.Modifier_CS
	case RPC_HandleFriendApp:
		return enum.Modifier_CS
	case RPC_SettingAddFriendCondition:
		return enum.Modifier_CS
	case RPC_HookSendMessage:
		return enum.Modifier_CS
	case RPC_SubmitOrder:
		return enum.Modifier_CS
	case RPC_GetPowerRankInfo:
		return enum.Modifier_CS
	case RPC_UpgradeLordEquipLevel:
		return enum.Modifier_CS
	case RPC_GemCraft:
		return enum.Modifier_CS
	case RPC_EnhanceGem:
		return enum.Modifier_CS
	case RPC_EquipGem:
		return enum.Modifier_CS
	case RPC_UnEquipGem:
		return enum.Modifier_CS
	case RPC_UpgradeLordEquipGrade:
		return enum.Modifier_CS
	case RPC_LockGem:
		return enum.Modifier_CS
	case RPC_UnlockGem:
		return enum.Modifier_CS
	case RPC_SwitchEquipGem:
		return enum.Modifier_CS
	case RPC_LordGemRandom:
		return enum.Modifier_CS
	case RPC_HeroUpgradeQuality:
		return enum.Modifier_CS
	case RPC_SweepDungeon:
		return enum.Modifier_CS
	case RPC_SelectDungeonRougeTab:
		return enum.Modifier_CS
	case RPC_StartDungeon:
		return enum.Modifier_CS
	case RPC_RefreshDungeonRougeTab:
		return enum.Modifier_CS
	case RPC_SaveFunctionOpen:
		return enum.Modifier_CS
	case RPC_SaveGuideProgress:
		return enum.Modifier_CS
	case RPC_GetActivityList:
		return enum.Modifier_CS
	case RPC_CollectSign7Reward:
		return enum.Modifier_CS
	case RPC_CollectDay7Reward:
		return enum.Modifier_CS
	case RPC_CollectDay7ChestReward:
		return enum.Modifier_CS
	case RPC_SelectDay7Reward:
		return enum.Modifier_CS
	case RPC_CollectMailReward:
		return enum.Modifier_CS
	case RPC_ReadMail:
		return enum.Modifier_CS
	case RPC_DelMail:
		return enum.Modifier_CS
	case RPC_DeleteAllReadMail:
		return enum.Modifier_CS
	case RPC_ReadAndCollectAllMail:
		return enum.Modifier_CS
	case RPC_CollectGrowthFundReward:
		return enum.Modifier_CS
	case RPC_GetFirstChargeReward:
		return enum.Modifier_CS
	case RPC_ReceiveMonthCardReward:
		return enum.Modifier_CS
	case RPC_CollectDailySaleFreeReward:
		return enum.Modifier_CS
	case RPC_CollectDailyWeekTaskChest:
		return enum.Modifier_CS
	case RPC_InitSymbiotic:
		return enum.Modifier_CS
	case RPC_SwitchSymbioticHero:
		return enum.Modifier_CS
	case RPC_CollectAchievementTaskReward:
		return enum.Modifier_CS
	case RPC_OneKeyUpgradeHero:
		return enum.Modifier_CS
	case RPC_OneKeyEquipGem:
		return enum.Modifier_CS
	case RPC_CollectGroupReward:
		return enum.Modifier_CS
	case RPC_PushMsgs:
		return enum.Modifier_SC
	case RPC_PushPBMsgs:
		return enum.Modifier_SC
	case RPC_PushPBTopicMsg:
		return enum.Modifier_TP
	case RPC_PushOrderFinish:
		return enum.Modifier_SC
	default:
		return enum.Modifier_Invalid
	}
}

var defaultMessageEncoding = enum.Codec_Protobuf

func SetDefaultMessageEncoding(cdc enum.Codec) {
	switch cdc {
	case enum.Codec_Protobuf, enum.Codec_JSON:
		defaultMessageEncoding = cdc
	default:
		panic(fmt.Errorf("invalid codec ID: %d", cdc))
	}
}

func DefaultMessageEncoding() enum.Codec {
	return defaultMessageEncoding
}

type (
	unmarshaler func(ctx *wctx.BizContext, conn wnet.Conn, msg wnet.Message) (interface{}, error)
	invoker     func(ctx *wctx.Context, req interface{}, handlers WrpcHandlers) (interface{}, error)
)

var (
	unmarshalerMap = map[int32]unmarshaler{
		4:    unmarshalSimpleErrorNtf,
		9:    unmarshalMulticastErrorNtf,
		104:  unmarshalUserOfflineNtf,
		501:  unmarshalRequestDispatcherRequest,
		1001: unmarshalRegisterUserRequest,
		1002: unmarshalLoginRequest,
		1003: unmarshalHelloWorldRequest,
		1004: unmarshalVerifyUserRequest,
		1005: unmarshalAddBuildingRequest,
		1006: unmarshalUpgradeBuildingRequest,
		1007: unmarshalHeroLotteryRequest,
		1008: unmarshalHeroUpgradeLevelRequest,
		1009: unmarshalHeroUpgradeStarRequest,
		1010: unmarshalDebugAddItemRequest,
		1011: unmarshalBuildWorkHeroRequest,
		1012: unmarshalCollectResourceRequest,
		1013: unmarshalSetHeroBattlePosRequest,
		1014: unmarshalSetHeroTroopRequest,
		1015: unmarshalSetDefaultBattlePosRequest,
		1016: unmarshalCollectHeroLotteryAccRewardRequest,
		1017: unmarshalCollectHeroLotteryLevelRewardRequest,
		1018: unmarshalRandomHeroDiceRequest,
		1019: unmarshalLockHeroDiceRequest,
		1020: unmarshalTrainTroopsRequest,
		1021: unmarshalUserItemRequest,
		1022: unmarshalBuildingWorkVillageRequest,
		1023: unmarshalFinishDungeonStageRequest,
		1024: unmarshalCollectFirstPassRewardRequest,
		1025: unmarshalStartResearchRequest,
		1026: unmarshalCancerResearchRequest,
		1028: unmarshalCollectMainRewardRequest,
		1029: unmarshalCollectMapChapterRewardRequest,
		1030: unmarshalFinishBuildingRequest,
		1031: unmarshalFinishResearchRequest,
		1032: unmarshalFinishTrainRequest,
		1033: unmarshalFinishMapEventRequest,
		1034: unmarshalDebugRequest,
		1035: unmarshalOnBackCityRequest,
		1036: unmarshalUpgradeSkillLevelRequest,
		1037: unmarshalUpgradeDaveLevelRequest,
		1038: unmarshalFinishMainStageRequest,
		1039: unmarshalCollectIdleRewardRequest,
		1040: unmarshalResetMainStageRequest,
		1041: unmarshalKillMonsterRequest,
		1042: unmarshalStartMainStageRequest,
		1043: unmarshalGetIdleRewardRequest,
		1044: unmarshalGetManifestRequest,
		1045: unmarshalUploadDeviceInfoRequest,
		1046: unmarshalRefreshRougeSkillRequest,
		1047: unmarshalHeartBeatRequest,
		1048: unmarshalSelectRougeSkillRequest,
		1049: unmarshalCollectStageRewardRequest,
		1050: unmarshalGetGiftListRequest,
		1051: unmarshalPrepareOrderRequest,
		1052: unmarshalHeroUpgradeGeneRequest,
		1053: unmarshalSelectEliteRougeSkillRequest,
		1054: unmarshalGetStageRankInfoRequest,
		1055: unmarshalGetStageRankInfoByStageIdRequest,
		1056: unmarshalCollectStageLevelRewardsRequest,
		1057: unmarshalGetPhotovoltaicRewardRequest,
		1058: unmarshalCollectPhotovoltaicRewardRequest,
		1059: unmarshalSweepMainStageRequest,
		1060: unmarshalGetAllStageRankInfoRequest,
		1061: unmarshalChangeNameRequest,
		1062: unmarshalChangeAvatarRequest,
		1063: unmarshalCollectMonsterBookRewardRequest,
		1064: unmarshalHeroBeKilledRequest,
		1065: unmarshalGetDailyTaskRequest,
		1066: unmarshalCollectDailyTaskRewardRequest,
		1067: unmarshalCollectDailyChestRewardRequest,
		1068: unmarshalGetMailListRequest,
		1069: unmarshalGetAllianceInfoRequest,
		1070: unmarshalCreateAllianceRequest,
		1071: unmarshalApplyJoinAllianceRequest,
		1072: unmarshalGetAllianceMembersInfosRequest,
		1073: unmarshalLeaveAllianceRequest,
		1074: unmarshalEditAllianceNameRequest,
		1075: unmarshalEditAllianceAcronymRequest,
		1076: unmarshalEditRecruitSettingRequest,
		1077: unmarshalEditAllianceStepNameRequest,
		1078: unmarshalEditAllianceFlagRequest,
		1079: unmarshalTransferPresidentRequest,
		1080: unmarshalRemoveMemberRequest,
		1081: unmarshalChangeMemberStepRequest,
		1082: unmarshalGetAllianceAppListRequest,
		1083: unmarshalHandleAllianceAppRequest,
		1084: unmarshalDisbandAllianceRequest,
		1085: unmarshalEditAllianceNoticeRequest,
		1086: unmarshalGetAllianceListRequest,
		1087: unmarshalCancerJoinAllianceRequest,
		1088: unmarshalBuyAllianceShopRequest,
		1089: unmarshalCollectAllianceTaskRewardRequest,
		1090: unmarshalCollectAllianceChestRewardRequest,
		1091: unmarshalGetUserInfoListRequest,
		1092: unmarshalAddFriendsRequest,
		1093: unmarshalDelFriendsRequest,
		1094: unmarshalGetFriendsListRequest,
		1095: unmarshalAddBlackListRequest,
		1096: unmarshalDelBlackListRequest,
		1097: unmarshalGetBlackListRequest,
		1098: unmarshalGetFriendRecommendationListRequest,
		1099: unmarshalGetFriendAppListRequest,
		1100: unmarshalHandleFriendAppRequest,
		1101: unmarshalSettingAddFriendConditionRequest,
		1102: unmarshalHookSendMessageRequest,
		1103: unmarshalSubmitOrderRequest,
		1104: unmarshalGetPowerRankInfoRequest,
		1105: unmarshalUpgradeLordEquipLevelRequest,
		1106: unmarshalGemCraftRequest,
		1107: unmarshalEnhanceGemRequest,
		1108: unmarshalEquipGemRequest,
		1109: unmarshalUnEquipGemRequest,
		1110: unmarshalUpgradeLordEquipGradeRequest,
		1111: unmarshalLockGemRequest,
		1112: unmarshalUnlockGemRequest,
		1113: unmarshalSwitchEquipGemRequest,
		1114: unmarshalLordGemRandomRequest,
		1115: unmarshalHeroUpgradeQualityRequest,
		1116: unmarshalSweepDungeonRequest,
		1117: unmarshalSelectDungeonRougeTabRequest,
		1118: unmarshalStartDungeonRequest,
		1119: unmarshalRefreshDungeonRougeTabRequest,
		1120: unmarshalSaveFunctionOpenRequest,
		1121: unmarshalSaveGuideProgressRequest,
		1122: unmarshalGetActivityListRequest,
		1123: unmarshalCollectSign7RewardRequest,
		1124: unmarshalCollectDay7RewardRequest,
		1125: unmarshalCollectDay7ChestRewardRequest,
		1126: unmarshalSelectDay7RewardRequest,
		1127: unmarshalCollectMailRewardRequest,
		1128: unmarshalReadMailRequest,
		1129: unmarshalDelMailRequest,
		1130: unmarshalDeleteAllReadMailRequest,
		1131: unmarshalReadAndCollectAllMailRequest,
		1132: unmarshalCollectGrowthFundRewardRequest,
		1133: unmarshalGetFirstChargeRewardRequest,
		1134: unmarshalReceiveMonthCardRewardRequest,
		1135: unmarshalCollectDailySaleFreeRewardRequest,
		1136: unmarshalCollectDailyWeekTaskChestRequest,
		1137: unmarshalInitSymbioticRequest,
		1138: unmarshalSwitchSymbioticHeroRequest,
		1139: unmarshalCollectAchievementTaskRewardRequest,
		1140: unmarshalOneKeyUpgradeHeroRequest,
		1141: unmarshalOneKeyEquipGemRequest,
		1142: unmarshalCollectGroupRewardRequest,
	}

	invokerMap = map[int32]invoker{
		4:    invokeSimpleErrorHandler,
		9:    invokeMulticastErrorHandler,
		104:  invokeUserOfflineHandler,
		501:  invokeRequestDispatcherHandler,
		1001: invokeRegisterUserHandler,
		1002: invokeLoginHandler,
		1003: invokeHelloWorldHandler,
		1004: invokeVerifyUserHandler,
		1005: invokeAddBuildingHandler,
		1006: invokeUpgradeBuildingHandler,
		1007: invokeHeroLotteryHandler,
		1008: invokeHeroUpgradeLevelHandler,
		1009: invokeHeroUpgradeStarHandler,
		1010: invokeDebugAddItemHandler,
		1011: invokeBuildWorkHeroHandler,
		1012: invokeCollectResourceHandler,
		1013: invokeSetHeroBattlePosHandler,
		1014: invokeSetHeroTroopHandler,
		1015: invokeSetDefaultBattlePosHandler,
		1016: invokeCollectHeroLotteryAccRewardHandler,
		1017: invokeCollectHeroLotteryLevelRewardHandler,
		1018: invokeRandomHeroDiceHandler,
		1019: invokeLockHeroDiceHandler,
		1020: invokeTrainTroopsHandler,
		1021: invokeUserItemHandler,
		1022: invokeBuildingWorkVillageHandler,
		1023: invokeFinishDungeonStageHandler,
		1024: invokeCollectFirstPassRewardHandler,
		1025: invokeStartResearchHandler,
		1026: invokeCancerResearchHandler,
		1028: invokeCollectMainRewardHandler,
		1029: invokeCollectMapChapterRewardHandler,
		1030: invokeFinishBuildingHandler,
		1031: invokeFinishResearchHandler,
		1032: invokeFinishTrainHandler,
		1033: invokeFinishMapEventHandler,
		1034: invokeDebugHandler,
		1035: invokeOnBackCityHandler,
		1036: invokeUpgradeSkillLevelHandler,
		1037: invokeUpgradeDaveLevelHandler,
		1038: invokeFinishMainStageHandler,
		1039: invokeCollectIdleRewardHandler,
		1040: invokeResetMainStageHandler,
		1041: invokeKillMonsterHandler,
		1042: invokeStartMainStageHandler,
		1043: invokeGetIdleRewardHandler,
		1044: invokeGetManifestHandler,
		1045: invokeUploadDeviceInfoHandler,
		1046: invokeRefreshRougeSkillHandler,
		1047: invokeHeartBeatHandler,
		1048: invokeSelectRougeSkillHandler,
		1049: invokeCollectStageRewardHandler,
		1050: invokeGetGiftListHandler,
		1051: invokePrepareOrderHandler,
		1052: invokeHeroUpgradeGeneHandler,
		1053: invokeSelectEliteRougeSkillHandler,
		1054: invokeGetStageRankInfoHandler,
		1055: invokeGetStageRankInfoByStageIdHandler,
		1056: invokeCollectStageLevelRewardsHandler,
		1057: invokeGetPhotovoltaicRewardHandler,
		1058: invokeCollectPhotovoltaicRewardHandler,
		1059: invokeSweepMainStageHandler,
		1060: invokeGetAllStageRankInfoHandler,
		1061: invokeChangeNameHandler,
		1062: invokeChangeAvatarHandler,
		1063: invokeCollectMonsterBookRewardHandler,
		1064: invokeHeroBeKilledHandler,
		1065: invokeGetDailyTaskHandler,
		1066: invokeCollectDailyTaskRewardHandler,
		1067: invokeCollectDailyChestRewardHandler,
		1068: invokeGetMailListHandler,
		1069: invokeGetAllianceInfoHandler,
		1070: invokeCreateAllianceHandler,
		1071: invokeApplyJoinAllianceHandler,
		1072: invokeGetAllianceMembersInfosHandler,
		1073: invokeLeaveAllianceHandler,
		1074: invokeEditAllianceNameHandler,
		1075: invokeEditAllianceAcronymHandler,
		1076: invokeEditRecruitSettingHandler,
		1077: invokeEditAllianceStepNameHandler,
		1078: invokeEditAllianceFlagHandler,
		1079: invokeTransferPresidentHandler,
		1080: invokeRemoveMemberHandler,
		1081: invokeChangeMemberStepHandler,
		1082: invokeGetAllianceAppListHandler,
		1083: invokeHandleAllianceAppHandler,
		1084: invokeDisbandAllianceHandler,
		1085: invokeEditAllianceNoticeHandler,
		1086: invokeGetAllianceListHandler,
		1087: invokeCancerJoinAllianceHandler,
		1088: invokeBuyAllianceShopHandler,
		1089: invokeCollectAllianceTaskRewardHandler,
		1090: invokeCollectAllianceChestRewardHandler,
		1091: invokeGetUserInfoListHandler,
		1092: invokeAddFriendsHandler,
		1093: invokeDelFriendsHandler,
		1094: invokeGetFriendsListHandler,
		1095: invokeAddBlackListHandler,
		1096: invokeDelBlackListHandler,
		1097: invokeGetBlackListHandler,
		1098: invokeGetFriendRecommendationListHandler,
		1099: invokeGetFriendAppListHandler,
		1100: invokeHandleFriendAppHandler,
		1101: invokeSettingAddFriendConditionHandler,
		1102: invokeHookSendMessageHandler,
		1103: invokeSubmitOrderHandler,
		1104: invokeGetPowerRankInfoHandler,
		1105: invokeUpgradeLordEquipLevelHandler,
		1106: invokeGemCraftHandler,
		1107: invokeEnhanceGemHandler,
		1108: invokeEquipGemHandler,
		1109: invokeUnEquipGemHandler,
		1110: invokeUpgradeLordEquipGradeHandler,
		1111: invokeLockGemHandler,
		1112: invokeUnlockGemHandler,
		1113: invokeSwitchEquipGemHandler,
		1114: invokeLordGemRandomHandler,
		1115: invokeHeroUpgradeQualityHandler,
		1116: invokeSweepDungeonHandler,
		1117: invokeSelectDungeonRougeTabHandler,
		1118: invokeStartDungeonHandler,
		1119: invokeRefreshDungeonRougeTabHandler,
		1120: invokeSaveFunctionOpenHandler,
		1121: invokeSaveGuideProgressHandler,
		1122: invokeGetActivityListHandler,
		1123: invokeCollectSign7RewardHandler,
		1124: invokeCollectDay7RewardHandler,
		1125: invokeCollectDay7ChestRewardHandler,
		1126: invokeSelectDay7RewardHandler,
		1127: invokeCollectMailRewardHandler,
		1128: invokeReadMailHandler,
		1129: invokeDelMailHandler,
		1130: invokeDeleteAllReadMailHandler,
		1131: invokeReadAndCollectAllMailHandler,
		1132: invokeCollectGrowthFundRewardHandler,
		1133: invokeGetFirstChargeRewardHandler,
		1134: invokeReceiveMonthCardRewardHandler,
		1135: invokeCollectDailySaleFreeRewardHandler,
		1136: invokeCollectDailyWeekTaskChestHandler,
		1137: invokeInitSymbioticHandler,
		1138: invokeSwitchSymbioticHeroHandler,
		1139: invokeCollectAchievementTaskRewardHandler,
		1140: invokeOneKeyUpgradeHeroHandler,
		1141: invokeOneKeyEquipGemHandler,
		1142: invokeCollectGroupRewardHandler,
	}
)

type WrpcHandlers interface {
	Decorate(ctx *wctx.Context, hdr *wnet.Header, h func(ctx *wctx.Context) (interface{}, error)) (interface{}, error)
	// PushNotification pushes a message to users.
	PushNotification(paramX interface{}, msgID int32, payload interface{}, uids ...int64) (strays []int64, err error)
	// PushNotificationToUserConns pushes a message to stargate connections.
	PushNotificationToUserConns(paramX interface{}, memo []byte, msgID int32, payload interface{}, userConns []olcache.UserConn) error
	// SimpleErrorHandler handles unexpected errors
	SimpleErrorHandler(ctx *wctx.Context, ntf *SimpleErrorNtf) error
	// MulticastErrorHandler handles unexpected multicast errors
	MulticastErrorHandler(ctx *wctx.Context, ntf *MulticastErrorNtf) error
	// UserOfflineHandler handles the user offline notification
	UserOfflineHandler(ctx *wctx.Context, ntf *UserOfflineNtf) error
	// RequestDispatcher [501]...
	RequestDispatcherHandler(ctx *wctx.Context, req *RequestDispatcherRequest) (*RequestDispatcherReply, error)
	// RegisterUser [1001]...
	RegisterUserHandler(ctx *wctx.Context, req *RegisterUserRequest) (*RegisterUserReply, error)
	LoginHandler(ctx *wctx.Context, req *LoginRequest) (*LoginReply, error)
	HelloWorldHandler(ctx *wctx.Context, req *HelloWorldRequest) (*HelloWorldReply, error)
	VerifyUserHandler(ctx *wctx.Context, req *VerifyUserRequest) (*VerifyUserReply, error)
	AddBuildingHandler(ctx *wctx.Context, req *AddBuildingRequest) (*AddBuildingReply, error)
	//升级建筑
	UpgradeBuildingHandler(ctx *wctx.Context, req *UpgradeBuildingRequest) (*UpgradeBuildingReply, error)
	//英雄招募
	HeroLotteryHandler(ctx *wctx.Context, req *HeroLotteryRequest) (*HeroLotteryReply, error)
	//英雄升级
	HeroUpgradeLevelHandler(ctx *wctx.Context, req *HeroUpgradeLevelRequest) (*HeroUpgradeLevelReply, error)
	//英雄升星
	HeroUpgradeStarHandler(ctx *wctx.Context, req *HeroUpgradeStarRequest) (*HeroUpgradeStarReply, error)
	//添加道具
	DebugAddItemHandler(ctx *wctx.Context, req *DebugAddItemRequest) (*DebugAddItemReply, error)
	//派驻英雄
	BuildWorkHeroHandler(ctx *wctx.Context, req *BuildWorkHeroRequest) (*BuildWorkHeroReply, error)
	//采集资源
	CollectResourceHandler(ctx *wctx.Context, req *CollectResourceRequest) (*CollectResourceReply, error)
	//英雄上阵
	SetHeroBattlePosHandler(ctx *wctx.Context, req *SetHeroBattlePosRequest) (*SetHeroBattlePosReply, error)
	//设置英雄带兵
	SetHeroTroopHandler(ctx *wctx.Context, req *SetHeroTroopRequest) (*SetHeroTroopReply, error)
	//设置默认镜头
	SetDefaultBattlePosHandler(ctx *wctx.Context, req *SetDefaultBattlePosRequest) (*SetDefaultBattlePosReply, error)
	//领取招募累计次数奖励
	CollectHeroLotteryAccRewardHandler(ctx *wctx.Context, req *CollectHeroLotteryAccRewardRequest) (*CollectHeroLotteryAccRewardReply, error)
	//领取招募等级奖励
	CollectHeroLotteryLevelRewardHandler(ctx *wctx.Context, req *CollectHeroLotteryLevelRewardRequest) (*CollectHeroLotteryLevelRewardReply, error)
	//随机潜力
	RandomHeroDiceHandler(ctx *wctx.Context, req *RandomHeroDiceRequest) (*RandomHeroDiceReply, error)
	//设置潜力锁
	LockHeroDiceHandler(ctx *wctx.Context, req *LockHeroDiceRequest) (*LockHeroDiceReply, error)
	//造兵
	TrainTroopsHandler(ctx *wctx.Context, req *TrainTroopsRequest) (*TrainTroopsReply, error)
	//使用道具
	UserItemHandler(ctx *wctx.Context, req *UserItemRequest) (*UserItemReply, error)
	//派遣农民
	BuildingWorkVillageHandler(ctx *wctx.Context, req *BuildingWorkVillageRequest) (*BuildingWorkVillageReply, error)
	//完成挑战
	FinishDungeonStageHandler(ctx *wctx.Context, req *FinishDungeonStageRequest) (*FinishDungeonStageReply, error)
	//领取首胜奖励
	CollectFirstPassRewardHandler(ctx *wctx.Context, req *CollectFirstPassRewardRequest) (*CollectFirstPassRewardReply, error)
	//开始科技研究
	StartResearchHandler(ctx *wctx.Context, req *StartResearchRequest) (*StartResearchReply, error)
	//取消研究
	CancerResearchHandler(ctx *wctx.Context, req *CancerResearchRequest) (*CancerResearchReply, error)
	//领取主线任务奖励
	CollectMainRewardHandler(ctx *wctx.Context, req *CollectMainRewardRequest) (*CollectMainRewardReply, error)
	//领取地图探索值奖励
	CollectMapChapterRewardHandler(ctx *wctx.Context, req *CollectMapChapterRewardRequest) (*CollectMapChapterRewardReply, error)
	//建筑升级完成
	FinishBuildingHandler(ctx *wctx.Context, req *FinishBuildingRequest) (*FinishBuildingReply, error)
	//研究完成
	FinishResearchHandler(ctx *wctx.Context, req *FinishResearchRequest) (*FinishResearchReply, error)
	//训练完成
	FinishTrainHandler(ctx *wctx.Context, req *FinishTrainRequest) (*FinishTrainReply, error)
	//完成地图事件
	FinishMapEventHandler(ctx *wctx.Context, req *FinishMapEventRequest) (*FinishMapEventReply, error)
	//debug接口
	DebugHandler(ctx *wctx.Context, req *DebugRequest) (*DebugReply, error)
	//玩家回程
	OnBackCityHandler(ctx *wctx.Context, req *OnBackCityRequest) (*OnBackCityReply, error)
	//升级英雄技能
	UpgradeSkillLevelHandler(ctx *wctx.Context, req *UpgradeSkillLevelRequest) (*UpgradeSkillLevelReply, error)
	//升级戴夫等级
	UpgradeDaveLevelHandler(ctx *wctx.Context, req *UpgradeDaveLevelRequest) (*UpgradeDaveLevelReply, error)
	//完成主线关卡 stageId 关卡id ，is_fail 挑战是否失败 ，is_elite 是否精英关卡 perfect_index 1:通关 2:二星通关 3:三星通关, max_hp_percent 0.12, max_time 0-10000
	FinishMainStageHandler(ctx *wctx.Context, req *FinishMainStageRequest) (*FinishMainStageReply, error)
	//领取挂机奖励
	CollectIdleRewardHandler(ctx *wctx.Context, req *CollectIdleRewardRequest) (*CollectIdleRewardReply, error)
	//重置主线关卡
	ResetMainStageHandler(ctx *wctx.Context, req *ResetMainStageRequest) (*ResetMainStageReply, error)
	//击杀怪物
	KillMonsterHandler(ctx *wctx.Context, req *KillMonsterRequest) (*KillMonsterReply, error)
	//开始主线关卡
	StartMainStageHandler(ctx *wctx.Context, req *StartMainStageRequest) (*StartMainStageReply, error)
	//获取挂机奖励信息
	GetIdleRewardHandler(ctx *wctx.Context, req *GetIdleRewardRequest) (*GetIdleRewardReply, error)
	//获取manifest信息
	GetManifestHandler(ctx *wctx.Context, req *GetManifestRequest) (*GetManifestReply, error)
	//上传设备信息
	UploadDeviceInfoHandler(ctx *wctx.Context, req *UploadDeviceInfoRequest) (*UploadDeviceInfoReply, error)
	//局内刷新技能
	RefreshRougeSkillHandler(ctx *wctx.Context, req *RefreshRougeSkillRequest) (*RefreshRougeSkillReply, error)
	//心跳
	HeartBeatHandler(ctx *wctx.Context, req *HeartBeatRequest) (*HeartBeatReply, error)
	//选取局内卡牌
	SelectRougeSkillHandler(ctx *wctx.Context, req *SelectRougeSkillRequest) (*SelectRougeSkillReply, error)
	//领取关卡奖励 1:通关 2:二星通关 3:三星通关
	CollectStageRewardHandler(ctx *wctx.Context, req *CollectStageRewardRequest) (*CollectStageRewardReply, error)
	//获取礼包列表
	GetGiftListHandler(ctx *wctx.Context, req *GetGiftListRequest) (*GetGiftListReply, error)
	//建立订单
	PrepareOrderHandler(ctx *wctx.Context, req *PrepareOrderRequest) (*PrepareOrderReply, error)
	//英雄基因升级
	HeroUpgradeGeneHandler(ctx *wctx.Context, req *HeroUpgradeGeneRequest) (*HeroUpgradeGeneReply, error)
	//选取精英怪肉鸽卡牌
	SelectEliteRougeSkillHandler(ctx *wctx.Context, req *SelectEliteRougeSkillRequest) (*SelectEliteRougeSkillReply, error)
	//获取关卡排行榜信息
	GetStageRankInfoHandler(ctx *wctx.Context, req *GetStageRankInfoRequest) (*GetStageRankInfoReply, error)
	//获取某一关的排行榜信息
	GetStageRankInfoByStageIdHandler(ctx *wctx.Context, req *GetStageRankInfoByStageIdRequest) (*GetStageRankInfoByStageIdReply, error)
	//领取排行榜关卡等级奖励
	CollectStageLevelRewardsHandler(ctx *wctx.Context, req *CollectStageLevelRewardsRequest) (*CollectStageLevelRewardsReply, error)
	//获取光伏发电信息
	GetPhotovoltaicRewardHandler(ctx *wctx.Context, req *GetPhotovoltaicRewardRequest) (*GetPhotovoltaicRewardReply, error)
	//领取光伏发电奖励
	CollectPhotovoltaicRewardHandler(ctx *wctx.Context, req *CollectPhotovoltaicRewardRequest) (*CollectPhotovoltaicRewardReply, error)
	//挂机扫荡
	SweepMainStageHandler(ctx *wctx.Context, req *SweepMainStageRequest) (*SweepMainStageReply, error)
	//获取所有关的排行榜信息
	GetAllStageRankInfoHandler(ctx *wctx.Context, req *GetAllStageRankInfoRequest) (*GetAllStageRankInfoReply, error)
	//用户改名
	ChangeNameHandler(ctx *wctx.Context, req *ChangeNameRequest) (*ChangeNameReply, error)
	//0 改名成功 1 名字重复 2 名字不合法 3 道具不足
	//用户改头像
	ChangeAvatarHandler(ctx *wctx.Context, req *ChangeAvatarRequest) (*ChangeAvatarReply, error)
	//领取怪物图鉴奖励
	CollectMonsterBookRewardHandler(ctx *wctx.Context, req *CollectMonsterBookRewardRequest) (*CollectMonsterBookRewardReply, error)
	// status 0:未解锁 1:已解锁 2:已领取
	//英雄被杀死
	HeroBeKilledHandler(ctx *wctx.Context, req *HeroBeKilledRequest) (*HeroBeKilledReply, error)
	//获取每日任务列表
	GetDailyTaskHandler(ctx *wctx.Context, req *GetDailyTaskRequest) (*GetDailyTaskReply, error)
	//领取每日任务奖励
	CollectDailyTaskRewardHandler(ctx *wctx.Context, req *CollectDailyTaskRewardRequest) (*CollectDailyTaskRewardReply, error)
	//领取每日任务宝箱奖励
	CollectDailyChestRewardHandler(ctx *wctx.Context, req *CollectDailyChestRewardRequest) (*CollectDailyChestRewardReply, error)
	//获取邮件列表
	GetMailListHandler(ctx *wctx.Context, req *GetMailListRequest) (*GetMailListReply, error)
	//获取联盟信息
	GetAllianceInfoHandler(ctx *wctx.Context, req *GetAllianceInfoRequest) (*GetAllianceInfoReply, error)
	//创建联盟
	CreateAllianceHandler(ctx *wctx.Context, req *CreateAllianceRequest) (*CreateAllianceReply, error)
	//recruitSetting 0:不需要申请，1：需要申请
	//申请加入联盟
	ApplyJoinAllianceHandler(ctx *wctx.Context, req *ApplyJoinAllianceRequest) (*ApplyJoinAllianceReply, error)
	//获取联盟成员信息
	GetAllianceMembersInfosHandler(ctx *wctx.Context, req *GetAllianceMembersInfosRequest) (*GetAllianceMembersInfosReply, error)
	//退出联盟
	LeaveAllianceHandler(ctx *wctx.Context, req *LeaveAllianceRequest) (*LeaveAllianceReply, error)
	//修改联盟名称
	EditAllianceNameHandler(ctx *wctx.Context, req *EditAllianceNameRequest) (*EditAllianceNameReply, error)
	//修改联盟简称
	EditAllianceAcronymHandler(ctx *wctx.Context, req *EditAllianceAcronymRequest) (*EditAllianceAcronymReply, error)
	//变更招募信息
	EditRecruitSettingHandler(ctx *wctx.Context, req *EditRecruitSettingRequest) (*EditRecruitSettingReply, error)
	//status 0:不需要申请，1：需要申请
	//变更阶级头衔
	EditAllianceStepNameHandler(ctx *wctx.Context, req *EditAllianceStepNameRequest) (*EditAllianceStepNameReply, error)
	//自定义旗帜
	EditAllianceFlagHandler(ctx *wctx.Context, req *EditAllianceFlagRequest) (*EditAllianceFlagReply, error)
	//转让会长
	TransferPresidentHandler(ctx *wctx.Context, req *TransferPresidentRequest) (*TransferPresidentReply, error)
	//移除成员
	RemoveMemberHandler(ctx *wctx.Context, req *RemoveMemberRequest) (*RemoveMemberReply, error)
	//变更成员阶级
	ChangeMemberStepHandler(ctx *wctx.Context, req *ChangeMemberStepRequest) (*ChangeMemberStepReply, error)
	//获取申请列表
	GetAllianceAppListHandler(ctx *wctx.Context, req *GetAllianceAppListRequest) (*GetAllianceAppListReply, error)
	//处理申请人
	HandleAllianceAppHandler(ctx *wctx.Context, req *HandleAllianceAppRequest) (*HandleAllianceAppReply, error)
	//解散联盟
	DisbandAllianceHandler(ctx *wctx.Context, req *DisbandAllianceRequest) (*DisbandAllianceReply, error)
	//修改联盟公告
	EditAllianceNoticeHandler(ctx *wctx.Context, req *EditAllianceNoticeRequest) (*EditAllianceNoticeReply, error)
	//获取联盟列表
	GetAllianceListHandler(ctx *wctx.Context, req *GetAllianceListRequest) (*GetAllianceListReply, error)
	//撤回联盟申请
	CancerJoinAllianceHandler(ctx *wctx.Context, req *CancerJoinAllianceRequest) (*CancerJoinAllianceReply, error)
	//购买联盟商店物品
	BuyAllianceShopHandler(ctx *wctx.Context, req *BuyAllianceShopRequest) (*BuyAllianceShopReply, error)
	//领取联盟任务奖励
	CollectAllianceTaskRewardHandler(ctx *wctx.Context, req *CollectAllianceTaskRewardRequest) (*CollectAllianceTaskRewardReply, error)
	//领取联盟任务宝箱奖励
	CollectAllianceChestRewardHandler(ctx *wctx.Context, req *CollectAllianceChestRewardRequest) (*CollectAllianceChestRewardReply, error)
	//获取玩家个人信息
	GetUserInfoListHandler(ctx *wctx.Context, req *GetUserInfoListRequest) (*GetUserInfoListReply, error)
	//申请添加好友
	AddFriendsHandler(ctx *wctx.Context, req *AddFriendsRequest) (*AddFriendsReply, error)
	//删除好友
	DelFriendsHandler(ctx *wctx.Context, req *DelFriendsRequest) (*DelFriendsReply, error)
	//获取好友列表
	GetFriendsListHandler(ctx *wctx.Context, req *GetFriendsListRequest) (*GetFriendsListReply, error)
	//加入黑名单
	AddBlackListHandler(ctx *wctx.Context, req *AddBlackListRequest) (*AddBlackListReply, error)
	//移除黑名单
	DelBlackListHandler(ctx *wctx.Context, req *DelBlackListRequest) (*DelBlackListReply, error)
	//获取黑名单列表
	GetBlackListHandler(ctx *wctx.Context, req *GetBlackListRequest) (*GetBlackListReply, error)
	//获取好友推荐列表
	GetFriendRecommendationListHandler(ctx *wctx.Context, req *GetFriendRecommendationListRequest) (*GetFriendRecommendationListReply, error)
	//获取好友申请列表
	GetFriendAppListHandler(ctx *wctx.Context, req *GetFriendAppListRequest) (*GetFriendAppListReply, error)
	//处理好友申请
	HandleFriendAppHandler(ctx *wctx.Context, req *HandleFriendAppRequest) (*HandleFriendAppReply, error)
	//设置加好友条件
	SettingAddFriendConditionHandler(ctx *wctx.Context, req *SettingAddFriendConditionRequest) (*SettingAddFriendConditionReply, error)
	//频道发言
	HookSendMessageHandler(ctx *wctx.Context, req *HookSendMessageRequest) (*HookSendMessageReply, error)
	//type =1 好友 ，2 联盟 ，3 世界
	//测试礼包
	SubmitOrderHandler(ctx *wctx.Context, req *SubmitOrderRequest) (*SubmitOrderReply, error)
	//orderId 为 test
	//获取力量排行榜信息
	GetPowerRankInfoHandler(ctx *wctx.Context, req *GetPowerRankInfoRequest) (*GetPowerRankInfoReply, error)
	//升级领主装备
	UpgradeLordEquipLevelHandler(ctx *wctx.Context, req *UpgradeLordEquipLevelRequest) (*UpgradeLordEquipLevelReply, error)
	//合成宝石
	GemCraftHandler(ctx *wctx.Context, req *GemCraftRequest) (*GemCraftReply, error)
	//洗炼宝石
	EnhanceGemHandler(ctx *wctx.Context, req *EnhanceGemRequest) (*EnhanceGemReply, error)
	//装备宝石
	EquipGemHandler(ctx *wctx.Context, req *EquipGemRequest) (*EquipGemReply, error)
	//卸下宝石
	UnEquipGemHandler(ctx *wctx.Context, req *UnEquipGemRequest) (*UnEquipGemReply, error)
	//装备升阶
	UpgradeLordEquipGradeHandler(ctx *wctx.Context, req *UpgradeLordEquipGradeRequest) (*UpgradeLordEquipGradeReply, error)
	//锁定宝石
	LockGemHandler(ctx *wctx.Context, req *LockGemRequest) (*LockGemReply, error)
	//解锁宝石
	UnlockGemHandler(ctx *wctx.Context, req *UnlockGemRequest) (*UnlockGemReply, error)
	//切换宝石
	SwitchEquipGemHandler(ctx *wctx.Context, req *SwitchEquipGemRequest) (*SwitchEquipGemReply, error)
	//宝石随机
	LordGemRandomHandler(ctx *wctx.Context, req *LordGemRandomRequest) (*LordGemRandomReply, error)
	//英雄升品
	HeroUpgradeQualityHandler(ctx *wctx.Context, req *HeroUpgradeQualityRequest) (*HeroUpgradeQualityReply, error)
	//副本扫荡
	SweepDungeonHandler(ctx *wctx.Context, req *SweepDungeonRequest) (*SweepDungeonReply, error)
	//副本选择肉鸽卡牌
	SelectDungeonRougeTabHandler(ctx *wctx.Context, req *SelectDungeonRougeTabRequest) (*SelectDungeonRougeTabReply, error)
	//开始副本
	StartDungeonHandler(ctx *wctx.Context, req *StartDungeonRequest) (*StartDungeonReply, error)
	//副本肉鸽卡牌刷新
	RefreshDungeonRougeTabHandler(ctx *wctx.Context, req *RefreshDungeonRougeTabRequest) (*RefreshDungeonRougeTabReply, error)
	//保存功能开放
	SaveFunctionOpenHandler(ctx *wctx.Context, req *SaveFunctionOpenRequest) (*SaveFunctionOpenReply, error)
	//保存新手引导进度
	SaveGuideProgressHandler(ctx *wctx.Context, req *SaveGuideProgressRequest) (*SaveGuideProgressReply, error)
	//获取活动信息
	GetActivityListHandler(ctx *wctx.Context, req *GetActivityListRequest) (*GetActivityListReply, error)
	//领取七日签到奖励
	CollectSign7RewardHandler(ctx *wctx.Context, req *CollectSign7RewardRequest) (*CollectSign7RewardReply, error)
	//领取 7 日任务活动奖励
	CollectDay7RewardHandler(ctx *wctx.Context, req *CollectDay7RewardRequest) (*CollectDay7RewardReply, error)
	//领取 7 日任务宝箱奖励
	CollectDay7ChestRewardHandler(ctx *wctx.Context, req *CollectDay7ChestRewardRequest) (*CollectDay7ChestRewardReply, error)
	//选择 7 日任务奖励
	SelectDay7RewardHandler(ctx *wctx.Context, req *SelectDay7RewardRequest) (*SelectDay7RewardReply, error)
	//领取邮件奖励
	CollectMailRewardHandler(ctx *wctx.Context, req *CollectMailRewardRequest) (*CollectMailRewardReply, error)
	//阅读邮件
	ReadMailHandler(ctx *wctx.Context, req *ReadMailRequest) (*ReadMailReply, error)
	//删除邮件
	DelMailHandler(ctx *wctx.Context, req *DelMailRequest) (*DelMailReply, error)
	DeleteAllReadMailHandler(ctx *wctx.Context, req *DeleteAllReadMailRequest) (*DeleteAllReadMailReply, error)
	//删除所有已读邮件
	ReadAndCollectAllMailHandler(ctx *wctx.Context, req *ReadAndCollectAllMailRequest) (*ReadAndCollectAllMailReply, error)
	//一键阅读并领取邮件
	//领取成长基金奖励
	CollectGrowthFundRewardHandler(ctx *wctx.Context, req *CollectGrowthFundRewardRequest) (*CollectGrowthFundRewardReply, error)
	//领取首充奖励
	GetFirstChargeRewardHandler(ctx *wctx.Context, req *GetFirstChargeRewardRequest) (*GetFirstChargeRewardReply, error)
	//领取月卡奖励
	ReceiveMonthCardRewardHandler(ctx *wctx.Context, req *ReceiveMonthCardRewardRequest) (*ReceiveMonthCardRewardReply, error)
	//领取每日特惠免费礼包
	CollectDailySaleFreeRewardHandler(ctx *wctx.Context, req *CollectDailySaleFreeRewardRequest) (*CollectDailySaleFreeRewardReply, error)
	//领取每日任务周宝箱奖励
	CollectDailyWeekTaskChestHandler(ctx *wctx.Context, req *CollectDailyWeekTaskChestRequest) (*CollectDailyWeekTaskChestReply, error)
	//初始化共生系统
	InitSymbioticHandler(ctx *wctx.Context, req *InitSymbioticRequest) (*InitSymbioticReply, error)
	//切换共生英雄
	SwitchSymbioticHeroHandler(ctx *wctx.Context, req *SwitchSymbioticHeroRequest) (*SwitchSymbioticHeroReply, error)
	//领取成就任务奖励
	CollectAchievementTaskRewardHandler(ctx *wctx.Context, req *CollectAchievementTaskRewardRequest) (*CollectAchievementTaskRewardReply, error)
	//一键升级英雄
	OneKeyUpgradeHeroHandler(ctx *wctx.Context, req *OneKeyUpgradeHeroRequest) (*OneKeyUpgradeHeroReply, error)
	//一键装备宝石
	OneKeyEquipGemHandler(ctx *wctx.Context, req *OneKeyEquipGemRequest) (*OneKeyEquipGemReply, error)
	//领取章节总奖励
	CollectGroupRewardHandler(ctx *wctx.Context, req *CollectGroupRewardRequest) (*CollectGroupRewardReply, error)
}

func GetRpcHandler(rpc int32) invoker {
	if handler, ok := invokerMap[rpc]; ok {
		return handler
	}
	return nil
}

func NewWrpcRequest(wrpcID int32) interface{} {
	switch wrpcID {
	case 4:
		return &SimpleErrorNtf{}
	case 9:
		return &MulticastErrorNtf{}
	case 104:
		return &UserOfflineNtf{}
	case 501:
		return &RequestDispatcherRequest{}
	case 1001:
		return &RegisterUserRequest{}
	case 1002:
		return &LoginRequest{}
	case 1003:
		return &HelloWorldRequest{}
	case 1004:
		return &VerifyUserRequest{}
	case 1005:
		return &AddBuildingRequest{}
	case 1006:
		return &UpgradeBuildingRequest{}
	case 1007:
		return &HeroLotteryRequest{}
	case 1008:
		return &HeroUpgradeLevelRequest{}
	case 1009:
		return &HeroUpgradeStarRequest{}
	case 1010:
		return &DebugAddItemRequest{}
	case 1011:
		return &BuildWorkHeroRequest{}
	case 1012:
		return &CollectResourceRequest{}
	case 1013:
		return &SetHeroBattlePosRequest{}
	case 1014:
		return &SetHeroTroopRequest{}
	case 1015:
		return &SetDefaultBattlePosRequest{}
	case 1016:
		return &CollectHeroLotteryAccRewardRequest{}
	case 1017:
		return &CollectHeroLotteryLevelRewardRequest{}
	case 1018:
		return &RandomHeroDiceRequest{}
	case 1019:
		return &LockHeroDiceRequest{}
	case 1020:
		return &TrainTroopsRequest{}
	case 1021:
		return &UserItemRequest{}
	case 1022:
		return &BuildingWorkVillageRequest{}
	case 1023:
		return &FinishDungeonStageRequest{}
	case 1024:
		return &CollectFirstPassRewardRequest{}
	case 1025:
		return &StartResearchRequest{}
	case 1026:
		return &CancerResearchRequest{}
	case 1028:
		return &CollectMainRewardRequest{}
	case 1029:
		return &CollectMapChapterRewardRequest{}
	case 1030:
		return &FinishBuildingRequest{}
	case 1031:
		return &FinishResearchRequest{}
	case 1032:
		return &FinishTrainRequest{}
	case 1033:
		return &FinishMapEventRequest{}
	case 1034:
		return &DebugRequest{}
	case 1035:
		return &OnBackCityRequest{}
	case 1036:
		return &UpgradeSkillLevelRequest{}
	case 1037:
		return &UpgradeDaveLevelRequest{}
	case 1038:
		return &FinishMainStageRequest{}
	case 1039:
		return &CollectIdleRewardRequest{}
	case 1040:
		return &ResetMainStageRequest{}
	case 1041:
		return &KillMonsterRequest{}
	case 1042:
		return &StartMainStageRequest{}
	case 1043:
		return &GetIdleRewardRequest{}
	case 1044:
		return &GetManifestRequest{}
	case 1045:
		return &UploadDeviceInfoRequest{}
	case 1046:
		return &RefreshRougeSkillRequest{}
	case 1047:
		return &HeartBeatRequest{}
	case 1048:
		return &SelectRougeSkillRequest{}
	case 1049:
		return &CollectStageRewardRequest{}
	case 1050:
		return &GetGiftListRequest{}
	case 1051:
		return &PrepareOrderRequest{}
	case 1052:
		return &HeroUpgradeGeneRequest{}
	case 1053:
		return &SelectEliteRougeSkillRequest{}
	case 1054:
		return &GetStageRankInfoRequest{}
	case 1055:
		return &GetStageRankInfoByStageIdRequest{}
	case 1056:
		return &CollectStageLevelRewardsRequest{}
	case 1057:
		return &GetPhotovoltaicRewardRequest{}
	case 1058:
		return &CollectPhotovoltaicRewardRequest{}
	case 1059:
		return &SweepMainStageRequest{}
	case 1060:
		return &GetAllStageRankInfoRequest{}
	case 1061:
		return &ChangeNameRequest{}
	case 1062:
		return &ChangeAvatarRequest{}
	case 1063:
		return &CollectMonsterBookRewardRequest{}
	case 1064:
		return &HeroBeKilledRequest{}
	case 1065:
		return &GetDailyTaskRequest{}
	case 1066:
		return &CollectDailyTaskRewardRequest{}
	case 1067:
		return &CollectDailyChestRewardRequest{}
	case 1068:
		return &GetMailListRequest{}
	case 1069:
		return &GetAllianceInfoRequest{}
	case 1070:
		return &CreateAllianceRequest{}
	case 1071:
		return &ApplyJoinAllianceRequest{}
	case 1072:
		return &GetAllianceMembersInfosRequest{}
	case 1073:
		return &LeaveAllianceRequest{}
	case 1074:
		return &EditAllianceNameRequest{}
	case 1075:
		return &EditAllianceAcronymRequest{}
	case 1076:
		return &EditRecruitSettingRequest{}
	case 1077:
		return &EditAllianceStepNameRequest{}
	case 1078:
		return &EditAllianceFlagRequest{}
	case 1079:
		return &TransferPresidentRequest{}
	case 1080:
		return &RemoveMemberRequest{}
	case 1081:
		return &ChangeMemberStepRequest{}
	case 1082:
		return &GetAllianceAppListRequest{}
	case 1083:
		return &HandleAllianceAppRequest{}
	case 1084:
		return &DisbandAllianceRequest{}
	case 1085:
		return &EditAllianceNoticeRequest{}
	case 1086:
		return &GetAllianceListRequest{}
	case 1087:
		return &CancerJoinAllianceRequest{}
	case 1088:
		return &BuyAllianceShopRequest{}
	case 1089:
		return &CollectAllianceTaskRewardRequest{}
	case 1090:
		return &CollectAllianceChestRewardRequest{}
	case 1091:
		return &GetUserInfoListRequest{}
	case 1092:
		return &AddFriendsRequest{}
	case 1093:
		return &DelFriendsRequest{}
	case 1094:
		return &GetFriendsListRequest{}
	case 1095:
		return &AddBlackListRequest{}
	case 1096:
		return &DelBlackListRequest{}
	case 1097:
		return &GetBlackListRequest{}
	case 1098:
		return &GetFriendRecommendationListRequest{}
	case 1099:
		return &GetFriendAppListRequest{}
	case 1100:
		return &HandleFriendAppRequest{}
	case 1101:
		return &SettingAddFriendConditionRequest{}
	case 1102:
		return &HookSendMessageRequest{}
	case 1103:
		return &SubmitOrderRequest{}
	case 1104:
		return &GetPowerRankInfoRequest{}
	case 1105:
		return &UpgradeLordEquipLevelRequest{}
	case 1106:
		return &GemCraftRequest{}
	case 1107:
		return &EnhanceGemRequest{}
	case 1108:
		return &EquipGemRequest{}
	case 1109:
		return &UnEquipGemRequest{}
	case 1110:
		return &UpgradeLordEquipGradeRequest{}
	case 1111:
		return &LockGemRequest{}
	case 1112:
		return &UnlockGemRequest{}
	case 1113:
		return &SwitchEquipGemRequest{}
	case 1114:
		return &LordGemRandomRequest{}
	case 1115:
		return &HeroUpgradeQualityRequest{}
	case 1116:
		return &SweepDungeonRequest{}
	case 1117:
		return &SelectDungeonRougeTabRequest{}
	case 1118:
		return &StartDungeonRequest{}
	case 1119:
		return &RefreshDungeonRougeTabRequest{}
	case 1120:
		return &SaveFunctionOpenRequest{}
	case 1121:
		return &SaveGuideProgressRequest{}
	case 1122:
		return &GetActivityListRequest{}
	case 1123:
		return &CollectSign7RewardRequest{}
	case 1124:
		return &CollectDay7RewardRequest{}
	case 1125:
		return &CollectDay7ChestRewardRequest{}
	case 1126:
		return &SelectDay7RewardRequest{}
	case 1127:
		return &CollectMailRewardRequest{}
	case 1128:
		return &ReadMailRequest{}
	case 1129:
		return &DelMailRequest{}
	case 1130:
		return &DeleteAllReadMailRequest{}
	case 1131:
		return &ReadAndCollectAllMailRequest{}
	case 1132:
		return &CollectGrowthFundRewardRequest{}
	case 1133:
		return &GetFirstChargeRewardRequest{}
	case 1134:
		return &ReceiveMonthCardRewardRequest{}
	case 1135:
		return &CollectDailySaleFreeRewardRequest{}
	case 1136:
		return &CollectDailyWeekTaskChestRequest{}
	case 1137:
		return &InitSymbioticRequest{}
	case 1138:
		return &SwitchSymbioticHeroRequest{}
	case 1139:
		return &CollectAchievementTaskRewardRequest{}
	case 1140:
		return &OneKeyUpgradeHeroRequest{}
	case 1141:
		return &OneKeyEquipGemRequest{}
	case 1142:
		return &CollectGroupRewardRequest{}
	}

	return nil
}

const (
	rpc_name = "a_rpc_name" // rpc字段
)

var (
	count           *prometheus.CounterVec   // rpc调用次数(包含错误次数)
	errorCount      *prometheus.CounterVec   // rpc handler返回error次数
	duration        *prometheus.HistogramVec // rpc handle消耗时间
	prometheus_flag bool
)

// InitRpc 初始化
func InitRpc(reg prometheus.Registerer) {
	count = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "rpc_count",
		Help: "rpc次数",
	}, []string{rpc_name})
	errorCount = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "rpc_error_count",
		Help: "rpc返回错误次数",
	}, []string{rpc_name})
	duration = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "rpc_duration_ms",
		Help:    "rpc handle 时间(ms)",
		Buckets: []float64{50, 100, 200, 500, 1000},
	}, []string{rpc_name})
	reg.MustRegister(count, errorCount, duration)
	prometheus_flag = true
}

// OnRPCInvoke 当rpc调用打点
func OnRPCInvoke(msgID int32, elapse float64, err error) {
	name, ok := ID2Name[msgID]
	if !ok {
		name = strconv.Itoa((int)(msgID))
	}
	count.WithLabelValues(name).Inc()
	duration.WithLabelValues(name).Observe(elapse)
	if err != nil {
		errorCount.WithLabelValues(name).Inc()
	}
}

func BlendHandlerMap(handlers WrpcHandlers) map[int32]wnet.MsgHandler {
	m := make(map[int32]wnet.MsgHandler)
	for k, v := range invokerMap {
		k, invoke := k, v
		unmarshal := unmarshalerMap[k]
		m[k] = func(conn wnet.Conn, msg wnet.Message) error {
			var (
				msgID     = msg.Header().MsgID()
				start     = time.Now()
				handleErr error
			)
			if prometheus_flag {
				defer func() {
					elapse := time.Now().Sub(start)
					OnRPCInvoke(msgID, float64(elapse.Milliseconds()), handleErr)
				}()
			}

			hdr := msg.Header()
			hdr.ClearFlagRecycle()
			defer msg.Recycle()
			var bizCtx wctx.BizContext
			req, err := unmarshal(&bizCtx, conn, msg)
			handleErr = err
			if err != nil {
				return err
			}
			ctx := wctx.NewContextFromPool(nil)
			defer wctx.RecycleContext(ctx)
			ctx.Context = xctx.BG
			ctx.BizContext = bizCtx
			ctx.ReqMessage = msg
			ctx.ReqConn = conn
			r, err := handlers.Decorate(ctx, hdr, func(ctx *wctx.Context) (interface{}, error) {
				return invoke(ctx, req, handlers)
			})
			handleErr = err
			if err != nil {
				return err
			}
			if hdr.FlagRequest() {
				return replyImpl(conn, msg, r)
			} else {
				return nil
			}
		}
	}

	return m
}

func replyImpl(conn wnet.Conn, msg wnet.Message, r interface{}) error {
	hdr := msg.Header()
	var flags = wnet.SMF_Recycle
	if hdr.FlagInterBackend() {
		flags = flags | wnet.SMF_InterBackend
	}
	reply, err := wnet.BuildMessage(conn, hdr.MsgID(), wnet.CMF_Reply, flags, hdr.ExtendCodecID(), r,
		wnet.ExtractReplyFactors(msg))
	if err != nil {
		return err
	}
	if hdr.FlagInterBackend() {
		reply.Header().N64 = hdr.N64
	}
	conn.Reply(reply, msg)
	return nil
}

func NewStaticDispatcherForStandaloneServer(d server.ProcDispatcher, handlers WrpcHandlers) server.Option {
	m := BlendHandlerMap(handlers)
	return server.WithMsgDispatcher(d, func() map[int32]wnet.MsgHandler {
		return m
	})
}

func NewDispatcherForStandaloneServer(d server.ProcDispatcher, f func() map[int32]wnet.MsgHandler) server.Option {
	return server.WithMsgDispatcher(d, f)
}

func NewStaticDispatcherForAppBackend(d server.ProcDispatcher, handlers WrpcHandlers) client.Option {
	m := BlendHandlerMap(handlers)
	return client.WithMsgDispatcher(d, func() map[int32]wnet.MsgHandler {
		return m
	})
}

func NewDispatcherForAppBackend(d server.ProcDispatcher, f func() map[int32]wnet.MsgHandler) client.Option {
	return client.WithMsgDispatcher(d, f)
}

type Notifier struct {
	conns    []wnet.Conn
	messages []wnet.Message
}

func (this *Notifier) AddNtf(conn wnet.Conn, msg wnet.Message) {
	this.conns = append(this.conns, conn)
	this.messages = append(this.messages, msg)
}

func (this *Notifier) Len() int {
	return len(this.messages)
}

func (this *Notifier) Push() {
	var err error
	n := len(this.messages)
	for i := 0; i < n; i++ {
		err = this.conns[i].SendMessage(this.messages[i])
		if err != nil {
			wlog.Error(err)
		}
	}
}

type Pusher struct {
	handlers WrpcHandlers
	paramX   interface{}
}

func NewPusher(handlers WrpcHandlers, paramX interface{}) Pusher {
	return Pusher{
		handlers: handlers,
		paramX:   paramX,
	}
}

type ConnPusher struct {
	handlers WrpcHandlers
	paramX   interface{}

	userConnSource func() []olcache.UserConn
}

func NewConnPusher(handlers WrpcHandlers, paramX interface{}, userConnSource func() []olcache.UserConn) ConnPusher {
	return ConnPusher{
		handlers:       handlers,
		paramX:         paramX,
		userConnSource: userConnSource,
	}
}

type BroadcastHelper struct {
	stargateMap *gkit.StargateMap
}

func NewBroadcastHelper(stargateMap *gkit.StargateMap) BroadcastHelper {
	return BroadcastHelper{
		stargateMap: stargateMap,
	}
}

func NewPushTopic(topicType uuid.IDType, id uuid.ID) string {
	return id.ChangeType(topicType).SubjectWithPre(uuid.GatePrefix)
}

type DeprecatedTopicPusher struct {
	stargateMap *gkit.StargateMap
}

func DeprecatedNewTopicPusher(stargateMap *gkit.StargateMap) DeprecatedTopicPusher {
	return DeprecatedTopicPusher{
		stargateMap: stargateMap,
	}
}

func reportExecutionTime(msg string, stub *wnet.Stub, err error) {
	if err == nil {
		wlog.DefaultPlainLogger().Info(msg,
			zap.Int64("timeCost", int64(time.Since(stub.StartTime)/time.Microsecond)),
			zap.NamedError("err", err),
		)
		return
	}

	wlog.DefaultPlainLogger().Error(msg,
		zap.Int64("timeCost", int64(time.Since(stub.StartTime)/time.Microsecond)),
		zap.NamedError("err", err),
	)
}

type Remote struct {
	conn wnet.Conn
}

func NewRemote(conn wnet.Conn) Remote {
	return Remote{conn: conn}
}

type (
	remoteAsync Remote
	remoteNtf   Remote
)

func (r Remote) WithAsync() remoteAsync {
	return remoteAsync(r)
}

func (r Remote) WithReplyDiscarded() remoteNtf {
	return remoteNtf(r)
}

type reqNtfMarshaler interface {
	Marshal() ([]byte, error)
	//MarshalJSON() ([]byte, error)
}

func marshalReqNtf(obj reqNtfMarshaler, preferred enum.Codec) (enum.Codec, []byte, error) {
	cdc := defaultMessageEncoding
	if preferred != enum.Codec_Autofit {
		cdc = preferred
	}
	switch cdc {
	case enum.Codec_Protobuf:
		d, err := obj.Marshal()
		return cdc, d, err
	case enum.Codec_JSON:
		//d, err := obj.MarshalJSON()
		d, err := json.Marshal(obj)
		return cdc, d, err
	default:
		panic("impossible")
	}
}

func digCtx(ctx interface{}) (context.Context, int64) {
	switch x := ctx.(type) {
	case *wctx.Context:
		return x.Context, x.TraceID
	case *wctx.ThreadContext:
		return x.Context, x.TraceID
	case *wctx.BizContext:
		return nil, x.TraceID
	case context.Context:
		return x, 0
	case nil:
		return nil, 0
	default:
		panic("unexpected context type: " + reflect.TypeOf(ctx).Name())
	}
}

var (
	HandlerExecutionTime = handlerExecutionTime{
		SimpleError:                   false,
		MulticastError:                false,
		UserOffline:                   false,
		RequestDispatcher:             true,
		RegisterUser:                  true,
		Login:                         true,
		HelloWorld:                    true,
		VerifyUser:                    true,
		AddBuilding:                   true,
		UpgradeBuilding:               true,
		HeroLottery:                   true,
		HeroUpgradeLevel:              true,
		HeroUpgradeStar:               true,
		DebugAddItem:                  true,
		BuildWorkHero:                 true,
		CollectResource:               true,
		SetHeroBattlePos:              true,
		SetHeroTroop:                  true,
		SetDefaultBattlePos:           true,
		CollectHeroLotteryAccReward:   true,
		CollectHeroLotteryLevelReward: true,
		RandomHeroDice:                true,
		LockHeroDice:                  true,
		TrainTroops:                   true,
		UserItem:                      true,
		BuildingWorkVillage:           true,
		FinishDungeonStage:            true,
		CollectFirstPassReward:        true,
		StartResearch:                 true,
		CancerResearch:                true,
		CollectMainReward:             true,
		CollectMapChapterReward:       true,
		FinishBuilding:                true,
		FinishResearch:                true,
		FinishTrain:                   true,
		FinishMapEvent:                true,
		Debug:                         true,
		OnBackCity:                    true,
		UpgradeSkillLevel:             true,
		UpgradeDaveLevel:              true,
		FinishMainStage:               true,
		CollectIdleReward:             true,
		ResetMainStage:                true,
		KillMonster:                   true,
		StartMainStage:                true,
		GetIdleReward:                 true,
		GetManifest:                   true,
		UploadDeviceInfo:              true,
		RefreshRougeSkill:             true,
		HeartBeat:                     true,
		SelectRougeSkill:              true,
		CollectStageReward:            true,
		GetGiftList:                   true,
		PrepareOrder:                  true,
		HeroUpgradeGene:               true,
		SelectEliteRougeSkill:         true,
		GetStageRankInfo:              true,
		GetStageRankInfoByStageId:     true,
		CollectStageLevelRewards:      true,
		GetPhotovoltaicReward:         true,
		CollectPhotovoltaicReward:     true,
		SweepMainStage:                true,
		GetAllStageRankInfo:           true,
		ChangeName:                    true,
		ChangeAvatar:                  true,
		CollectMonsterBookReward:      true,
		HeroBeKilled:                  true,
		GetDailyTask:                  true,
		CollectDailyTaskReward:        true,
		CollectDailyChestReward:       true,
		GetMailList:                   true,
		GetAllianceInfo:               true,
		CreateAlliance:                true,
		ApplyJoinAlliance:             true,
		GetAllianceMembersInfos:       true,
		LeaveAlliance:                 true,
		EditAllianceName:              true,
		EditAllianceAcronym:           true,
		EditRecruitSetting:            true,
		EditAllianceStepName:          true,
		EditAllianceFlag:              true,
		TransferPresident:             true,
		RemoveMember:                  true,
		ChangeMemberStep:              true,
		GetAllianceAppList:            true,
		HandleAllianceApp:             true,
		DisbandAlliance:               true,
		EditAllianceNotice:            true,
		GetAllianceList:               true,
		CancerJoinAlliance:            true,
		BuyAllianceShop:               true,
		CollectAllianceTaskReward:     true,
		CollectAllianceChestReward:    true,
		GetUserInfoList:               true,
		AddFriends:                    true,
		DelFriends:                    true,
		GetFriendsList:                true,
		AddBlackList:                  true,
		DelBlackList:                  true,
		GetBlackList:                  true,
		GetFriendRecommendationList:   true,
		GetFriendAppList:              true,
		HandleFriendApp:               true,
		SettingAddFriendCondition:     true,
		HookSendMessage:               true,
		SubmitOrder:                   true,
		GetPowerRankInfo:              true,
		UpgradeLordEquipLevel:         true,
		GemCraft:                      true,
		EnhanceGem:                    true,
		EquipGem:                      true,
		UnEquipGem:                    true,
		UpgradeLordEquipGrade:         true,
		LockGem:                       true,
		UnlockGem:                     true,
		SwitchEquipGem:                true,
		LordGemRandom:                 true,
		HeroUpgradeQuality:            true,
		SweepDungeon:                  true,
		SelectDungeonRougeTab:         true,
		StartDungeon:                  true,
		RefreshDungeonRougeTab:        true,
		SaveFunctionOpen:              true,
		SaveGuideProgress:             true,
		GetActivityList:               true,
		CollectSign7Reward:            true,
		CollectDay7Reward:             true,
		CollectDay7ChestReward:        true,
		SelectDay7Reward:              true,
		CollectMailReward:             true,
		ReadMail:                      true,
		DelMail:                       true,
		DeleteAllReadMail:             true,
		ReadAndCollectAllMail:         true,
		CollectGrowthFundReward:       true,
		GetFirstChargeReward:          true,
		ReceiveMonthCardReward:        true,
		CollectDailySaleFreeReward:    true,
		CollectDailyWeekTaskChest:     true,
		InitSymbiotic:                 true,
		SwitchSymbioticHero:           true,
		CollectAchievementTaskReward:  true,
		OneKeyUpgradeHero:             true,
		OneKeyEquipGem:                true,
		CollectGroupReward:            true,
	}
)

type handlerExecutionTime struct {
	SimpleError                   bool
	MulticastError                bool
	UserOffline                   bool
	RequestDispatcher             bool
	RegisterUser                  bool
	Login                         bool
	HelloWorld                    bool
	VerifyUser                    bool
	AddBuilding                   bool
	UpgradeBuilding               bool
	HeroLottery                   bool
	HeroUpgradeLevel              bool
	HeroUpgradeStar               bool
	DebugAddItem                  bool
	BuildWorkHero                 bool
	CollectResource               bool
	SetHeroBattlePos              bool
	SetHeroTroop                  bool
	SetDefaultBattlePos           bool
	CollectHeroLotteryAccReward   bool
	CollectHeroLotteryLevelReward bool
	RandomHeroDice                bool
	LockHeroDice                  bool
	TrainTroops                   bool
	UserItem                      bool
	BuildingWorkVillage           bool
	FinishDungeonStage            bool
	CollectFirstPassReward        bool
	StartResearch                 bool
	CancerResearch                bool
	CollectMainReward             bool
	CollectMapChapterReward       bool
	FinishBuilding                bool
	FinishResearch                bool
	FinishTrain                   bool
	FinishMapEvent                bool
	Debug                         bool
	OnBackCity                    bool
	UpgradeSkillLevel             bool
	UpgradeDaveLevel              bool
	FinishMainStage               bool
	CollectIdleReward             bool
	ResetMainStage                bool
	KillMonster                   bool
	StartMainStage                bool
	GetIdleReward                 bool
	GetManifest                   bool
	UploadDeviceInfo              bool
	RefreshRougeSkill             bool
	HeartBeat                     bool
	SelectRougeSkill              bool
	CollectStageReward            bool
	GetGiftList                   bool
	PrepareOrder                  bool
	HeroUpgradeGene               bool
	SelectEliteRougeSkill         bool
	GetStageRankInfo              bool
	GetStageRankInfoByStageId     bool
	CollectStageLevelRewards      bool
	GetPhotovoltaicReward         bool
	CollectPhotovoltaicReward     bool
	SweepMainStage                bool
	GetAllStageRankInfo           bool
	ChangeName                    bool
	ChangeAvatar                  bool
	CollectMonsterBookReward      bool
	HeroBeKilled                  bool
	GetDailyTask                  bool
	CollectDailyTaskReward        bool
	CollectDailyChestReward       bool
	GetMailList                   bool
	GetAllianceInfo               bool
	CreateAlliance                bool
	ApplyJoinAlliance             bool
	GetAllianceMembersInfos       bool
	LeaveAlliance                 bool
	EditAllianceName              bool
	EditAllianceAcronym           bool
	EditRecruitSetting            bool
	EditAllianceStepName          bool
	EditAllianceFlag              bool
	TransferPresident             bool
	RemoveMember                  bool
	ChangeMemberStep              bool
	GetAllianceAppList            bool
	HandleAllianceApp             bool
	DisbandAlliance               bool
	EditAllianceNotice            bool
	GetAllianceList               bool
	CancerJoinAlliance            bool
	BuyAllianceShop               bool
	CollectAllianceTaskReward     bool
	CollectAllianceChestReward    bool
	GetUserInfoList               bool
	AddFriends                    bool
	DelFriends                    bool
	GetFriendsList                bool
	AddBlackList                  bool
	DelBlackList                  bool
	GetBlackList                  bool
	GetFriendRecommendationList   bool
	GetFriendAppList              bool
	HandleFriendApp               bool
	SettingAddFriendCondition     bool
	HookSendMessage               bool
	SubmitOrder                   bool
	GetPowerRankInfo              bool
	UpgradeLordEquipLevel         bool
	GemCraft                      bool
	EnhanceGem                    bool
	EquipGem                      bool
	UnEquipGem                    bool
	UpgradeLordEquipGrade         bool
	LockGem                       bool
	UnlockGem                     bool
	SwitchEquipGem                bool
	LordGemRandom                 bool
	HeroUpgradeQuality            bool
	SweepDungeon                  bool
	SelectDungeonRougeTab         bool
	StartDungeon                  bool
	RefreshDungeonRougeTab        bool
	SaveFunctionOpen              bool
	SaveGuideProgress             bool
	GetActivityList               bool
	CollectSign7Reward            bool
	CollectDay7Reward             bool
	CollectDay7ChestReward        bool
	SelectDay7Reward              bool
	CollectMailReward             bool
	ReadMail                      bool
	DelMail                       bool
	DeleteAllReadMail             bool
	ReadAndCollectAllMail         bool
	CollectGrowthFundReward       bool
	GetFirstChargeReward          bool
	ReceiveMonthCardReward        bool
	CollectDailySaleFreeReward    bool
	CollectDailyWeekTaskChest     bool
	InitSymbiotic                 bool
	SwitchSymbioticHero           bool
	CollectAchievementTaskReward  bool
	OneKeyUpgradeHero             bool
	OneKeyEquipGem                bool
	CollectGroupReward            bool
}

var (
	ExecutionTime = rpcExecutionTime{
		UserOnline:                    true,
		UpdateUserTopics:              true,
		KickOut:                       true,
		WantUserOfflineNtf:            true,
		UpdateUserConnWarehouse:       true,
		Echo:                          true,
		PerformUserConnHealthcheck:    true,
		RequestDispatcher:             true,
		RegisterUser:                  true,
		Login:                         true,
		HelloWorld:                    true,
		VerifyUser:                    true,
		AddBuilding:                   true,
		UpgradeBuilding:               true,
		HeroLottery:                   true,
		HeroUpgradeLevel:              true,
		HeroUpgradeStar:               true,
		DebugAddItem:                  true,
		BuildWorkHero:                 true,
		CollectResource:               true,
		SetHeroBattlePos:              true,
		SetHeroTroop:                  true,
		SetDefaultBattlePos:           true,
		CollectHeroLotteryAccReward:   true,
		CollectHeroLotteryLevelReward: true,
		RandomHeroDice:                true,
		LockHeroDice:                  true,
		TrainTroops:                   true,
		UserItem:                      true,
		BuildingWorkVillage:           true,
		FinishDungeonStage:            true,
		CollectFirstPassReward:        true,
		StartResearch:                 true,
		CancerResearch:                true,
		CollectMainReward:             true,
		CollectMapChapterReward:       true,
		FinishBuilding:                true,
		FinishResearch:                true,
		FinishTrain:                   true,
		FinishMapEvent:                true,
		Debug:                         true,
		OnBackCity:                    true,
		UpgradeSkillLevel:             true,
		UpgradeDaveLevel:              true,
		FinishMainStage:               true,
		CollectIdleReward:             true,
		ResetMainStage:                true,
		KillMonster:                   true,
		StartMainStage:                true,
		GetIdleReward:                 true,
		GetManifest:                   true,
		UploadDeviceInfo:              true,
		RefreshRougeSkill:             true,
		HeartBeat:                     true,
		SelectRougeSkill:              true,
		CollectStageReward:            true,
		GetGiftList:                   true,
		PrepareOrder:                  true,
		HeroUpgradeGene:               true,
		SelectEliteRougeSkill:         true,
		GetStageRankInfo:              true,
		GetStageRankInfoByStageId:     true,
		CollectStageLevelRewards:      true,
		GetPhotovoltaicReward:         true,
		CollectPhotovoltaicReward:     true,
		SweepMainStage:                true,
		GetAllStageRankInfo:           true,
		ChangeName:                    true,
		ChangeAvatar:                  true,
		CollectMonsterBookReward:      true,
		HeroBeKilled:                  true,
		GetDailyTask:                  true,
		CollectDailyTaskReward:        true,
		CollectDailyChestReward:       true,
		GetMailList:                   true,
		GetAllianceInfo:               true,
		CreateAlliance:                true,
		ApplyJoinAlliance:             true,
		GetAllianceMembersInfos:       true,
		LeaveAlliance:                 true,
		EditAllianceName:              true,
		EditAllianceAcronym:           true,
		EditRecruitSetting:            true,
		EditAllianceStepName:          true,
		EditAllianceFlag:              true,
		TransferPresident:             true,
		RemoveMember:                  true,
		ChangeMemberStep:              true,
		GetAllianceAppList:            true,
		HandleAllianceApp:             true,
		DisbandAlliance:               true,
		EditAllianceNotice:            true,
		GetAllianceList:               true,
		CancerJoinAlliance:            true,
		BuyAllianceShop:               true,
		CollectAllianceTaskReward:     true,
		CollectAllianceChestReward:    true,
		GetUserInfoList:               true,
		AddFriends:                    true,
		DelFriends:                    true,
		GetFriendsList:                true,
		AddBlackList:                  true,
		DelBlackList:                  true,
		GetBlackList:                  true,
		GetFriendRecommendationList:   true,
		GetFriendAppList:              true,
		HandleFriendApp:               true,
		SettingAddFriendCondition:     true,
		HookSendMessage:               true,
		SubmitOrder:                   true,
		GetPowerRankInfo:              true,
		UpgradeLordEquipLevel:         true,
		GemCraft:                      true,
		EnhanceGem:                    true,
		EquipGem:                      true,
		UnEquipGem:                    true,
		UpgradeLordEquipGrade:         true,
		LockGem:                       true,
		UnlockGem:                     true,
		SwitchEquipGem:                true,
		LordGemRandom:                 true,
		HeroUpgradeQuality:            true,
		SweepDungeon:                  true,
		SelectDungeonRougeTab:         true,
		StartDungeon:                  true,
		RefreshDungeonRougeTab:        true,
		SaveFunctionOpen:              true,
		SaveGuideProgress:             true,
		GetActivityList:               true,
		CollectSign7Reward:            true,
		CollectDay7Reward:             true,
		CollectDay7ChestReward:        true,
		SelectDay7Reward:              true,
		CollectMailReward:             true,
		ReadMail:                      true,
		DelMail:                       true,
		DeleteAllReadMail:             true,
		ReadAndCollectAllMail:         true,
		CollectGrowthFundReward:       true,
		GetFirstChargeReward:          true,
		ReceiveMonthCardReward:        true,
		CollectDailySaleFreeReward:    true,
		CollectDailyWeekTaskChest:     true,
		InitSymbiotic:                 true,
		SwitchSymbioticHero:           true,
		CollectAchievementTaskReward:  true,
		OneKeyUpgradeHero:             true,
		OneKeyEquipGem:                true,
		CollectGroupReward:            true,
	}
)

type rpcExecutionTime struct {
	UserOnline                    bool
	UpdateUserTopics              bool
	KickOut                       bool
	WantUserOfflineNtf            bool
	UpdateUserConnWarehouse       bool
	Echo                          bool
	PerformUserConnHealthcheck    bool
	RequestDispatcher             bool
	RegisterUser                  bool
	Login                         bool
	HelloWorld                    bool
	VerifyUser                    bool
	AddBuilding                   bool
	UpgradeBuilding               bool
	HeroLottery                   bool
	HeroUpgradeLevel              bool
	HeroUpgradeStar               bool
	DebugAddItem                  bool
	BuildWorkHero                 bool
	CollectResource               bool
	SetHeroBattlePos              bool
	SetHeroTroop                  bool
	SetDefaultBattlePos           bool
	CollectHeroLotteryAccReward   bool
	CollectHeroLotteryLevelReward bool
	RandomHeroDice                bool
	LockHeroDice                  bool
	TrainTroops                   bool
	UserItem                      bool
	BuildingWorkVillage           bool
	FinishDungeonStage            bool
	CollectFirstPassReward        bool
	StartResearch                 bool
	CancerResearch                bool
	CollectMainReward             bool
	CollectMapChapterReward       bool
	FinishBuilding                bool
	FinishResearch                bool
	FinishTrain                   bool
	FinishMapEvent                bool
	Debug                         bool
	OnBackCity                    bool
	UpgradeSkillLevel             bool
	UpgradeDaveLevel              bool
	FinishMainStage               bool
	CollectIdleReward             bool
	ResetMainStage                bool
	KillMonster                   bool
	StartMainStage                bool
	GetIdleReward                 bool
	GetManifest                   bool
	UploadDeviceInfo              bool
	RefreshRougeSkill             bool
	HeartBeat                     bool
	SelectRougeSkill              bool
	CollectStageReward            bool
	GetGiftList                   bool
	PrepareOrder                  bool
	HeroUpgradeGene               bool
	SelectEliteRougeSkill         bool
	GetStageRankInfo              bool
	GetStageRankInfoByStageId     bool
	CollectStageLevelRewards      bool
	GetPhotovoltaicReward         bool
	CollectPhotovoltaicReward     bool
	SweepMainStage                bool
	GetAllStageRankInfo           bool
	ChangeName                    bool
	ChangeAvatar                  bool
	CollectMonsterBookReward      bool
	HeroBeKilled                  bool
	GetDailyTask                  bool
	CollectDailyTaskReward        bool
	CollectDailyChestReward       bool
	GetMailList                   bool
	GetAllianceInfo               bool
	CreateAlliance                bool
	ApplyJoinAlliance             bool
	GetAllianceMembersInfos       bool
	LeaveAlliance                 bool
	EditAllianceName              bool
	EditAllianceAcronym           bool
	EditRecruitSetting            bool
	EditAllianceStepName          bool
	EditAllianceFlag              bool
	TransferPresident             bool
	RemoveMember                  bool
	ChangeMemberStep              bool
	GetAllianceAppList            bool
	HandleAllianceApp             bool
	DisbandAlliance               bool
	EditAllianceNotice            bool
	GetAllianceList               bool
	CancerJoinAlliance            bool
	BuyAllianceShop               bool
	CollectAllianceTaskReward     bool
	CollectAllianceChestReward    bool
	GetUserInfoList               bool
	AddFriends                    bool
	DelFriends                    bool
	GetFriendsList                bool
	AddBlackList                  bool
	DelBlackList                  bool
	GetBlackList                  bool
	GetFriendRecommendationList   bool
	GetFriendAppList              bool
	HandleFriendApp               bool
	SettingAddFriendCondition     bool
	HookSendMessage               bool
	SubmitOrder                   bool
	GetPowerRankInfo              bool
	UpgradeLordEquipLevel         bool
	GemCraft                      bool
	EnhanceGem                    bool
	EquipGem                      bool
	UnEquipGem                    bool
	UpgradeLordEquipGrade         bool
	LockGem                       bool
	UnlockGem                     bool
	SwitchEquipGem                bool
	LordGemRandom                 bool
	HeroUpgradeQuality            bool
	SweepDungeon                  bool
	SelectDungeonRougeTab         bool
	StartDungeon                  bool
	RefreshDungeonRougeTab        bool
	SaveFunctionOpen              bool
	SaveGuideProgress             bool
	GetActivityList               bool
	CollectSign7Reward            bool
	CollectDay7Reward             bool
	CollectDay7ChestReward        bool
	SelectDay7Reward              bool
	CollectMailReward             bool
	ReadMail                      bool
	DelMail                       bool
	DeleteAllReadMail             bool
	ReadAndCollectAllMail         bool
	CollectGrowthFundReward       bool
	GetFirstChargeReward          bool
	ReceiveMonthCardReward        bool
	CollectDailySaleFreeReward    bool
	CollectDailyWeekTaskChest     bool
	InitSymbiotic                 bool
	SwitchSymbioticHero           bool
	CollectAchievementTaskReward  bool
	OneKeyUpgradeHero             bool
	OneKeyEquipGem                bool
	CollectGroupReward            bool
}

type messageFragment struct {
	N64        int64
	N32        int32
	SeqNumber  int32
	TraceID    int64
	UserConnID int64
}

func buildMessageFragment(msg wnet.Message) *messageFragment {
	msgb, ok := msg.(*wnet.MessageB)
	if ok {
		return &messageFragment{
			N64:        msgb.H.N64,
			N32:        msgb.H.N32,
			SeqNumber:  msgb.H.SeqNumber,
			TraceID:    msgb.TraceID,
			UserConnID: msgb.UserConnID,
		}
	}
	return nil
}

func buildReportFields(existing []zap.Field, frag *messageFragment, req, reply interface{}, dt int64, err error) []zap.Field {
	n := len(existing)
	if !misc.IsNil(req) {
		n++
	}
	if !misc.IsNil(reply) {
		n++
	}
	if dt >= 0 {
		n++
	}
	if err != nil {
		n++
	}
	if frag != nil {
		n += 5
	}

	a := make([]zap.Field, n, n)
	var i int
	for i < len(existing) {
		a[i] = existing[i]
		a[i].Key = fmt.Sprintf("x:%d:%s", i, a[i].Key)
		i++
	}
	if !misc.IsNil(req) {
		a[i] = zap.Reflect("request", req)
		i++
	}
	if !misc.IsNil(reply) {
		a[i] = zap.Reflect("reply", reply)
		i++
	}
	if dt >= 0 {
		a[i] = zap.Int64("timeCost", dt)
		i++
	}
	if err != nil {
		a[i] = zap.NamedError("err", err)
		i++
	}
	if frag != nil {
		a[i] = zap.Int64("N64", frag.N64)
		i++
		a[i] = zap.Int32("N32", frag.N32)
		i++
		a[i] = zap.Int32("SeqNumber", frag.SeqNumber)
		i++
		a[i] = zap.Int64("TraceID", frag.TraceID)
		i++
		a[i] = zap.Int64("UserConnID", frag.UserConnID)
		i++
	}

	if i != n {
		panic("buildReportFields: i != n")
	}
	return a
}

func unmarshalSimpleErrorNtf(ctx *wctx.BizContext, conn wnet.Conn, msg wnet.Message) (interface{}, error) {
	var hdr = msg.Header()
	var ntf SimpleErrorNtf
	err := autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &ntf)
	if err != nil {
		ntf.reportUnmarshalError(msg, err)
		return nil, wnet.NewReportedError(fmt.Errorf(
			"failed to unmarshal SimpleErrorNtf. codec: %d, err: %w", hdr.CodecID(), err))
	}
	if b, ok := msg.(*wnet.MessageB); ok {
		ctx.TraceID = b.TraceID
	}
	return &ntf, nil
}

func (this *SimpleErrorNtf) reportUnmarshalError(msg wnet.Message, err error) {
	stats.SimpleError.IncN()
	stats.SimpleError.IncErrs()
	fields := buildReportFields(nil, buildMessageFragment(msg), nil, nil, -1, err)
	wlog.DefaultPlainLogger().Error("handler: SimpleError", fields...)
}

func (this *SimpleErrorNtf) report(ctx *wctx.Context, frag *messageFragment, dt int64, err error) error {
	if err != nil {
		stats.SimpleError.IncErrs()
		fields := buildReportFields(ctx.ZapFields, frag, this, nil, dt, err)
		wlog.DefaultPlainLogger().Error("handler: SimpleError, "+strconv.Itoa(int(ctx.TraceID)), fields...)
		return wnet.NewReportedError(err)
	} else if dt >= 0 {
		fields := buildReportFields(ctx.ZapFields, frag, nil, nil, dt, err)
		wlog.DefaultPlainLogger().Info("handler: SimpleError", fields...)
		return nil
	} else {
		return nil
	}
}

func invokeSimpleErrorHandler(ctx *wctx.Context, obj interface{}, handlers WrpcHandlers) (_ interface{}, err error) {
	ntf := obj.(*SimpleErrorNtf)
	return nil, ntf.invokeHandler(ctx, handlers)
}

func (this *SimpleErrorNtf) invokeHandler(ctx *wctx.Context, handlers WrpcHandlers) (err error) {
	var startTime time.Time
	if HandlerExecutionTime.SimpleError {
		startTime = time.Now()
	}
	frag := buildMessageFragment(ctx.ReqMessage)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %+v\n%s", r, debug.Stack())
		}
		var dt int64 = -1
		if HandlerExecutionTime.SimpleError {
			dt = int64(time.Since(startTime) / time.Microsecond)
		}
		err = this.report(ctx, frag, dt, err)
	}()

	stats.SimpleError.IncN()
	return handlers.SimpleErrorHandler(ctx, this)
}

func unmarshalMulticastErrorNtf(ctx *wctx.BizContext, conn wnet.Conn, msg wnet.Message) (interface{}, error) {
	var hdr = msg.Header()
	var ntf MulticastErrorNtf
	err := autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &ntf)
	if err != nil {
		ntf.reportUnmarshalError(msg, err)
		return nil, wnet.NewReportedError(fmt.Errorf(
			"failed to unmarshal MulticastErrorNtf. codec: %d, err: %w", hdr.CodecID(), err))
	}
	if b, ok := msg.(*wnet.MessageB); ok {
		ctx.TraceID = b.TraceID
	}
	return &ntf, nil
}

func (this *MulticastErrorNtf) reportUnmarshalError(msg wnet.Message, err error) {
	stats.MulticastError.IncN()
	stats.MulticastError.IncErrs()
	fields := buildReportFields(nil, buildMessageFragment(msg), nil, nil, -1, err)
	wlog.DefaultPlainLogger().Error("handler: MulticastError", fields...)
}

func (this *MulticastErrorNtf) report(ctx *wctx.Context, frag *messageFragment, dt int64, err error) error {
	if err != nil {
		stats.MulticastError.IncErrs()
		fields := buildReportFields(ctx.ZapFields, frag, this, nil, dt, err)
		wlog.DefaultPlainLogger().Error("handler: MulticastError, "+strconv.Itoa(int(ctx.TraceID)), fields...)
		return wnet.NewReportedError(err)
	} else if dt >= 0 {
		fields := buildReportFields(ctx.ZapFields, frag, nil, nil, dt, err)
		wlog.DefaultPlainLogger().Info("handler: MulticastError", fields...)
		return nil
	} else {
		return nil
	}
}

func invokeMulticastErrorHandler(ctx *wctx.Context, obj interface{}, handlers WrpcHandlers) (_ interface{}, err error) {
	ntf := obj.(*MulticastErrorNtf)
	return nil, ntf.invokeHandler(ctx, handlers)
}

func (this *MulticastErrorNtf) invokeHandler(ctx *wctx.Context, handlers WrpcHandlers) (err error) {
	var startTime time.Time
	if HandlerExecutionTime.MulticastError {
		startTime = time.Now()
	}
	frag := buildMessageFragment(ctx.ReqMessage)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %+v\n%s", r, debug.Stack())
		}
		var dt int64 = -1
		if HandlerExecutionTime.MulticastError {
			dt = int64(time.Since(startTime) / time.Microsecond)
		}
		err = this.report(ctx, frag, dt, err)
	}()

	stats.MulticastError.IncN()
	return handlers.MulticastErrorHandler(ctx, this)
}

func unmarshalUserOfflineNtf(ctx *wctx.BizContext, conn wnet.Conn, msg wnet.Message) (interface{}, error) {
	var hdr = msg.Header()
	var ntf UserOfflineNtf
	err := autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &ntf)
	if err != nil {
		ntf.reportUnmarshalError(msg, err)
		return nil, wnet.NewReportedError(fmt.Errorf(
			"failed to unmarshal UserOfflineNtf. codec: %d, err: %w", hdr.CodecID(), err))
	}
	if b, ok := msg.(*wnet.MessageB); ok {
		ctx.TraceID = b.TraceID
	}
	return &ntf, nil
}

func (this *UserOfflineNtf) reportUnmarshalError(msg wnet.Message, err error) {
	stats.UserOffline.IncN()
	stats.UserOffline.IncErrs()
	fields := buildReportFields(nil, buildMessageFragment(msg), nil, nil, -1, err)
	wlog.DefaultPlainLogger().Error("handler: UserOffline", fields...)
}

func (this *UserOfflineNtf) report(ctx *wctx.Context, frag *messageFragment, dt int64, err error) error {
	if err != nil {
		stats.UserOffline.IncErrs()
		fields := buildReportFields(ctx.ZapFields, frag, this, nil, dt, err)
		wlog.DefaultPlainLogger().Error("handler: UserOffline, "+strconv.Itoa(int(ctx.TraceID)), fields...)
		return wnet.NewReportedError(err)
	} else if dt >= 0 {
		fields := buildReportFields(ctx.ZapFields, frag, nil, nil, dt, err)
		wlog.DefaultPlainLogger().Info("handler: UserOffline", fields...)
		return nil
	} else {
		return nil
	}
}

func invokeUserOfflineHandler(ctx *wctx.Context, obj interface{}, handlers WrpcHandlers) (_ interface{}, err error) {
	ntf := obj.(*UserOfflineNtf)
	return nil, ntf.invokeHandler(ctx, handlers)
}

func (this *UserOfflineNtf) invokeHandler(ctx *wctx.Context, handlers WrpcHandlers) (err error) {
	var startTime time.Time
	if HandlerExecutionTime.UserOffline {
		startTime = time.Now()
	}
	frag := buildMessageFragment(ctx.ReqMessage)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %+v\n%s", r, debug.Stack())
		}
		var dt int64 = -1
		if HandlerExecutionTime.UserOffline {
			dt = int64(time.Since(startTime) / time.Microsecond)
		}
		err = this.report(ctx, frag, dt, err)
	}()

	stats.UserOffline.IncN()
	return handlers.UserOfflineHandler(ctx, this)
}

func (bh BroadcastHelper) SyncServerLoad(ntf *SyncServerLoadNtf, stargates ...int64) error {
	stats.SyncServerLoad.IncN()
	conns := bh.stargateMap.Conns(stargates)
	for _, conn := range conns {
		const flags = wnet.SMF_Recycle | wnet.SMF_Broadcast
		newMsg, err := wnet.BuildMessage(conn, int32(RPC_SyncServerLoad), 0, flags, defaultMessageEncoding, ntf)
		if err != nil {
			stats.SyncServerLoad.IncErrs()
			return err
		}
		if err := conn.SendMessage(newMsg); err != nil {
			stats.SyncServerLoad.IncErrs()
			return err
		}
	}
	return nil
}

func (r Remote) requestUserOnline(ctx context.Context, traceID int64, userConnID int64, uid int64, fingerprint int64, n64 int64, n32 int32, wantOfflineNtf bool, trustClientN32 bool, addToWarehouse map[string]string) (_ *wnet.Stub, err error) {
	stats.UserOnline.IncN()
	req := &UserOnlineRequest{
		UID:            uid,
		Fingerprint:    fingerprint,
		N64:            n64,
		N32:            n32,
		WantOfflineNtf: wantOfflineNtf,
		TrustClientN32: trustClientN32,
		AddToWarehouse: addToWarehouse,
	}
	cdc, payload, err := marshalReqNtf(req, r.conn.Codec)
	if err != nil {
		return nil, err
	}

	const flags = wnet.SMF_Recycle
	msg, err := wnet.BuildMessageDirect(r.conn, int32(RPC_UserOnline), 0, flags, cdc, payload,
		wnet.WithTraceID(traceID), wnet.WithUserConnID(userConnID))
	if err != nil {
		return nil, err
	}
	msg.Header().N64 = n64
	msg.Header().N32 = n32
	return r.conn.Request(ctx, msg, r.userOnlineReplyHandler)
}

func (r Remote) UserOnline(ctx interface{}, userConnID int64, uid int64, fingerprint int64, n64 int64, n32 int32, wantOfflineNtf bool, trustClientN32 bool, addToWarehouse map[string]string) (_ *UserOnlineReply, err error) {
	stdCtx, traceID := digCtx(ctx)
	defer func() {
		if err != nil {
			stats.UserOnline.IncErrs()
		}
	}()
	if stdCtx == nil {
		panic("ctx should contain (or be) a context.Context")
	}
	stub, err := r.requestUserOnline(stdCtx, traceID, userConnID, uid, fingerprint, n64, n32, wantOfflineNtf, trustClientN32, addToWarehouse)
	if err != nil {
		return nil, err
	}
	defer func() {
		if ExecutionTime.UserOnline {
			reportExecutionTime("rpc: UserOnline", stub, err)
		}
	}()

	select {
	case r := <-stub.Reply:
		switch x := r.(type) {
		case *UserOnlineReply:
			return x, nil
		case error:
			return nil, x
		default:
			panic(x)
		}
	case <-stdCtx.Done():
		r.conn.DeleteRequestStub(stub.SeqNumber)
		return nil, stdCtx.Err()
	}
}

func (r remoteAsync) UserOnline(ctx interface{}, userConnID int64, uid int64, fingerprint int64, n64 int64, n32 int32, wantOfflineNtf bool, trustClientN32 bool, addToWarehouse map[string]string) (_ *wnet.Stub, err error) {
	stdCtx, traceID := digCtx(ctx)
	defer func() {
		if err != nil {
			stats.UserOnline.IncErrs()
		}
	}()
	if stdCtx == nil {
		panic("ctx should contain (or be) a context.Context")
	}
	return Remote(r).requestUserOnline(stdCtx, traceID, userConnID, uid, fingerprint, n64, n32, wantOfflineNtf, trustClientN32, addToWarehouse)
}

func (r remoteNtf) UserOnline(ctxOrNil interface{}, userConnID int64, uid int64, fingerprint int64, n64 int64, n32 int32, wantOfflineNtf bool, trustClientN32 bool, addToWarehouse map[string]string) (err error) {
	stdCtx, traceID := digCtx(ctxOrNil)
	defer func() {
		if err != nil {
			stats.UserOnline.IncErrs()
		}
	}()
	_, err = Remote(r).requestUserOnline(stdCtx, traceID, userConnID, uid, fingerprint, n64, n32, wantOfflineNtf, trustClientN32, addToWarehouse)
	return err
}

func (r Remote) userOnlineReplyHandler(conn wnet.Conn, msg wnet.Message, stub *wnet.Stub) error {
	switch hdr := msg.Header(); hdr.MsgID() {
	case int32(RPC_UserOnline):
		var reply UserOnlineReply
		if err := autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &reply); err != nil {
			stub.Reply <- errors.New("failed to unmarshal the reply of UserOnline")
		} else {
			stub.Reply <- &reply
		}
	case int32(enum.Msg_SimpleError):
		var simpleErr pb.SimpleError
		_ = autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &simpleErr)
		stub.Reply <- wnet.WrapRawSimpleError(&simpleErr)
	case int32(enum.Msg_OK):
		stub.Reply <- &UserOnlineReply{}
	default:
		stub.Reply <- fmt.Errorf("unexpected reply of UserOnline: %d", hdr.MsgID())
	}
	msg.RecycleAnyhow()
	return nil
}

func (r Remote) requestUpdateUserTopics(ctx context.Context, traceID int64, userConnID int64, uid int64, toAdd []string, toRemove []string, n64 int64) (_ *wnet.Stub, err error) {
	stats.UpdateUserTopics.IncN()
	req := &UpdateUserTopicsRequest{
		UID:      uid,
		ToAdd:    toAdd,
		ToRemove: toRemove,
		N64:      n64,
	}
	cdc, payload, err := marshalReqNtf(req, r.conn.Codec)
	if err != nil {
		return nil, err
	}

	const flags = wnet.SMF_Recycle
	msg, err := wnet.BuildMessageDirect(r.conn, int32(RPC_UpdateUserTopics), 0, flags, cdc, payload,
		wnet.WithTraceID(traceID), wnet.WithUserConnID(userConnID))
	if err != nil {
		return nil, err
	}
	return r.conn.Request(ctx, msg, r.updateUserTopicsReplyHandler)
}

func (r Remote) UpdateUserTopics(ctx interface{}, userConnID int64, uid int64, toAdd []string, toRemove []string, n64 int64) (_ *UpdateUserTopicsReply, err error) {
	stdCtx, traceID := digCtx(ctx)
	defer func() {
		if err != nil {
			stats.UpdateUserTopics.IncErrs()
		}
	}()
	if stdCtx == nil {
		panic("ctx should contain (or be) a context.Context")
	}
	stub, err := r.requestUpdateUserTopics(stdCtx, traceID, userConnID, uid, toAdd, toRemove, n64)
	if err != nil {
		return nil, err
	}
	defer func() {
		if ExecutionTime.UpdateUserTopics {
			reportExecutionTime("rpc: UpdateUserTopics", stub, err)
		}
	}()

	select {
	case r := <-stub.Reply:
		switch x := r.(type) {
		case *UpdateUserTopicsReply:
			return x, nil
		case error:
			return nil, x
		default:
			panic(x)
		}
	case <-stdCtx.Done():
		r.conn.DeleteRequestStub(stub.SeqNumber)
		return nil, stdCtx.Err()
	}
}

func (r remoteAsync) UpdateUserTopics(ctx interface{}, userConnID int64, uid int64, toAdd []string, toRemove []string, n64 int64) (_ *wnet.Stub, err error) {
	stdCtx, traceID := digCtx(ctx)
	defer func() {
		if err != nil {
			stats.UpdateUserTopics.IncErrs()
		}
	}()
	if stdCtx == nil {
		panic("ctx should contain (or be) a context.Context")
	}
	return Remote(r).requestUpdateUserTopics(stdCtx, traceID, userConnID, uid, toAdd, toRemove, n64)
}

func (r remoteNtf) UpdateUserTopics(ctxOrNil interface{}, userConnID int64, uid int64, toAdd []string, toRemove []string, n64 int64) (err error) {
	stdCtx, traceID := digCtx(ctxOrNil)
	defer func() {
		if err != nil {
			stats.UpdateUserTopics.IncErrs()
		}
	}()
	_, err = Remote(r).requestUpdateUserTopics(stdCtx, traceID, userConnID, uid, toAdd, toRemove, n64)
	return err
}

func (r Remote) updateUserTopicsReplyHandler(conn wnet.Conn, msg wnet.Message, stub *wnet.Stub) error {
	switch hdr := msg.Header(); hdr.MsgID() {
	case int32(RPC_UpdateUserTopics):
		var reply UpdateUserTopicsReply
		if err := autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &reply); err != nil {
			stub.Reply <- errors.New("failed to unmarshal the reply of UpdateUserTopics")
		} else {
			stub.Reply <- &reply
		}
	case int32(enum.Msg_SimpleError):
		var simpleErr pb.SimpleError
		_ = autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &simpleErr)
		stub.Reply <- wnet.WrapRawSimpleError(&simpleErr)
	case int32(enum.Msg_OK):
		stub.Reply <- &UpdateUserTopicsReply{}
	default:
		stub.Reply <- fmt.Errorf("unexpected reply of UpdateUserTopics: %d", hdr.MsgID())
	}
	msg.RecycleAnyhow()
	return nil
}

func (r Remote) requestKickOut(ctx context.Context, traceID int64, uid int64, userConnID int64, sig int64, msgToClient string, msgToStargate string, rogueConn bool, force bool) (_ *wnet.Stub, err error) {
	stats.KickOut.IncN()
	req := &KickOutRequest{
		UID:           uid,
		UserConnID:    userConnID,
		Sig:           sig,
		MsgToClient:   msgToClient,
		MsgToStargate: msgToStargate,
		RogueConn:     rogueConn,
		Force:         force,
	}
	cdc, payload, err := marshalReqNtf(req, r.conn.Codec)
	if err != nil {
		return nil, err
	}

	const flags = wnet.SMF_Recycle
	msg, err := wnet.BuildMessageDirect(r.conn, int32(RPC_KickOut), 0, flags, cdc, payload,
		wnet.WithTraceID(traceID))
	if err != nil {
		return nil, err
	}
	return r.conn.Request(ctx, msg, r.kickOutReplyHandler)
}

func (r Remote) KickOut(ctx interface{}, uid int64, userConnID int64, sig int64, msgToClient string, msgToStargate string, rogueConn bool, force bool) (_ *KickOutReply, err error) {
	stdCtx, traceID := digCtx(ctx)
	defer func() {
		if err != nil {
			stats.KickOut.IncErrs()
		}
	}()
	if stdCtx == nil {
		panic("ctx should contain (or be) a context.Context")
	}
	stub, err := r.requestKickOut(stdCtx, traceID, uid, userConnID, sig, msgToClient, msgToStargate, rogueConn, force)
	if err != nil {
		return nil, err
	}
	defer func() {
		if ExecutionTime.KickOut {
			reportExecutionTime("rpc: KickOut", stub, err)
		}
	}()

	select {
	case r := <-stub.Reply:
		switch x := r.(type) {
		case *KickOutReply:
			return x, nil
		case error:
			return nil, x
		default:
			panic(x)
		}
	case <-stdCtx.Done():
		r.conn.DeleteRequestStub(stub.SeqNumber)
		return nil, stdCtx.Err()
	}
}

func (r remoteAsync) KickOut(ctx interface{}, uid int64, userConnID int64, sig int64, msgToClient string, msgToStargate string, rogueConn bool, force bool) (_ *wnet.Stub, err error) {
	stdCtx, traceID := digCtx(ctx)
	defer func() {
		if err != nil {
			stats.KickOut.IncErrs()
		}
	}()
	if stdCtx == nil {
		panic("ctx should contain (or be) a context.Context")
	}
	return Remote(r).requestKickOut(stdCtx, traceID, uid, userConnID, sig, msgToClient, msgToStargate, rogueConn, force)
}

func (r remoteNtf) KickOut(ctxOrNil interface{}, uid int64, userConnID int64, sig int64, msgToClient string, msgToStargate string, rogueConn bool, force bool) (err error) {
	stdCtx, traceID := digCtx(ctxOrNil)
	defer func() {
		if err != nil {
			stats.KickOut.IncErrs()
		}
	}()
	_, err = Remote(r).requestKickOut(stdCtx, traceID, uid, userConnID, sig, msgToClient, msgToStargate, rogueConn, force)
	return err
}

func (r Remote) kickOutReplyHandler(conn wnet.Conn, msg wnet.Message, stub *wnet.Stub) error {
	switch hdr := msg.Header(); hdr.MsgID() {
	case int32(RPC_KickOut):
		var reply KickOutReply
		if err := autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &reply); err != nil {
			stub.Reply <- errors.New("failed to unmarshal the reply of KickOut")
		} else {
			stub.Reply <- &reply
		}
	case int32(enum.Msg_SimpleError):
		var simpleErr pb.SimpleError
		_ = autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &simpleErr)
		stub.Reply <- wnet.WrapRawSimpleError(&simpleErr)
	case int32(enum.Msg_OK):
		stub.Reply <- &KickOutReply{}
	default:
		stub.Reply <- fmt.Errorf("unexpected reply of KickOut: %d", hdr.MsgID())
	}
	msg.RecycleAnyhow()
	return nil
}

func (r Remote) requestWantUserOfflineNtf(ctx context.Context, traceID int64, userConnID int64, uid int64, n64 int64) (_ *wnet.Stub, err error) {
	stats.WantUserOfflineNtf.IncN()
	req := &WantUserOfflineNtfRequest{
		UID: uid,
		N64: n64,
	}
	cdc, payload, err := marshalReqNtf(req, r.conn.Codec)
	if err != nil {
		return nil, err
	}

	const flags = wnet.SMF_Recycle
	msg, err := wnet.BuildMessageDirect(r.conn, int32(RPC_WantUserOfflineNtf), 0, flags, cdc, payload,
		wnet.WithTraceID(traceID), wnet.WithUserConnID(userConnID))
	if err != nil {
		return nil, err
	}
	return r.conn.Request(ctx, msg, r.wantUserOfflineNtfReplyHandler)
}

func (r Remote) WantUserOfflineNtf(ctx interface{}, userConnID int64, uid int64, n64 int64) (_ *WantUserOfflineNtfReply, err error) {
	stdCtx, traceID := digCtx(ctx)
	defer func() {
		if err != nil {
			stats.WantUserOfflineNtf.IncErrs()
		}
	}()
	if stdCtx == nil {
		panic("ctx should contain (or be) a context.Context")
	}
	stub, err := r.requestWantUserOfflineNtf(stdCtx, traceID, userConnID, uid, n64)
	if err != nil {
		return nil, err
	}
	defer func() {
		if ExecutionTime.WantUserOfflineNtf {
			reportExecutionTime("rpc: WantUserOfflineNtf", stub, err)
		}
	}()

	select {
	case r := <-stub.Reply:
		switch x := r.(type) {
		case *WantUserOfflineNtfReply:
			return x, nil
		case error:
			return nil, x
		default:
			panic(x)
		}
	case <-stdCtx.Done():
		r.conn.DeleteRequestStub(stub.SeqNumber)
		return nil, stdCtx.Err()
	}
}

func (r remoteAsync) WantUserOfflineNtf(ctx interface{}, userConnID int64, uid int64, n64 int64) (_ *wnet.Stub, err error) {
	stdCtx, traceID := digCtx(ctx)
	defer func() {
		if err != nil {
			stats.WantUserOfflineNtf.IncErrs()
		}
	}()
	if stdCtx == nil {
		panic("ctx should contain (or be) a context.Context")
	}
	return Remote(r).requestWantUserOfflineNtf(stdCtx, traceID, userConnID, uid, n64)
}

func (r remoteNtf) WantUserOfflineNtf(ctxOrNil interface{}, userConnID int64, uid int64, n64 int64) (err error) {
	stdCtx, traceID := digCtx(ctxOrNil)
	defer func() {
		if err != nil {
			stats.WantUserOfflineNtf.IncErrs()
		}
	}()
	_, err = Remote(r).requestWantUserOfflineNtf(stdCtx, traceID, userConnID, uid, n64)
	return err
}

func (r Remote) wantUserOfflineNtfReplyHandler(conn wnet.Conn, msg wnet.Message, stub *wnet.Stub) error {
	switch hdr := msg.Header(); hdr.MsgID() {
	case int32(RPC_WantUserOfflineNtf):
		var reply WantUserOfflineNtfReply
		if err := autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &reply); err != nil {
			stub.Reply <- errors.New("failed to unmarshal the reply of WantUserOfflineNtf")
		} else {
			stub.Reply <- &reply
		}
	case int32(enum.Msg_SimpleError):
		var simpleErr pb.SimpleError
		_ = autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &simpleErr)
		stub.Reply <- wnet.WrapRawSimpleError(&simpleErr)
	case int32(enum.Msg_OK):
		stub.Reply <- &WantUserOfflineNtfReply{}
	default:
		stub.Reply <- fmt.Errorf("unexpected reply of WantUserOfflineNtf: %d", hdr.MsgID())
	}
	msg.RecycleAnyhow()
	return nil
}

func (r Remote) requestUpdateUserConnWarehouse(ctx context.Context, traceID int64, userConnID int64, uid int64, toUpdate map[string]string, toRemove []string, n64 int64) (_ *wnet.Stub, err error) {
	stats.UpdateUserConnWarehouse.IncN()
	req := &UpdateUserConnWarehouseRequest{
		UID:      uid,
		ToUpdate: toUpdate,
		ToRemove: toRemove,
		N64:      n64,
	}
	cdc, payload, err := marshalReqNtf(req, r.conn.Codec)
	if err != nil {
		return nil, err
	}

	const flags = wnet.SMF_Recycle
	msg, err := wnet.BuildMessageDirect(r.conn, int32(RPC_UpdateUserConnWarehouse), 0, flags, cdc, payload,
		wnet.WithTraceID(traceID), wnet.WithUserConnID(userConnID))
	if err != nil {
		return nil, err
	}
	return r.conn.Request(ctx, msg, r.updateUserConnWarehouseReplyHandler)
}

func (r Remote) UpdateUserConnWarehouse(ctx interface{}, userConnID int64, uid int64, toUpdate map[string]string, toRemove []string, n64 int64) (_ *UpdateUserConnWarehouseReply, err error) {
	stdCtx, traceID := digCtx(ctx)
	defer func() {
		if err != nil {
			stats.UpdateUserConnWarehouse.IncErrs()
		}
	}()
	if stdCtx == nil {
		panic("ctx should contain (or be) a context.Context")
	}
	stub, err := r.requestUpdateUserConnWarehouse(stdCtx, traceID, userConnID, uid, toUpdate, toRemove, n64)
	if err != nil {
		return nil, err
	}
	defer func() {
		if ExecutionTime.UpdateUserConnWarehouse {
			reportExecutionTime("rpc: UpdateUserConnWarehouse", stub, err)
		}
	}()

	select {
	case r := <-stub.Reply:
		switch x := r.(type) {
		case *UpdateUserConnWarehouseReply:
			return x, nil
		case error:
			return nil, x
		default:
			panic(x)
		}
	case <-stdCtx.Done():
		r.conn.DeleteRequestStub(stub.SeqNumber)
		return nil, stdCtx.Err()
	}
}

func (r remoteAsync) UpdateUserConnWarehouse(ctx interface{}, userConnID int64, uid int64, toUpdate map[string]string, toRemove []string, n64 int64) (_ *wnet.Stub, err error) {
	stdCtx, traceID := digCtx(ctx)
	defer func() {
		if err != nil {
			stats.UpdateUserConnWarehouse.IncErrs()
		}
	}()
	if stdCtx == nil {
		panic("ctx should contain (or be) a context.Context")
	}
	return Remote(r).requestUpdateUserConnWarehouse(stdCtx, traceID, userConnID, uid, toUpdate, toRemove, n64)
}

func (r remoteNtf) UpdateUserConnWarehouse(ctxOrNil interface{}, userConnID int64, uid int64, toUpdate map[string]string, toRemove []string, n64 int64) (err error) {
	stdCtx, traceID := digCtx(ctxOrNil)
	defer func() {
		if err != nil {
			stats.UpdateUserConnWarehouse.IncErrs()
		}
	}()
	_, err = Remote(r).requestUpdateUserConnWarehouse(stdCtx, traceID, userConnID, uid, toUpdate, toRemove, n64)
	return err
}

func (r Remote) updateUserConnWarehouseReplyHandler(conn wnet.Conn, msg wnet.Message, stub *wnet.Stub) error {
	switch hdr := msg.Header(); hdr.MsgID() {
	case int32(RPC_UpdateUserConnWarehouse):
		var reply UpdateUserConnWarehouseReply
		if err := autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &reply); err != nil {
			stub.Reply <- errors.New("failed to unmarshal the reply of UpdateUserConnWarehouse")
		} else {
			stub.Reply <- &reply
		}
	case int32(enum.Msg_SimpleError):
		var simpleErr pb.SimpleError
		_ = autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &simpleErr)
		stub.Reply <- wnet.WrapRawSimpleError(&simpleErr)
	case int32(enum.Msg_OK):
		stub.Reply <- &UpdateUserConnWarehouseReply{}
	default:
		stub.Reply <- fmt.Errorf("unexpected reply of UpdateUserConnWarehouse: %d", hdr.MsgID())
	}
	msg.RecycleAnyhow()
	return nil
}

func (r Remote) requestEcho(ctx context.Context, traceID int64) (_ *wnet.Stub, err error) {
	stats.Echo.IncN()
	req := &EchoRequest{}
	cdc, payload, err := marshalReqNtf(req, r.conn.Codec)
	if err != nil {
		return nil, err
	}

	const flags = wnet.SMF_Recycle
	msg, err := wnet.BuildMessageDirect(r.conn, int32(RPC_Echo), 0, flags, cdc, payload,
		wnet.WithTraceID(traceID))
	if err != nil {
		return nil, err
	}
	return r.conn.Request(ctx, msg, r.echoReplyHandler)
}

func (r Remote) Echo(ctx interface{}) (_ *EchoReply, err error) {
	stdCtx, traceID := digCtx(ctx)
	defer func() {
		if err != nil {
			stats.Echo.IncErrs()
		}
	}()
	if stdCtx == nil {
		panic("ctx should contain (or be) a context.Context")
	}
	stub, err := r.requestEcho(stdCtx, traceID)
	if err != nil {
		return nil, err
	}
	defer func() {
		if ExecutionTime.Echo {
			reportExecutionTime("rpc: Echo", stub, err)
		}
	}()

	select {
	case r := <-stub.Reply:
		switch x := r.(type) {
		case *EchoReply:
			return x, nil
		case error:
			return nil, x
		default:
			panic(x)
		}
	case <-stdCtx.Done():
		r.conn.DeleteRequestStub(stub.SeqNumber)
		return nil, stdCtx.Err()
	}
}

func (r remoteAsync) Echo(ctx interface{}) (_ *wnet.Stub, err error) {
	stdCtx, traceID := digCtx(ctx)
	defer func() {
		if err != nil {
			stats.Echo.IncErrs()
		}
	}()
	if stdCtx == nil {
		panic("ctx should contain (or be) a context.Context")
	}
	return Remote(r).requestEcho(stdCtx, traceID)
}

func (r remoteNtf) Echo(ctxOrNil interface{}) (err error) {
	stdCtx, traceID := digCtx(ctxOrNil)
	defer func() {
		if err != nil {
			stats.Echo.IncErrs()
		}
	}()
	_, err = Remote(r).requestEcho(stdCtx, traceID)
	return err
}

func (r Remote) echoReplyHandler(conn wnet.Conn, msg wnet.Message, stub *wnet.Stub) error {
	switch hdr := msg.Header(); hdr.MsgID() {
	case int32(RPC_Echo):
		var reply EchoReply
		if err := autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &reply); err != nil {
			stub.Reply <- errors.New("failed to unmarshal the reply of Echo")
		} else {
			stub.Reply <- &reply
		}
	case int32(enum.Msg_SimpleError):
		var simpleErr pb.SimpleError
		_ = autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &simpleErr)
		stub.Reply <- wnet.WrapRawSimpleError(&simpleErr)
	case int32(enum.Msg_OK):
		stub.Reply <- &EchoReply{}
	default:
		stub.Reply <- fmt.Errorf("unexpected reply of Echo: %d", hdr.MsgID())
	}
	msg.RecycleAnyhow()
	return nil
}

func (r Remote) requestPerformUserConnHealthcheck(ctx context.Context, traceID int64, userConnIDs []int64) (_ *wnet.Stub, err error) {
	stats.PerformUserConnHealthcheck.IncN()
	req := &PerformUserConnHealthcheckRequest{
		UserConnIDs: userConnIDs,
	}
	cdc, payload, err := marshalReqNtf(req, r.conn.Codec)
	if err != nil {
		return nil, err
	}

	const flags = wnet.SMF_Recycle
	msg, err := wnet.BuildMessageDirect(r.conn, int32(RPC_PerformUserConnHealthcheck), 0, flags, cdc, payload,
		wnet.WithTraceID(traceID))
	if err != nil {
		return nil, err
	}
	return r.conn.Request(ctx, msg, r.performUserConnHealthcheckReplyHandler)
}

func (r Remote) PerformUserConnHealthcheck(ctx interface{}, userConnIDs []int64) (_ *PerformUserConnHealthcheckReply, err error) {
	stdCtx, traceID := digCtx(ctx)
	defer func() {
		if err != nil {
			stats.PerformUserConnHealthcheck.IncErrs()
		}
	}()
	if stdCtx == nil {
		panic("ctx should contain (or be) a context.Context")
	}
	stub, err := r.requestPerformUserConnHealthcheck(stdCtx, traceID, userConnIDs)
	if err != nil {
		return nil, err
	}
	defer func() {
		if ExecutionTime.PerformUserConnHealthcheck {
			reportExecutionTime("rpc: PerformUserConnHealthcheck", stub, err)
		}
	}()

	select {
	case r := <-stub.Reply:
		switch x := r.(type) {
		case *PerformUserConnHealthcheckReply:
			return x, nil
		case error:
			return nil, x
		default:
			panic(x)
		}
	case <-stdCtx.Done():
		r.conn.DeleteRequestStub(stub.SeqNumber)
		return nil, stdCtx.Err()
	}
}

func (r remoteAsync) PerformUserConnHealthcheck(ctx interface{}, userConnIDs []int64) (_ *wnet.Stub, err error) {
	stdCtx, traceID := digCtx(ctx)
	defer func() {
		if err != nil {
			stats.PerformUserConnHealthcheck.IncErrs()
		}
	}()
	if stdCtx == nil {
		panic("ctx should contain (or be) a context.Context")
	}
	return Remote(r).requestPerformUserConnHealthcheck(stdCtx, traceID, userConnIDs)
}

func (r remoteNtf) PerformUserConnHealthcheck(ctxOrNil interface{}, userConnIDs []int64) (err error) {
	stdCtx, traceID := digCtx(ctxOrNil)
	defer func() {
		if err != nil {
			stats.PerformUserConnHealthcheck.IncErrs()
		}
	}()
	_, err = Remote(r).requestPerformUserConnHealthcheck(stdCtx, traceID, userConnIDs)
	return err
}

func (r Remote) performUserConnHealthcheckReplyHandler(conn wnet.Conn, msg wnet.Message, stub *wnet.Stub) error {
	switch hdr := msg.Header(); hdr.MsgID() {
	case int32(RPC_PerformUserConnHealthcheck):
		var reply PerformUserConnHealthcheckReply
		if err := autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &reply); err != nil {
			stub.Reply <- errors.New("failed to unmarshal the reply of PerformUserConnHealthcheck")
		} else {
			stub.Reply <- &reply
		}
	case int32(enum.Msg_SimpleError):
		var simpleErr pb.SimpleError
		_ = autofit.Unmarshal(hdr.CodecID(), msg.Payload(), &simpleErr)
		stub.Reply <- wnet.WrapRawSimpleError(&simpleErr)
	case int32(enum.Msg_OK):
		stub.Reply <- &PerformUserConnHealthcheckReply{}
	default:
		stub.Reply <- fmt.Errorf("unexpected reply of PerformUserConnHealthcheck: %d", hdr.MsgID())
	}
	msg.RecycleAnyhow()
	return nil
}
