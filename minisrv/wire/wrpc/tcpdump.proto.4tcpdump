// Code generated by wrpc. DO NOT EDIT.

syntax = "proto3";
package tcpdump;

// note:
// 	function number > 200
// 	packageName_rpcFuncName = rpcNumber;

enum RPC4TCPDump {
	RPC4TCPDumpNone = 0;
	wrpc_AddBlackList = 1095;
	wrpc_AddBuilding = 1005;
	wrpc_AddFriends = 1092;
	wrpc_ApplyJoinAlliance = 1071;
	wrpc_BuildWorkHero = 1011;
	wrpc_BuildingWorkVillage = 1022;
	wrpc_BuyAllianceShop = 1088;
	wrpc_CancerJoinAlliance = 1087;
	wrpc_CancerResearch = 1026;
	wrpc_ChangeAvatar = 1062;
	wrpc_ChangeMemberStep = 1081;
	wrpc_ChangeName = 1061;
	wrpc_CollectAchievementTaskReward = 1139;
	wrpc_CollectAllianceChestReward = 1090;
	wrpc_CollectAllianceTaskReward = 1089;
	wrpc_CollectDailyChestReward = 1067;
	wrpc_CollectDailySaleFreeReward = 1135;
	wrpc_CollectDailyTaskReward = 1066;
	wrpc_CollectDailyWeekTaskChest = 1136;
	wrpc_CollectDay7ChestReward = 1125;
	wrpc_CollectDay7Reward = 1124;
	wrpc_CollectFirstPassReward = 1024;
	wrpc_CollectGroupReward = 1142;
	wrpc_CollectGrowthFundReward = 1132;
	wrpc_CollectHeroLotteryAccReward = 1016;
	wrpc_CollectHeroLotteryLevelReward = 1017;
	wrpc_CollectIdleReward = 1039;
	wrpc_CollectMailReward = 1127;
	wrpc_CollectMainReward = 1028;
	wrpc_CollectMapChapterReward = 1029;
	wrpc_CollectMonsterBookReward = 1063;
	wrpc_CollectPhotovoltaicReward = 1058;
	wrpc_CollectResource = 1012;
	wrpc_CollectSign7Reward = 1123;
	wrpc_CollectStageLevelRewards = 1056;
	wrpc_CollectStageReward = 1049;
	wrpc_CreateAlliance = 1070;
	wrpc_Debug = 1034;
	wrpc_DebugAddItem = 1010;
	wrpc_DelBlackList = 1096;
	wrpc_DelFriends = 1093;
	wrpc_DelMail = 1129;
	wrpc_DeleteAllReadMail = 1130;
	wrpc_DisbandAlliance = 1084;
	wrpc_Echo = 107;
	wrpc_EditAllianceAcronym = 1075;
	wrpc_EditAllianceFlag = 1078;
	wrpc_EditAllianceName = 1074;
	wrpc_EditAllianceNotice = 1085;
	wrpc_EditAllianceStepName = 1077;
	wrpc_EditRecruitSetting = 1076;
	wrpc_EnhanceGem = 1107;
	wrpc_EquipGem = 1108;
	wrpc_FinishBuilding = 1030;
	wrpc_FinishDungeonStage = 1023;
	wrpc_FinishMainStage = 1038;
	wrpc_FinishMapEvent = 1033;
	wrpc_FinishResearch = 1031;
	wrpc_FinishTrain = 1032;
	wrpc_GemCraft = 1106;
	wrpc_GetActivityList = 1122;
	wrpc_GetAllStageRankInfo = 1060;
	wrpc_GetAllianceAppList = 1082;
	wrpc_GetAllianceInfo = 1069;
	wrpc_GetAllianceList = 1086;
	wrpc_GetAllianceMembersInfos = 1072;
	wrpc_GetBlackList = 1097;
	wrpc_GetDailyTask = 1065;
	wrpc_GetFirstChargeReward = 1133;
	wrpc_GetFriendAppList = 1099;
	wrpc_GetFriendRecommendationList = 1098;
	wrpc_GetFriendsList = 1094;
	wrpc_GetGiftList = 1050;
	wrpc_GetIdleReward = 1043;
	wrpc_GetMailList = 1068;
	wrpc_GetManifest = 1044;
	wrpc_GetPhotovoltaicReward = 1057;
	wrpc_GetPowerRankInfo = 1104;
	wrpc_GetStageRankInfo = 1054;
	wrpc_GetStageRankInfoByStageId = 1055;
	wrpc_GetUserInfoList = 1091;
	wrpc_HandleAllianceApp = 1083;
	wrpc_HandleFriendApp = 1100;
	wrpc_HeartBeat = 1047;
	wrpc_HelloWorld = 1003;
	wrpc_HeroBeKilled = 1064;
	wrpc_HeroLottery = 1007;
	wrpc_HeroUpgradeGene = 1052;
	wrpc_HeroUpgradeLevel = 1008;
	wrpc_HeroUpgradeQuality = 1115;
	wrpc_HeroUpgradeStar = 1009;
	wrpc_HookSendMessage = 1102;
	wrpc_InitSymbiotic = 1137;
	wrpc_KickOut = 103;
	wrpc_KillMonster = 1041;
	wrpc_LeaveAlliance = 1073;
	wrpc_LockGem = 1111;
	wrpc_LockHeroDice = 1019;
	wrpc_Login = 1002;
	wrpc_LordGemRandom = 1114;
	wrpc_MulticastError = 9;
	wrpc_OnBackCity = 1035;
	wrpc_OneKeyEquipGem = 1141;
	wrpc_OneKeyUpgradeHero = 1140;
	wrpc_PerformUserConnHealthcheck = 108;
	wrpc_PrepareOrder = 1051;
	wrpc_PushMsgs = 6001;
	wrpc_PushOrderFinish = 6006;
	wrpc_PushPBMsgs = 6004;
	wrpc_PushPBTopicMsg = 6005;
	wrpc_RandomHeroDice = 1018;
	wrpc_ReadAndCollectAllMail = 1131;
	wrpc_ReadMail = 1128;
	wrpc_ReceiveMonthCardReward = 1134;
	wrpc_RefreshDungeonRougeTab = 1119;
	wrpc_RefreshRougeSkill = 1046;
	wrpc_RegisterUser = 1001;
	wrpc_RemoveMember = 1080;
	wrpc_RequestDispatcher = 501;
	wrpc_ResetMainStage = 1040;
	wrpc_SaveFunctionOpen = 1120;
	wrpc_SaveGuideProgress = 1121;
	wrpc_SelectDay7Reward = 1126;
	wrpc_SelectDungeonRougeTab = 1117;
	wrpc_SelectEliteRougeSkill = 1053;
	wrpc_SelectRougeSkill = 1048;
	wrpc_SetDefaultBattlePos = 1015;
	wrpc_SetHeroBattlePos = 1013;
	wrpc_SetHeroTroop = 1014;
	wrpc_SettingAddFriendCondition = 1101;
	wrpc_SimpleError = 4;
	wrpc_StartDungeon = 1118;
	wrpc_StartMainStage = 1042;
	wrpc_StartResearch = 1025;
	wrpc_SubmitOrder = 1103;
	wrpc_SweepDungeon = 1116;
	wrpc_SweepMainStage = 1059;
	wrpc_SwitchEquipGem = 1113;
	wrpc_SwitchSymbioticHero = 1138;
	wrpc_SyncServerLoad = 109;
	wrpc_TrainTroops = 1020;
	wrpc_TransferPresident = 1079;
	wrpc_UnEquipGem = 1109;
	wrpc_UnlockGem = 1112;
	wrpc_UpdateUserConnWarehouse = 106;
	wrpc_UpdateUserTopics = 101;
	wrpc_UpgradeBuilding = 1006;
	wrpc_UpgradeDaveLevel = 1037;
	wrpc_UpgradeLordEquipGrade = 1110;
	wrpc_UpgradeLordEquipLevel = 1105;
	wrpc_UpgradeSkillLevel = 1036;
	wrpc_UploadDeviceInfo = 1045;
	wrpc_UserItem = 1021;
	wrpc_UserOffline = 104;
	wrpc_UserOnline = 100;
	wrpc_VerifyUser = 1004;
	wrpc_WantUserOfflineNtf = 105;
}

