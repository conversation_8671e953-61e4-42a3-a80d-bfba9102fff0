
�$

datarow.protominirpc-github.com/gogo/protobuf/gogoproto/gogo.protoshare_replicas.proto"

Deprecated"�
DataRow*
table (2.minirpc.ClientTableRtableE
achievement_task/ (2.minirpc.AchievementTaskH RachievementTask<

activity_task* (2.minirpc.ActivityTaskH RactivityTask/
alliance# (2.minirpc.AllianceH RallianceB
alliance_member$ (2.minirpc.AllianceMemberH RallianceMemberF
alliance_shop_buy% (2.minirpc.AllianceShopBuyH RallianceShopBuy<

alliance_task& (2.minirpc.AllianceTaskH RallianceTask)
animal (2.minirpc.AnimalH Ranimal<

building_info (2.minirpc.BuildingInfoH RbuildingInfo?
client_version0 (2.minirpc.ClientVersionH R
clientVersion5

consumable (2.minirpc.ConsumableH R
consumable3

daily_task (2.minirpc.DailyTaskH R	dailyTask#
dave (2
.minirpc.DaveH Rdave,
dungeon (2.minirpc.DungeonH Rdungeon9
first_charge- (2.minirpc.FirstChargeH RfirstCharge<

function_open( (2.minirpc.FunctionOpenH RfunctionOpenI
global_stage_level (2.minirpc.GlobalStageLevelH RglobalStageLevel6
growth_fund, (2.minirpc.GrowthFundH R
growthFund6
growth_task (2.minirpc.GrowthTaskH R
growthTask0
	hero_info (2.minirpc.HeroInfoH RheroInfo9
hero_lottery (2.minirpc.HeroLotteryH RheroLottery3

hero_skill (2.minirpc.HeroSkillH R	heroSkill?
huatuo_version1 (2.minirpc.HuatuoVersionH R
huatuoVersion#
lord
 (2
.minirpc.LordH Rlord3

lord_equip  (2.minirpc.LordEquipH R	lordEquip-
lord_gem! (2.minirpc.LordGemH RlordGem@
lord_gem_random' (2.minirpc.LordGemRandomH R
lordGemRandom@
main_line_stage (2.minirpc.MainLineStageH R
mainLineStage0
	main_task (2.minirpc.MainTaskH RmainTask0
	map_event (2.minirpc.MapEventH RmapEvent9
monster_info (2.minirpc.MonsterInfoH RmonsterInfo3

month_card. (2.minirpc.MonthCardH R	monthCard9
newbie_guide) (2.minirpc.NewbieGuideH RnewbieGuide?
out_consumable (2.minirpc.OutConsumableH R
outConsumable<

payment_order (2.minirpc.PaymentOrderH RpaymentOrder9
regular_pack+ (2.minirpc.RegularPackH RregularPack6
repeat_task (2.minirpc.RepeatTaskH R
repeatTask/
research (2.minirpc.ResearchH Rresearch9
user_benefit (2.minirpc.UserBenefitH RuserBenefit0
	user_data (2.minirpc.UserDataH RuserData9
user_devices (2.minirpc.UserDevicesH RuserDevicesG
user_iap_buy_times (2.minirpc.UserIapBuyTimesH RuserIapBuyTimes0
	user_info (2.minirpc.UserInfoH RuserInfo-
user_job	 (2.minirpc.UserJobH RuserJob6
user_lookup (2.minirpc.UserLookupH R
userLookup0
	user_mail" (2.minirpc.UserMailH RuserMail3

user_troop
 (2.minirpc.UserTroopH R	userTroop/
villager (2.minirpc.VillagerH Rvillager6
weekly_task (2.minirpc.WeeklyTaskH R
weeklyTaskB
row"�

PayloadRow>
add_friend_info (2.minirpc.AddFriendInfoR
addFriendInfoJ
agree_join_alliance (2.minirpc.AgreeJoinAllianceRagreeJoinAlliance7
order_result (2.minirpc.OrderResultRorderResult.
	user_push (2.minirpc.UserPushRuserPush=
per_player_score (2.minirpc.DeprecatedRperPlayerScore*�

ClientTable
ClientTable_None 
ClientTable_AchievementTask/
ClientTable_ActivityTask*
ClientTable_Alliance#
ClientTable_AllianceMember$
ClientTable_AllianceShopBuy%
ClientTable_AllianceTask&
ClientTable_Animal
ClientTable_BuildingInfo
ClientTable_ClientVersion0
ClientTable_Consumable
ClientTable_DailyTask
ClientTable_Dave
ClientTable_Dungeon
ClientTable_FirstCharge-
ClientTable_FunctionOpen( 
ClientTable_GlobalStageLevel
ClientTable_GrowthFund,
ClientTable_GrowthTask
ClientTable_HeroInfo
ClientTable_HeroLottery
ClientTable_HeroSkill
ClientTable_HuatuoVersion1
ClientTable_Lord

ClientTable_LordEquip 
ClientTable_LordGem!
ClientTable_LordGemRandom'
ClientTable_MainLineStage
ClientTable_MainTask
ClientTable_MapEvent
ClientTable_MonsterInfo
ClientTable_MonthCard.
ClientTable_NewbieGuide)
ClientTable_OutConsumable
ClientTable_PaymentOrder
ClientTable_RegularPack+
ClientTable_RepeatTask
ClientTable_Research
ClientTable_UserBenefit
ClientTable_UserData
ClientTable_UserDevices
ClientTable_UserIapBuyTimes
ClientTable_UserInfo
ClientTable_UserJob	
ClientTable_UserLookup
ClientTable_UserMail"
ClientTable_UserTroop

ClientTable_Villager
ClientTable_WeeklyTaskB���������� �� bproto3