
�-
	pb1.protowrpc-github.com/gogo/protobuf/gogoproto/gogo.protoproto/share_replicas.proto"7

RequestParams
Type (	RType
Data (RData"�
UserData#
Ui1 (2.minirpc.UserInfoRUi11
building (2.minirpc.BuildingInfoRbuilding%
hero (2.minirpc.HeroInfoRhero7
hero_lottery (2.minirpc.HeroLotteryRheroLottery5
consumables (2.minirpc.ConsumableRconsumables9

user_benefits (2.minirpc.UserBenefitRuserBenefits+
user_job (2.minirpc.UserJobRuserJob1

user_troop (2.minirpc.UserTroopR	userTroop-
research	 (2.minirpc.ResearchRresearch.
	main_task
 (2.minirpc.MainTaskRmainTask.
	map_event (2.minirpc.MapEventRmapEvent1

hero_skill (2.minirpc.HeroSkillR	heroSkill!
dave
 (2
.minirpc.DaveRdave.
	user_data (2.minirpc.UserDataRuserData>
main_line_stage (2.minirpc.MainLineStageR
mainLineStage&
cur_server_time (R
curServerTime"

is_new_player (RisNewPlayer
	rtm_token (	RrtmTokenA
user_iap_buy_log (2.minirpc.UserIapBuyTimesR
userIapBuyLog1

lord_equip (2.minirpc.LordEquipR	lordEquip+
lord_gem (2.minirpc.LordGemRlordGem>
lord_gem_random (2.minirpc.LordGemRandomR
lordGemRandom*
dungeon (2.minirpc.DungeonRdungeon:

function_open (2.minirpc.FunctionOpenRfunctionOpen7
newbie_guide (2.minirpc.NewbieGuideRnewbieGuide:

activity_task (2.minirpc.ActivityTaskRactivityTask7
first_charge (2.minirpc.FirstChargeRfirstCharge4
growth_fund (2.minirpc.GrowthFundR
growthFund1

month_card (2.minirpc.MonthCardR	monthCard7
regular_pack (2.minirpc.RegularPackRregularPackC
achievement_task (2.minirpc.AchievementTaskRachievementTask"�
ManiFest
langUrl (	RlangUrl
appId (	RappId&
bundleVersionR (	RbundleVersionR&
bundleVersionG (	RbundleVersionG
cdn (	Rcdn"S
Rewards
itemId (RitemId
	itemValue (R	itemValue
type (Rtype"�
KillMonsterRet'
rewards (2
.wrpc.RewardsRrewards&
selectRougeIds (RselectRougeIds*
unSelectRougeIds (RunSelectRougeIds
kill_amount (R
killAmount"�
GetRewardReturn<
rewards (2".wrpc.GetRewardReturn.RewardsEntryRrewards
isFull (RisFull:
RewardsEntry
key (Rkey
value (Rvalue:8"i

SendMsgParams
FromUid (RFromUid
Uids (RUids
MsgType (
RMsgType
Msg (	RMsg"k
SendPBMsgParams
FromUid (RFromUid
Uids (RUids
MsgType (
RMsgType
Msg (RMsg"A
MultiSendMsgParams+
Params (2.wrpc.SendMsgParamsRParams"E
MultiSendPBMsgParams-
Params (2.wrpc.SendPBMsgParamsRParams"V
SendTopicMsgParams
Topic (	RTopic
MsgType (
RMsgType
Msg (	RMsg"X
SendPBTopicMsgParams
Topic (	RTopic
MsgType (
RMsgType
Msg (	RMsg"�
finishEventReturn
success (Rsuccess>
rewards (2$.wrpc.finishEventReturn.RewardsEntryRrewards:
RewardsEntry
key (Rkey
value (Rvalue:8"^
LevelStruct
card_ids (RcardIds
	total_exp (RtotalExp
hero_hp (RheroHp"

GiftStruct
id (Rid"�

LevelRankInfo
rank (Rrank
uid (Ruid
name (	Rname
level (Rlevel
score (Rscore
icon (Ricon
finish_time (R
finishTime"�

PowerRankInfo
rank (Rrank
uid (Ruid
name (	Rname
power (Rpower
score (Rscore
icon (Ricon"�
LordGemCraft 
gem_craft_id (R
gemCraftId7
gem_ids (2.wrpc.LordGemCraft.GemIdsEntryRgemIds
amount (Ramount9
GemIdsEntry
key (Rkey
value (Rvalue:8"�
DailyResult1

daily_task (2.minirpc.DailyTaskR	dailyTask2
daily_chest (2.minirpc.UserDataR
dailyChest4
weekly_chest (2.minirpc.UserDataRweeklyChest"C
GetMailListResult.
	mail_list (2.minirpc.UserMailRmailList"�
AllianceInfoRet-
alliance (2.minirpc.AllianceRallianceB
alliance_members (2.minirpc.AllianceMemberRallianceMembers=

alliance_shop (2.minirpc.AllianceShopBuyRallianceShopJ
alliance_member_info (2.wrpc.AllianceMemberInfoRallianceMemberInfo:

alliance_task (2.minirpc.AllianceTaskRallianceTask8
alliance_chest (2.minirpc.UserDataR
allianceChest"�
AllianceMemberInfo
uid (Ruid
stage_id (RstageId
power (Rpower
name (	Rname(
avatar_config_id (RavatarConfigId
step_id (RstepId
finish_time (R
finishTime"L
GetAllianceMembersInfosRet.
infos (2.wrpc.AllianceMemberInfoRinfos"C
GetAllianceListRet-
alliance (2.minirpc.AllianceRalliance"�
GetUserInfo
uid (Ruid
name (	Rname#

alliance_name (	RallianceName)
alliance_acronym (	RallianceAcronym
	max_stage (RmaxStage
power (Rpower,
friend_stage_limit (RfriendStageLimit'
allow_strangers (RallowStrangers(
avatar_config_id	 (RavatarConfigId%
hero
 (2.minirpc.HeroInfoRhero1

lord_equip (2.minirpc.LordEquipR	lordEquipG
lordGemPower (2#.wrpc.GetUserInfo.LordGemPowerEntryRlordGemPower?
LordGemPowerEntry
key (Rkey
value (Rvalue:8"�
ActivityInfo
	startTime (R	startTime
endTime (RendTime
status (Rstatus"
activityName (	RactivityName

activityId (R
activityId

publicTime (R
publicTime"
activityType (RactivityType"�
InitSymbioticRet 
resetHeroId (RresetHeroIdL
resetItemMap (2(.wrpc.InitSymbioticRet.ResetItemMapEntryRresetItemMap?
ResetItemMapEntry
key (Rkey
value (Rvalue:8"K
OneKeyHeroLevelUpgradeRet
heroId (RheroId
amount (Ramount";
OneKeyEquipGemReq
GemId (RGemId
Pos (RPosB���������� bproto3
��

knights.protowrpc-github.com/gogo/protobuf/gogoproto/gogo.proto	pb1.proto"�
RequestDispatcherRequest
UID (RUID

UserConnID (R
UserConnID

MethodName (	R
MethodName+
Params (2.wrpc.RequestParamsRParams"E
RequestDispatcherReply+
Result (2.wrpc.RequestParamsRResult"a
RegisterUserRequest
UID (RUID

UserConnID (R
UserConnID
WorldId (
RWorldId";
RegisterUserReply&
Result (2.wrpc.UserDataRResult"�
LoginRequest
UID (RUID

UserConnID (R
UserConnID
WorldId (RWorldId
	AccountId (	R	AccountId
Ticket	 (	RTicket
Version
 (	RVersion"4

LoginReply&
Result (2.wrpc.UserDataRResult"Y
HelloWorldRequest
UID (RUID

UserConnID (R
UserConnID
Test (	RTest")
HelloWorldReply
Result (	RResult"�
VerifyUserRequest
UID (RUID

UserConnID (R
UserConnID
Fpid (RFpid
	ConnectId (R	ConnectId 
Fingerprint	 (RFingerprint
	AccountId
 (	R	AccountId
Ticket (	RTicket")
VerifyUserReply
Result (RResult"b
AddBuildingRequest
UID (RUID

UserConnID (R
UserConnID
ConfigId (RConfigId"*
AddBuildingReply
Result (RResult"�
UpgradeBuildingRequest
UID (RUID

UserConnID (R
UserConnID

BuildingId (R
BuildingId

UseDiamond (R
UseDiamond".
UpgradeBuildingReply
Result (RResult"�
HeroLotteryRequest
UID (RUID

UserConnID (R
UserConnID
ConfigId (RConfigId
Amount (RAmount
IsFree	 (RIsFree"9
HeroLotteryReply%
Result (2
.wrpc.RewardsRResult"�
HeroUpgradeLevelRequest
UID (RUID

UserConnID (R
UserConnID
HeroId (RHeroId
Amount (RAmount
IsFree	 (RIsFree"/
HeroUpgradeLevelReply
Result (RResult"z
HeroUpgradeStarRequest
UID (RUID

UserConnID (R
UserConnID
HeroId (RHeroId
Amount (RAmount".
HeroUpgradeStarReply
Result (RResult"w
DebugAddItemRequest
UID (RUID

UserConnID (R
UserConnID
ItemId (RItemId
Amount (RAmount"+
DebugAddItemReply
Result (RResult"�
BuildWorkHeroRequest
UID (RUID

UserConnID (R
UserConnID

BuildingId (R
BuildingId
HeroId (RHeroId
IsWork	 (RIsWork",
BuildWorkHeroReply
Result (RResult"j
CollectResourceRequest
UID (RUID

UserConnID (R
UserConnID

BuildingId (R
BuildingId".
CollectResourceReply
Result (RResult"y
SetHeroBattlePosRequest
UID (RUID

UserConnID (R
UserConnID
PosId (RPosId
HeroId (RHeroId"/
SetHeroBattlePosReply
Result (RResult"q
SetHeroTroopRequest
UID (RUID

UserConnID (R
UserConnID
HeroId (RHeroId
Num (RNum"+
SetHeroTroopReply
Result (RResult"d
SetDefaultBattlePosRequest
UID (RUID

UserConnID (R
UserConnID
PosId (RPosId"2
SetDefaultBattlePosReply
Result (RResult"r
"CollectHeroLotteryAccRewardRequest
UID (RUID

UserConnID (R
UserConnID
ConfigId (RConfigId"�
 CollectHeroLotteryAccRewardReplyJ
Result (22.wrpc.CollectHeroLotteryAccRewardReply.ResultEntryRResult9
ResultEntry
key (Rkey
value (Rvalue:8"r
$CollectHeroLotteryLevelRewardRequest
UID (RUID

UserConnID (R
UserConnID
LevelId (RLevelId"�
"CollectHeroLotteryLevelRewardReplyL
Result (24.wrpc.CollectHeroLotteryLevelRewardReply.ResultEntryRResult9
ResultEntry
key (Rkey
value (Rvalue:8"a
RandomHeroDiceRequest
UID (RUID

UserConnID (R
UserConnID
HeroId (RHeroId"-
RandomHeroDiceReply
Result (RResult"�
LockHeroDiceRequest
UID (RUID

UserConnID (R
UserConnID
HeroId (RHeroId
Slot (RSlot
Lock	 (RLock"+
LockHeroDiceReply
Result (RResult"�
TrainTroopsRequest
UID (RUID

UserConnID (R
UserConnID
ConfigId (RConfigId
Amount (RAmount

UseDiamond	 (R
UseDiamond"*
TrainTroopsReply
Result (RResult"�
UserItemRequest
UID (RUID

UserConnID (R
UserConnID
ItemId (RItemId
Amount (RAmount
Type	 (RType
Para1
 (RPara1"'

UserItemReply
Result (RResult"�
BuildingWorkVillageRequest
UID (RUID

UserConnID (R
UserConnID

BuildingId (R
BuildingId
	VillageId (R	VillageId
IsWork	 (RIsWork"2
BuildingWorkVillageReply
Result (RResult"�
FinishDungeonStageRequest
UID (RUID

UserConnID (R
UserConnID 
DungeonType (RDungeonType
	DungeonId (R	DungeonId
IsWin	 (RIsWin"
EstimateTime
 (REstimateTime"@
FinishDungeonStageReply%
Result (2
.wrpc.RewardsRResult"o
CollectFirstPassRewardRequest
UID (RUID

UserConnID (R
UserConnID
	DungeonId (R	DungeonId"5
CollectFirstPassRewardReply
Result (RResult"�
StartResearchRequest
UID (RUID

UserConnID (R
UserConnID
TechId (RTechId

UseDiamond (R
UseDiamond",
StartResearchReply
Result (RResult"a
CancerResearchRequest
UID (RUID

UserConnID (R
UserConnID
TechId (RTechId"-
CancerResearchReply
Result (RResult"f
CollectMainRewardRequest
UID (RUID

UserConnID (R
UserConnID
QuestId (RQuestId"�
CollectMainRewardReply@
Result (2(.wrpc.CollectMainRewardReply.ResultEntryRResult9
ResultEntry
key (Rkey
value (Rvalue:8"n
CollectMapChapterRewardRequest
UID (RUID

UserConnID (R
UserConnID
RewardId (RRewardId"6
CollectMapChapterRewardReply
Result (RResult"i
FinishBuildingRequest
UID (RUID

UserConnID (R
UserConnID

BuildingId (R
BuildingId"-
FinishBuildingReply
Result (RResult"a
FinishResearchRequest
UID (RUID

UserConnID (R
UserConnID
TechId (RTechId"-
FinishResearchReply
Result (RResult"d
FinishTrainRequest
UID (RUID

UserConnID (R
UserConnID
	TroopType (R	TroopType"*
FinishTrainReply
Result (RResult"u
FinishMapEventRequest
UID (RUID

UserConnID (R
UserConnID
EventId (REventId
Pos (RPos"F
FinishMapEventReply/
Result (2.wrpc.finishEventReturnRResult"j
DebugRequest
UID (RUID

UserConnID (R
UserConnID
Type (RType
Para1 (RPara1"$

DebugReply
Result (RResult"E
OnBackCityRequest
UID (RUID

UserConnID (R
UserConnID")
OnBackCityReply
Result (RResult"~
UpgradeSkillLevelRequest
UID (RUID

UserConnID (R
UserConnID
HeroId (RHeroId
GroupId (RGroupId"0
UpgradeSkillLevelReply
Result (RResult"K
UpgradeDaveLevelRequest
UID (RUID

UserConnID (R
UserConnID"/
UpgradeDaveLevelReply
Result (RResult"�
FinishMainStageRequest
UID (RUID

UserConnID (R
UserConnID3
LevelStruct (2.wrpc.LevelStructRLevelStruct
IsFail (RIsFail
StageId	 (RStageId
IsElite
 (RIsElite"
PerfectIndex (RPerfectIndex"
MaxHpPercent (RMaxHpPercent
MaxTime
 (RMaxTimeL

HeroHealth (2,.wrpc.FinishMainStageRequest.HeroHealthEntryR
HeroHealthC
HeroAtk (2).wrpc.FinishMainStageRequest.HeroAtkEntryRHeroAtkR
BossProgress (2..wrpc.FinishMainStageRequest.BossProgressEntryRBossProgress=
HeroHealthEntry
key (Rkey
value (Rvalue:8:
HeroAtkEntry
key (Rkey
value (Rvalue:8?
BossProgressEntry
key (Rkey
value (Rvalue:8"=
FinishMainStageReply%
Result (2
.wrpc.RewardsRResult"L
CollectIdleRewardRequest
UID (RUID

UserConnID (R
UserConnID"�
CollectIdleRewardReply@
Result (2(.wrpc.CollectIdleRewardReply.ResultEntryRResult9
ResultEntry
key (Rkey
value (Rvalue:8"c
ResetMainStageRequest
UID (RUID

UserConnID (R
UserConnID
StageId (RStageId"-
ResetMainStageReply
Result (RResult"�
KillMonsterRequest
UID (RUID

UserConnID (R
UserConnID
	MonsterId (R	MonsterId 
DungeonType (RDungeonType"@
KillMonsterReply,
Result (2.wrpc.KillMonsterRetRResult"}
StartMainStageRequest
UID (RUID

UserConnID (R
UserConnID
StageId (RStageId
IsElite (RIsElite"-
StartMainStageReply
Result (RResult"H
GetIdleRewardRequest
UID (RUID

UserConnID (R
UserConnID"C
GetIdleRewardReply-
Result (2.wrpc.GetRewardReturnRResult"�
GetManifestRequest
UID (RUID

UserConnID (R
UserConnID
Os (	ROs
Channel (	RChannel$

ClientVersion	 (	R
ClientVersion":
GetManifestReply&
Result (2.wrpc.ManiFestRResult"k
UploadDeviceInfoRequest
UID (RUID

UserConnID (R
UserConnID

DeviceInfo (	R
DeviceInfo"/
UploadDeviceInfoReply
Result (RResult"�
RefreshRougeSkillRequest
UID (RUID

UserConnID (R
UserConnID
RougeId (RRougeId
	RougeType (R	RougeType"0
RefreshRougeSkillReply
Result (RResult"D
HeartBeatRequest
UID (RUID

UserConnID (R
UserConnID"(
HeartBeatReply
Result (RResult"e
SelectRougeSkillRequest
UID (RUID

UserConnID (R
UserConnID
SkillId (RSkillId"/
SelectRougeSkillReply
Result (RResult"�
CollectStageRewardRequest
UID (RUID

UserConnID (R
UserConnID
StageId (RStageId 
RewardIndex (RRewardIndex"@
CollectStageRewardReply%
Result (2
.wrpc.RewardsRResult"F
GetGiftListRequest
UID (RUID

UserConnID (R
UserConnID"<
GetGiftListReply(
Result (2.wrpc.GiftStructRResult"e
PrepareOrderRequest
UID (RUID

UserConnID (R
UserConnID
	ProductId (R	ProductId"+
PrepareOrderReply
Result (	RResult"b
HeroUpgradeGeneRequest
UID (RUID

UserConnID (R
UserConnID
HeroId (RHeroId".
HeroUpgradeGeneReply
Result (RResult"P
SelectEliteRougeSkillRequest
UID (RUID

UserConnID (R
UserConnID"4
SelectEliteRougeSkillReply
Result (RResult"K
GetStageRankInfoRequest
UID (RUID

UserConnID (R
UserConnID"D
GetStageRankInfoReply+
Result (2.wrpc.LevelRankInfoRResult"n
 GetStageRankInfoByStageIdRequest
UID (RUID

UserConnID (R
UserConnID
StageId (RStageId"M
GetStageRankInfoByStageIdReply+
Result (2.wrpc.LevelRankInfoRResult"o
CollectStageLevelRewardsRequest
UID (RUID

UserConnID (R
UserConnID
RewardId (RRewardId"F
CollectStageLevelRewardsReply%
Result (2
.wrpc.RewardsRResult"P
GetPhotovoltaicRewardRequest
UID (RUID

UserConnID (R
UserConnID"4
GetPhotovoltaicRewardReply
Result (RResult"T
 CollectPhotovoltaicRewardRequest
UID (RUID

UserConnID (R
UserConnID"�
CollectPhotovoltaicRewardReplyH
Result (20.wrpc.CollectPhotovoltaicRewardReply.ResultEntryRResult9
ResultEntry
key (Rkey
value (Rvalue:8"a
SweepMainStageRequest
UID (RUID

UserConnID (R
UserConnID
Amount (RAmount"<
SweepMainStageReply%
Result (2
.wrpc.RewardsRResult"N
GetAllStageRankInfoRequest
UID (RUID

UserConnID (R
UserConnID"G
GetAllStageRankInfoReply+
Result (2.wrpc.LevelRankInfoRResult"q
ChangeNameRequest
UID (RUID

UserConnID (R
UserConnID
Name (	RName
IsFree (RIsFree")
ChangeNameReply
Result (RResult"c
ChangeAvatarRequest
UID (RUID

UserConnID (R
UserConnID
AvatarId (RAvatarId"+
ChangeAvatarReply
Result (RResult"�
CollectMonsterBookRewardRequest
UID (RUID

UserConnID (R
UserConnID
	MonsterId (R	MonsterId
Status (RStatus"�
CollectMonsterBookRewardReplyG
Result (2/.wrpc.CollectMonsterBookRewardReply.ResultEntryRResult9
ResultEntry
key (Rkey
value (Rvalue:8"_
HeroBeKilledRequest
UID (RUID

UserConnID (R
UserConnID
HeroId (RHeroId"+
HeroBeKilledReply
Result (RResult"G
GetDailyTaskRequest
UID (RUID

UserConnID (R
UserConnID">
GetDailyTaskReply)
Result (2.wrpc.DailyResultRResult"k
CollectDailyTaskRewardRequest
UID (RUID

UserConnID (R
UserConnID
QuestId (RQuestId"D
CollectDailyTaskRewardReply%
Result (2
.wrpc.RewardsRResult"l
CollectDailyChestRewardRequest
UID (RUID

UserConnID (R
UserConnID
QuestId (RQuestId"E
CollectDailyChestRewardReply%
Result (2
.wrpc.RewardsRResult"F
GetMailListRequest
UID (RUID

UserConnID (R
UserConnID"C
GetMailListReply/
Result (2.wrpc.GetMailListResultRResult"J
GetAllianceInfoRequest
UID (RUID

UserConnID (R
UserConnID"E
GetAllianceInfoReply-
Result (2.wrpc.AllianceInfoRetRResult"�
CreateAllianceRequest
UID (RUID

UserConnID (R
UserConnID
Name (	RName&
RecruitSetting (RRecruitSetting
FlagBase	 (RFlagBase

FlagEmblem
 (R
FlagEmblem
IsFree (RIsFree"D
CreateAllianceReply-
Result (2.wrpc.AllianceInfoRetRResult"l
ApplyJoinAllianceRequest
UID (RUID

UserConnID (R
UserConnID

AllianceId (R
AllianceId"G
ApplyJoinAllianceReply-
Result (2.wrpc.AllianceInfoRetRResult"r
GetAllianceMembersInfosRequest
UID (RUID

UserConnID (R
UserConnID

AllianceId (R
AllianceId"X
GetAllianceMembersInfosReply8
Result (2 .wrpc.GetAllianceMembersInfosRetRResult"H
LeaveAllianceRequest
UID (RUID

UserConnID (R
UserConnID",
LeaveAllianceReply
Result (RResult"�
EditAllianceNameRequest
UID (RUID

UserConnID (R
UserConnID

AllianceId (R
AllianceId
Name (	RName
IsFree	 (RIsFree"/
EditAllianceNameReply
Result (RResult"�
EditAllianceAcronymRequest
UID (RUID

UserConnID (R
UserConnID

AllianceId (R
AllianceId
Name (	RName
IsFree	 (RIsFree"2
EditAllianceAcronymReply
Result (RResult"�
EditRecruitSettingRequest
UID (RUID

UserConnID (R
UserConnID

AllianceId (R
AllianceId
Status (RStatus&
PowerCondition	 (RPowerCondition,
MaxStageCondition
 (RMaxStageCondition"1
EditRecruitSettingReply
Result (RResult"�
EditAllianceStepNameRequest
UID (RUID

UserConnID (R
UserConnID

AllianceId (R
AllianceIdK
StepName (2/.wrpc.EditAllianceStepNameRequest.StepNameEntryRStepName;

StepNameEntry
key (Rkey
value (	Rvalue:8"3
EditAllianceStepNameReply
Result (RResult"�
EditAllianceFlagRequest
UID (RUID

UserConnID (R
UserConnID

AllianceId (R
AllianceId
FlagBase (RFlagBase

FlagEmblem	 (R
FlagEmblem"/
EditAllianceFlagReply
Result (RResult"f
TransferPresidentRequest
UID (RUID

UserConnID (R
UserConnID
DestUid (RDestUid"0
TransferPresidentReply
Result (RResult"a
RemoveMemberRequest
UID (RUID

UserConnID (R
UserConnID
DestUid (RDestUid"+
RemoveMemberReply
Result (RResult"y
ChangeMemberStepRequest
UID (RUID

UserConnID (R
UserConnID
DestUid (RDestUid
Step (RStep"/
ChangeMemberStepReply
Result (RResult"M
GetAllianceAppListRequest
UID (RUID

UserConnID (R
UserConnID"S
GetAllianceAppListReply8
Result (2 .wrpc.GetAllianceMembersInfosRetRResult"�
HandleAllianceAppRequest
UID (RUID

UserConnID (R
UserConnID
DestUid (RDestUid
IsAgree (RIsAgree"0
HandleAllianceAppReply
Result (RResult"J
DisbandAllianceRequest
UID (RUID

UserConnID (R
UserConnID".
DisbandAllianceReply
Result (RResult"�
EditAllianceNoticeRequest
UID (RUID

UserConnID (R
UserConnID

AllianceId (R
AllianceId
Notice (	RNotice"1
EditAllianceNoticeReply
Result (RResult"J
GetAllianceListRequest
UID (RUID

UserConnID (R
UserConnID"H
GetAllianceListReply0
Result (2.wrpc.GetAllianceListRetRResult"m
CancerJoinAllianceRequest
UID (RUID

UserConnID (R
UserConnID

AllianceId (R
AllianceId"1
CancerJoinAllianceReply
Result (RResult"�
BuyAllianceShopRequest
UID (RUID

UserConnID (R
UserConnID 
CommodityId (RCommodityId
Amount (RAmount".
BuyAllianceShopReply
Result (RResult"n
 CollectAllianceTaskRewardRequest
UID (RUID

UserConnID (R
UserConnID
QuestId (RQuestId"G
CollectAllianceTaskRewardReply%
Result (2
.wrpc.RewardsRResult"o
!CollectAllianceChestRewardRequest
UID (RUID

UserConnID (R
UserConnID
QuestId (RQuestId"H
CollectAllianceChestRewardReply%
Result (2
.wrpc.RewardsRResult"d
GetUserInfoListRequest
UID (RUID

UserConnID (R
UserConnID
UidList (RUidList"A
GetUserInfoListReply)
Result (2.wrpc.GetUserInfoRResult"_
AddFriendsRequest
UID (RUID

UserConnID (R
UserConnID
DestUid (RDestUid")
AddFriendsReply
Result (RResult"_
DelFriendsRequest
UID (RUID

UserConnID (R
UserConnID
DestUid (RDestUid")
DelFriendsReply
Result (RResult"I
GetFriendsListRequest
UID (RUID

UserConnID (R
UserConnID"@
GetFriendsListReply)
Result (2.wrpc.GetUserInfoRResult"a
AddBlackListRequest
UID (RUID

UserConnID (R
UserConnID
DestUid (RDestUid"+
AddBlackListReply
Result (RResult"a
DelBlackListRequest
UID (RUID

UserConnID (R
UserConnID
DestUid (RDestUid"+
DelBlackListReply
Result (RResult"G
GetBlackListRequest
UID (RUID

UserConnID (R
UserConnID">
GetBlackListReply)
Result (2.wrpc.GetUserInfoRResult"n
"GetFriendRecommendationListRequest
UID (RUID

UserConnID (R
UserConnID
Filter (	RFilter"M
 GetFriendRecommendationListReply)
Result (2.wrpc.GetUserInfoRResult"K
GetFriendAppListRequest
UID (RUID

UserConnID (R
UserConnID"B
GetFriendAppListReply)
Result (2.wrpc.GetUserInfoRResult"~
HandleFriendAppRequest
UID (RUID

UserConnID (R
UserConnID
DestUid (RDestUid
IsAgree (RIsAgree".
HandleFriendAppReply
Result (RResult"�
 SettingAddFriendConditionRequest
UID (RUID

UserConnID (R
UserConnID
IsAllow (RIsAllow

StageLimit (R
StageLimit"8
SettingAddFriendConditionReply
Result (RResult"^
HookSendMessageRequest
UID (RUID

UserConnID (R
UserConnID
Type (RType".
HookSendMessageReply
Result (RResult"�
SubmitOrderRequest
UID (RUID

UserConnID (R
UserConnID
	PackageId (R	PackageId
OrderId (	ROrderId
	ProductId	 (	R	ProductId
Seq
 (RSeq"*
SubmitOrderReply
Result (RResult"K
GetPowerRankInfoRequest
UID (RUID

UserConnID (R
UserConnID"D
GetPowerRankInfoReply+
Result (2.wrpc.PowerRankInfoRResult"h
UpgradeLordEquipLevelRequest
UID (RUID

UserConnID (R
UserConnID
TypeId (RTypeId"4
UpgradeLordEquipLevelReply
Result (RResult"o
GemCraftRequest
UID (RUID

UserConnID (R
UserConnID*
Crafts (2.wrpc.LordGemCraftRCrafts"'

GemCraftReply
Result (RResult"[
EnhanceGemRequest
UID (RUID

UserConnID (R
UserConnID
GemId (RGemId")
EnhanceGemReply
Result (RResult"k
EquipGemRequest
UID (RUID

UserConnID (R
UserConnID
GemId (RGemId
Pos (RPos"'

EquipGemReply
Result (RResult"q
UnEquipGemRequest
UID (RUID

UserConnID (R
UserConnID
EquipId (REquipId
Pos (RPos")
UnEquipGemReply
Result (RResult"h
UpgradeLordEquipGradeRequest
UID (RUID

UserConnID (R
UserConnID
TypeId (RTypeId"4
UpgradeLordEquipGradeReply
Result (RResult"X
LockGemRequest
UID (RUID

UserConnID (R
UserConnID
GemId (RGemId"&
LockGemReply
Result (RResult"Z
UnlockGemRequest
UID (RUID

UserConnID (R
UserConnID
GemId (RGemId"(
UnlockGemReply
Result (RResult"Y
SwitchEquipGemRequest
UID (RUID

UserConnID (R
UserConnID
Id (RId"-
SwitchEquipGemReply
Result (RResult"�
LordGemRandomRequest
UID (RUID

UserConnID (R
UserConnID
ConfigId (RConfigId
Amount (RAmount
IsFree	 (RIsFree";
LordGemRandomReply%
Result (2
.wrpc.RewardsRResult"}
HeroUpgradeQualityRequest
UID (RUID

UserConnID (R
UserConnID
HeroId (RHeroId
StarId (RStarId"1
HeroUpgradeQualityReply
Result (RResult"�
SweepDungeonRequest
UID (RUID

UserConnID (R
UserConnID 
DungeonType (RDungeonType
	DungeonId (R	DungeonId":
SweepDungeonReply%
Result (2
.wrpc.RewardsRResult"�
SelectDungeonRougeTabRequest
UID (RUID

UserConnID (R
UserConnID 
DungeonType (RDungeonType
SkillId (RSkillId"4
SelectDungeonRougeTabReply
Result (RResult"�
StartDungeonRequest
UID (RUID

UserConnID (R
UserConnID 
DungeonType (RDungeonType
	DungeonId (R	DungeonId"+
StartDungeonReply
Result (RResult"o
RefreshDungeonRougeTabRequest
UID (RUID

UserConnID (R
UserConnID
	RougeType (R	RougeType"5
RefreshDungeonRougeTabReply
Result (RResult"�
SaveFunctionOpenRequest
UID (RUID

UserConnID (R
UserConnID

FunctionId (R
FunctionId
Status (RStatus"/
SaveFunctionOpenReply
Result (RResult"�
SaveGuideProgressRequest
UID (RUID

UserConnID (R
UserConnID
GuildId (RGuildId
Progress (RProgress"0
SaveGuideProgressReply
Result (RResult"J
GetActivityListRequest
UID (RUID

UserConnID (R
UserConnID"B
GetActivityListReply*
Result (2.wrpc.ActivityInfoRResult"M
CollectSign7RewardRequest
UID (RUID

UserConnID (R
UserConnID"@
CollectSign7RewardReply%
Result (2
.wrpc.RewardsRResult"f
CollectDay7RewardRequest
UID (RUID

UserConnID (R
UserConnID
QuestId (RQuestId"?
CollectDay7RewardReply%
Result (2
.wrpc.RewardsRResult"k
CollectDay7ChestRewardRequest
UID (RUID

UserConnID (R
UserConnID
ChestId (RChestId"D
CollectDay7ChestRewardReply%
Result (2
.wrpc.RewardsRResult"g
SelectDay7RewardRequest
UID (RUID

UserConnID (R
UserConnID
SelectId (RSelectId"/
SelectDay7RewardReply
Result (RResult"d
CollectMailRewardRequest
UID (RUID

UserConnID (R
UserConnID
MailId (RMailId"?
CollectMailRewardReply%
Result (2
.wrpc.RewardsRResult"[
ReadMailRequest
UID (RUID

UserConnID (R
UserConnID
MailId (RMailId"'

ReadMailReply
Result (RResult"Z
DelMailRequest
UID (RUID

UserConnID (R
UserConnID
MailId (RMailId"&
DelMailReply
Result (RResult"L
DeleteAllReadMailRequest
UID (RUID

UserConnID (R
UserConnID"0
DeleteAllReadMailReply
Result (RResult"P
ReadAndCollectAllMailRequest
UID (RUID

UserConnID (R
UserConnID"C
ReadAndCollectAllMailReply%
Result (2
.wrpc.RewardsRResult"j
CollectGrowthFundRewardRequest
UID (RUID

UserConnID (R
UserConnID
FundId (RFundId"E
CollectGrowthFundRewardReply%
Result (2
.wrpc.RewardsRResult"}
GetFirstChargeRewardRequest
UID (RUID

UserConnID (R
UserConnID
ChargeId (RChargeId
Day (RDay"B
GetFirstChargeRewardReply%
Result (2
.wrpc.RewardsRResult"i
ReceiveMonthCardRewardRequest
UID (RUID

UserConnID (R
UserConnID
CardId (RCardId"D
ReceiveMonthCardRewardReply%
Result (2
.wrpc.RewardsRResult"U
!CollectDailySaleFreeRewardRequest
UID (RUID

UserConnID (R
UserConnID"H
CollectDailySaleFreeRewardReply%
Result (2
.wrpc.RewardsRResult"n
 CollectDailyWeekTaskChestRequest
UID (RUID

UserConnID (R
UserConnID
QuestId (RQuestId"G
CollectDailyWeekTaskChestReply%
Result (2
.wrpc.RewardsRResult"H
InitSymbioticRequest
UID (RUID

UserConnID (R
UserConnID"D
InitSymbioticReply.
Result (2.wrpc.InitSymbioticRetRResult"�
SwitchSymbioticHeroRequest
UID (RUID

UserConnID (R
UserConnID
InHeroId (RInHeroId
	OutHeroId (R	OutHeroId"2
SwitchSymbioticHeroReply
Result (RResult"q
#CollectAchievementTaskRewardRequest
UID (RUID

UserConnID (R
UserConnID
QuestId (RQuestId"J
!CollectAchievementTaskRewardReply%
Result (2
.wrpc.RewardsRResult"�
OneKeyUpgradeHeroRequest
UID (RUID

UserConnID (R
UserConnID7
HeroId (2.wrpc.OneKeyHeroLevelUpgradeRetRHeroId"0
OneKeyUpgradeHeroReply
Result (RResult"~
OneKeyEquipGemRequest
UID (RUID

UserConnID (R
UserConnID3
GemInfos (2.wrpc.OneKeyEquipGemReqRGemInfos"-
OneKeyEquipGemReply
Result (RResult"e
CollectGroupRewardRequest
UID (RUID

UserConnID (R
UserConnID
MainId (RMainId"�
CollectGroupRewardReplyA
Result (2).wrpc.CollectGroupRewardReply.ResultEntryRResult9
ResultEntry
key (Rkey
value (Rvalue:8"O
PushMsgsNtf
MsgType (
RMsgType
MsgId (RMsgId
Msg	 (	RMsg"Q

PushPBMsgsNtf
MsgType (
RMsgType
MsgId (RMsgId
Msg	 (RMsg"?
PushPBTopicMsgNtf
MsgType (
RMsgType
Msg (	RMsg"@
PushOrderFinishNtf
OrderId (ROrderId
Seq (RSeqB�������� �� �� �� bproto3
�
common.protowrpc-github.com/gogo/protobuf/gogoproto/gogo.proto"�
SimpleErrorNtf
Err (	RErr
ErrCode (RErrCode
Details (	RDetails
Magic (RMagic"
Troublemaker (RTroublemaker"�
MulticastErrorNtf
Err (	RErr"
StargateName (	RStargateName*
StargateNameHash (RStargateNameHash 
UserConnIDs (RUserConnIDs"
Troublemaker (RTroublemaker
Memo (RMemo"�
UserOnlineRequest
UID (RUID 
Fingerprint (RFingerprint
N64 (RN64
N32 (RN32&
WantOfflineNtf (RWantOfflineNtf&
TrustClientN32 (RTrustClientN32S
AddToWarehouse (2+.wrpc.UserOnlineRequest.AddToWarehouseEntryRAddToWarehouseA
AddToWarehouseEntry
key (	Rkey
value (	Rvalue:8"3
UserOnlineReply 
Placeholder (RPlaceholder"o
UpdateUserTopicsRequest
UID (RUID
ToAdd (	RToAdd
ToRemove (	RToRemove
N64 (RN64"9
UpdateUserTopicsReply 
Placeholder (RPlaceholder"�
KickOutRequest
UID (RUID

UserConnID (R
UserConnID
Sig (RSig 
MsgToClient (	RMsgToClient$

MsgToStargate (	R
MsgToStargate
	RogueConn (R	RogueConn
Force (RForce"0
KickOutReply 
Placeholder (RPlaceholder"�
UserOfflineNtf
UID (RUID"
StargateName (	RStargateName

UserConnID (R
UserConnID
Lifetime (RLifetime
N32 (RN32*
StargateNameHash (RStargateNameHash8
TotalDownstreamMessages (RTotalDownstreamMessages2
TotalDownstreamBytes	 (RTotalDownstreamBytes4
TotalUpstreamMessages
 (RTotalUpstreamMessages.
TotalUpstreamBytes (RTotalUpstreamBytes"?
WantUserOfflineNtfRequest
UID (RUID
N64 (RN64";
WantUserOfflineNtfReply 
Placeholder (RPlaceholder"�
UpdateUserConnWarehouseRequest
UID (RUIDN
ToUpdate (22.wrpc.UpdateUserConnWarehouseRequest.ToUpdateEntryRToUpdate
ToRemove (	RToRemove
N64 (RN64;

ToUpdateEntry
key (	Rkey
value (	Rvalue:8"@
UpdateUserConnWarehouseReply 
Placeholder (RPlaceholder"
EchoRequest"-
	EchoReply 
Placeholder (RPlaceholder"E
!PerformUserConnHealthcheckRequest 
UserConnIDs (RUserConnIDs"�
PerformUserConnHealthcheckReply 
UserConnIDs (RUserConnIDs
Results (RResults"
StargateName (	RStargateName*
StargateNameHash (RStargateNameHash"�
SyncServerLoadNtf
Name (	RName
NameHash (RNameHash 
OnlineUsers	 (ROnlineUsers.
OnlineUserCapacity
 (ROnlineUserCapacity
CPU (RCPUB�������� �� �� �� bproto3
�"
tcpdump.proto.4tcpdumptcpdump*�"
RPC4TCPDump
RPC4TCPDumpNone 
wrpc_AddBlackList�
wrpc_AddBuilding�
wrpc_AddFriends�
wrpc_ApplyJoinAlliance�
wrpc_BuildWorkHero�
wrpc_BuildingWorkVillage�
wrpc_BuyAllianceShop�
wrpc_CancerJoinAlliance�
wrpc_CancerResearch�
wrpc_ChangeAvatar�
wrpc_ChangeMemberStep�
wrpc_ChangeName�&
!wrpc_CollectAchievementTaskReward�$
wrpc_CollectAllianceChestReward�#
wrpc_CollectAllianceTaskReward�!
wrpc_CollectDailyChestReward�$
wrpc_CollectDailySaleFreeReward� 
wrpc_CollectDailyTaskReward�#
wrpc_CollectDailyWeekTaskChest� 
wrpc_CollectDay7ChestReward�
wrpc_CollectDay7Reward� 
wrpc_CollectFirstPassReward�
wrpc_CollectGroupReward�!
wrpc_CollectGrowthFundReward�%
 wrpc_CollectHeroLotteryAccReward�'
"wrpc_CollectHeroLotteryLevelReward�
wrpc_CollectIdleReward�
wrpc_CollectMailReward�
wrpc_CollectMainReward�!
wrpc_CollectMapChapterReward�"
wrpc_CollectMonsterBookReward�#
wrpc_CollectPhotovoltaicReward�
wrpc_CollectResource�
wrpc_CollectSign7Reward�"
wrpc_CollectStageLevelRewards�
wrpc_CollectStageReward�
wrpc_CreateAlliance�

wrpc_Debug�
wrpc_DebugAddItem�
wrpc_DelBlackList�
wrpc_DelFriends�
wrpc_DelMail�
wrpc_DeleteAllReadMail�
wrpc_DisbandAlliance�
	wrpc_Echok
wrpc_EditAllianceAcronym�
wrpc_EditAllianceFlag�
wrpc_EditAllianceName�
wrpc_EditAllianceNotice�
wrpc_EditAllianceStepName�
wrpc_EditRecruitSetting�
wrpc_EnhanceGem�

wrpc_EquipGem�
wrpc_FinishBuilding�
wrpc_FinishDungeonStage�
wrpc_FinishMainStage�
wrpc_FinishMapEvent�
wrpc_FinishResearch�
wrpc_FinishTrain�

wrpc_GemCraft�
wrpc_GetActivityList�
wrpc_GetAllStageRankInfo�
wrpc_GetAllianceAppList�
wrpc_GetAllianceInfo�
wrpc_GetAllianceList�!
wrpc_GetAllianceMembersInfos�
wrpc_GetBlackList�
wrpc_GetDailyTask�
wrpc_GetFirstChargeReward�
wrpc_GetFriendAppList�%
 wrpc_GetFriendRecommendationList�
wrpc_GetFriendsList�
wrpc_GetGiftList�
wrpc_GetIdleReward�
wrpc_GetMailList�
wrpc_GetManifest�
wrpc_GetPhotovoltaicReward�
wrpc_GetPowerRankInfo�
wrpc_GetStageRankInfo�#
wrpc_GetStageRankInfoByStageId�
wrpc_GetUserInfoList�
wrpc_HandleAllianceApp�
wrpc_HandleFriendApp�
wrpc_HeartBeat�
wrpc_HelloWorld�
wrpc_HeroBeKilled�
wrpc_HeroLottery�
wrpc_HeroUpgradeGene�
wrpc_HeroUpgradeLevel�
wrpc_HeroUpgradeQuality�
wrpc_HeroUpgradeStar�
wrpc_HookSendMessage�
wrpc_InitSymbiotic�
wrpc_KickOutg
wrpc_KillMonster�
wrpc_LeaveAlliance�
wrpc_LockGem�
wrpc_LockHeroDice�

wrpc_Login�
wrpc_LordGemRandom�
wrpc_MulticastError	
wrpc_OnBackCity�
wrpc_OneKeyEquipGem�
wrpc_OneKeyUpgradeHero�#
wrpc_PerformUserConnHealthcheckl
wrpc_PrepareOrder�

wrpc_PushMsgs�.
wrpc_PushOrderFinish�.
wrpc_PushPBMsgs�.
wrpc_PushPBTopicMsg�.
wrpc_RandomHeroDice�
wrpc_ReadAndCollectAllMail�

wrpc_ReadMail� 
wrpc_ReceiveMonthCardReward� 
wrpc_RefreshDungeonRougeTab�
wrpc_RefreshRougeSkill�
wrpc_RegisterUser�
wrpc_RemoveMember�
wrpc_RequestDispatcher�
wrpc_ResetMainStage�
wrpc_SaveFunctionOpen�
wrpc_SaveGuideProgress�
wrpc_SelectDay7Reward�
wrpc_SelectDungeonRougeTab�
wrpc_SelectEliteRougeSkill�
wrpc_SelectRougeSkill�
wrpc_SetDefaultBattlePos�
wrpc_SetHeroBattlePos�
wrpc_SetHeroTroop�#
wrpc_SettingAddFriendCondition�
wrpc_SimpleError
wrpc_StartDungeon�
wrpc_StartMainStage�
wrpc_StartResearch�
wrpc_SubmitOrder�
wrpc_SweepDungeon�
wrpc_SweepMainStage�
wrpc_SwitchEquipGem�
wrpc_SwitchSymbioticHero�
wrpc_SyncServerLoadm
wrpc_TrainTroops�
wrpc_TransferPresident�
wrpc_UnEquipGem�
wrpc_UnlockGem� 
wrpc_UpdateUserConnWarehousej
wrpc_UpdateUserTopicse
wrpc_UpgradeBuilding�
wrpc_UpgradeDaveLevel�
wrpc_UpgradeLordEquipGrade�
wrpc_UpgradeLordEquipLevel�
wrpc_UpgradeSkillLevel�
wrpc_UploadDeviceInfo�

wrpc_UserItem�
wrpc_UserOfflineh
wrpc_UserOnlined
wrpc_VerifyUser�
wrpc_WantUserOfflineNtfibproto3