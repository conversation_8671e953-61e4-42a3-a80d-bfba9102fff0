// Code generated by wrpc. DO NOT EDIT.
// source: knights.wrpc

syntax = "proto3";

package wrpc;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";

option (gogoproto.marshaler_all) = true;
option (gogoproto.unmarshaler_all) = true;
option (gogoproto.sizer_all) = true;
option (gogoproto.goproto_getters_all) = false;
option (gogoproto.goproto_unrecognized_all) = false;
option (gogoproto.goproto_unkeyed_all) = false;
option (gogoproto.goproto_sizecache_all) = false;

import "pb1.proto";

// RequestDispatcher [501]...
message RequestDispatcherRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	string MethodName = 7;
	RequestParams Params = 8;
}

// RequestDispatcher [501]...
message RequestDispatcherReply {
	RequestParams Result = 7;
}

// RegisterUser [1001]...
message RegisterUserRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	uint32 WorldId = 7;
}

// RegisterUser [1001]...
message RegisterUserReply {
	UserData Result = 7;
}

message LoginRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 WorldId = 7;
	string AccountId = 8;
	string Ticket = 9;
	string Version = 10;
}

message LoginReply {
	UserData Result = 7;
}

message HelloWorldRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	string Test = 7;
}

message HelloWorldReply {
	string Result = 7;
}

message VerifyUserRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 Fpid = 7;
	int64 ConnectId = 8;
	int64 Fingerprint = 9;
	string AccountId = 10;
	string Ticket = 11;
}

message VerifyUserReply {
	bool Result = 7;
}

message AddBuildingRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 ConfigId = 7;
}

message AddBuildingReply {
	int64 Result = 7;
}

//升级建筑
message UpgradeBuildingRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 BuildingId = 7;
	bool UseDiamond = 8;
}

//升级建筑
message UpgradeBuildingReply {
	bool Result = 7;
}

//英雄招募
message HeroLotteryRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 ConfigId = 7;
	int64 Amount = 8;
	bool IsFree = 9;
}

//英雄招募
message HeroLotteryReply {
	repeated Rewards Result = 7;
}

//英雄升级
message HeroUpgradeLevelRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 HeroId = 7;
	int32 Amount = 8;
	bool IsFree = 9;
}

//英雄升级
message HeroUpgradeLevelReply {
	bool Result = 7;
}

//英雄升星
message HeroUpgradeStarRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 HeroId = 7;
	int32 Amount = 8;
}

//英雄升星
message HeroUpgradeStarReply {
	bool Result = 7;
}

//添加道具
message DebugAddItemRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 ItemId = 7;
	int64 Amount = 8;
}

//添加道具
message DebugAddItemReply {
	bool Result = 7;
}

//派驻英雄
message BuildWorkHeroRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 BuildingId = 7;
	int32 HeroId = 8;
	bool IsWork = 9;
}

//派驻英雄
message BuildWorkHeroReply {
	bool Result = 7;
}

//采集资源
message CollectResourceRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 BuildingId = 7;
}

//采集资源
message CollectResourceReply {
	int64 Result = 7;
}

//英雄上阵
message SetHeroBattlePosRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 PosId = 7;
	int32 HeroId = 8;
}

//英雄上阵
message SetHeroBattlePosReply {
	bool Result = 7;
}

//设置英雄带兵
message SetHeroTroopRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 HeroId = 7;
	int64 Num = 8;
}

//设置英雄带兵
message SetHeroTroopReply {
	bool Result = 7;
}

//设置默认镜头
message SetDefaultBattlePosRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 PosId = 7;
}

//设置默认镜头
message SetDefaultBattlePosReply {
	bool Result = 7;
}

//领取招募累计次数奖励
message CollectHeroLotteryAccRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 ConfigId = 7;
}

//领取招募累计次数奖励
message CollectHeroLotteryAccRewardReply {
	map<int32, int64> Result = 7;
}

//领取招募等级奖励
message CollectHeroLotteryLevelRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	repeated int32 LevelId = 7;
}

//领取招募等级奖励
message CollectHeroLotteryLevelRewardReply {
	map<int32, int64> Result = 7;
}

//随机潜力
message RandomHeroDiceRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 HeroId = 7;
}

//随机潜力
message RandomHeroDiceReply {
	repeated int32 Result = 7;
}

//设置潜力锁
message LockHeroDiceRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 HeroId = 7;
	int32 Slot = 8;
	bool Lock = 9;
}

//设置潜力锁
message LockHeroDiceReply {
	bool Result = 7;
}

//造兵
message TrainTroopsRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 ConfigId = 7;
	int64 Amount = 8;
	bool UseDiamond = 9;
}

//造兵
message TrainTroopsReply {
	bool Result = 7;
}

//使用道具
message UserItemRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 ItemId = 7;
	int64 Amount = 8;
	int32 Type = 9;
	int64 Para1 = 10;
}

//使用道具
message UserItemReply {
	bool Result = 7;
}

//派遣农民
message BuildingWorkVillageRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 BuildingId = 7;
	int32 VillageId = 8;
	bool IsWork = 9;
}

//派遣农民
message BuildingWorkVillageReply {
	bool Result = 7;
}

//完成挑战
message FinishDungeonStageRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 DungeonType = 7;
	int32 DungeonId = 8;
	bool IsWin = 9;
	int32 EstimateTime = 10;
}

//完成挑战
message FinishDungeonStageReply {
	repeated Rewards Result = 7;
}

//领取首胜奖励
message CollectFirstPassRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 DungeonId = 7;
}

//领取首胜奖励
message CollectFirstPassRewardReply {
	bool Result = 7;
}

//开始科技研究
message StartResearchRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 TechId = 7;
	bool UseDiamond = 8;
}

//开始科技研究
message StartResearchReply {
	bool Result = 7;
}

//取消研究
message CancerResearchRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 TechId = 7;
}

//取消研究
message CancerResearchReply {
	bool Result = 7;
}

//领取主线任务奖励
message CollectMainRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 QuestId = 7;
}

//领取主线任务奖励
message CollectMainRewardReply {
	map<int32, int64> Result = 7;
}

//领取地图探索值奖励
message CollectMapChapterRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 RewardId = 7;
}

//领取地图探索值奖励
message CollectMapChapterRewardReply {
	bool Result = 7;
}

//建筑升级完成
message FinishBuildingRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 BuildingId = 7;
}

//建筑升级完成
message FinishBuildingReply {
	bool Result = 7;
}

//研究完成
message FinishResearchRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 TechId = 7;
}

//研究完成
message FinishResearchReply {
	bool Result = 7;
}

//训练完成
message FinishTrainRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 TroopType = 7;
}

//训练完成
message FinishTrainReply {
	bool Result = 7;
}

//完成地图事件
message FinishMapEventRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 EventId = 7;
	int32 Pos = 8;
}

//完成地图事件
message FinishMapEventReply {
	finishEventReturn Result = 7;
}

//debug接口
message DebugRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 Type = 7;
	int64 Para1 = 8;
}

//debug接口
message DebugReply {
	bool Result = 7;
}

//玩家回程
message OnBackCityRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//玩家回程
message OnBackCityReply {
	bool Result = 7;
}

//升级英雄技能
message UpgradeSkillLevelRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 HeroId = 7;
	int32 GroupId = 8;
}

//升级英雄技能
message UpgradeSkillLevelReply {
	bool Result = 7;
}

//升级戴夫等级
message UpgradeDaveLevelRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//升级戴夫等级
message UpgradeDaveLevelReply {
	bool Result = 7;
}

//完成主线关卡 stageId 关卡id ，is_fail 挑战是否失败 ，is_elite 是否精英关卡 perfect_index 1:通关 2:二星通关 3:三星通关, max_hp_percent 0.12, max_time 0-10000
message FinishMainStageRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	LevelStruct LevelStruct = 7;
	bool IsFail = 8;
	int32 StageId = 9;
	bool IsElite = 10;
	int32 PerfectIndex = 11;
	float MaxHpPercent = 12;
	int32 MaxTime = 13;
	map<int32, float> HeroHealth = 14;
	map<int32, float> HeroAtk = 15;
	map<int32, float> BossProgress = 16;
}

//完成主线关卡 stageId 关卡id ，is_fail 挑战是否失败 ，is_elite 是否精英关卡 perfect_index 1:通关 2:二星通关 3:三星通关, max_hp_percent 0.12, max_time 0-10000
message FinishMainStageReply {
	repeated Rewards Result = 7;
}

//领取挂机奖励
message CollectIdleRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//领取挂机奖励
message CollectIdleRewardReply {
	map<int32, int64> Result = 7;
}

//重置主线关卡
message ResetMainStageRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 StageId = 7;
}

//重置主线关卡
message ResetMainStageReply {
	bool Result = 7;
}

//击杀怪物
message KillMonsterRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	repeated int32 MonsterId = 7;
	int32 DungeonType = 8;
}

//击杀怪物
message KillMonsterReply {
	KillMonsterRet Result = 7;
}

//开始主线关卡
message StartMainStageRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 StageId = 7;
	bool IsElite = 8;
}

//开始主线关卡
message StartMainStageReply {
	bool Result = 7;
}

//获取挂机奖励信息
message GetIdleRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//获取挂机奖励信息
message GetIdleRewardReply {
	GetRewardReturn Result = 7;
}

//获取manifest信息
message GetManifestRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//获取manifest信息
message GetManifestReply {
	ManiFest Result = 7;
}

//上传设备信息
message UploadDeviceInfoRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	string DeviceInfo = 7;
}

//上传设备信息
message UploadDeviceInfoReply {
	bool Result = 7;
}

//局内刷新技能
message RefreshRougeSkillRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 RougeId = 7;
	int32 RougeType = 8;
}

//局内刷新技能
message RefreshRougeSkillReply {
	repeated int32 Result = 7;
}

//心跳
message HeartBeatRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//心跳
message HeartBeatReply {
	bool Result = 7;
}

//选取局内卡牌
message SelectRougeSkillRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 SkillId = 7;
}

//选取局内卡牌
message SelectRougeSkillReply {
	bool Result = 7;
}

//领取关卡奖励 1:通关 2:二星通关 3:三星通关
message CollectStageRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 StageId = 7;
	int32 RewardIndex = 8;
}

//领取关卡奖励 1:通关 2:二星通关 3:三星通关
message CollectStageRewardReply {
	repeated Rewards Result = 7;
}

//获取礼包列表
message GetGiftListRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//获取礼包列表
message GetGiftListReply {
	repeated GiftStruct Result = 7;
}

//建立订单
message PrepareOrderRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 ProductId = 7;
}

//建立订单
message PrepareOrderReply {
	string Result = 7;
}

//英雄基因升级
message HeroUpgradeGeneRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 HeroId = 7;
}

//英雄基因升级
message HeroUpgradeGeneReply {
	bool Result = 7;
}

//选取精英怪肉鸽卡牌
message SelectEliteRougeSkillRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//选取精英怪肉鸽卡牌
message SelectEliteRougeSkillReply {
	bool Result = 7;
}

//获取关卡排行榜信息
message GetStageRankInfoRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//获取关卡排行榜信息
message GetStageRankInfoReply {
	repeated LevelRankInfo Result = 7;
}

//获取某一关的排行榜信息
message GetStageRankInfoByStageIdRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 StageId = 7;
}

//获取某一关的排行榜信息
message GetStageRankInfoByStageIdReply {
	repeated LevelRankInfo Result = 7;
}

//领取排行榜关卡等级奖励
message CollectStageLevelRewardsRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 RewardId = 7;
}

//领取排行榜关卡等级奖励
message CollectStageLevelRewardsReply {
	repeated Rewards Result = 7;
}

//获取光伏发电信息
message GetPhotovoltaicRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//获取光伏发电信息
message GetPhotovoltaicRewardReply {
	int32 Result = 7;
}

//领取光伏发电奖励
message CollectPhotovoltaicRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//领取光伏发电奖励
message CollectPhotovoltaicRewardReply {
	map<int32, int64> Result = 7;
}

//挂机扫荡
message SweepMainStageRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 Amount = 7;
}

//挂机扫荡
message SweepMainStageReply {
	repeated Rewards Result = 7;
}

//获取所有关的排行榜信息
message GetAllStageRankInfoRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//获取所有关的排行榜信息
message GetAllStageRankInfoReply {
	repeated LevelRankInfo Result = 7;
}

//用户改名
message ChangeNameRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	string Name = 7;
	bool IsFree = 8;
}

//用户改名
message ChangeNameReply {
	int32 Result = 7;
}

//0 改名成功 1 名字重复 2 名字不合法 3 道具不足
//用户改头像
message ChangeAvatarRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 AvatarId = 7;
}

//0 改名成功 1 名字重复 2 名字不合法 3 道具不足
//用户改头像
message ChangeAvatarReply {
	bool Result = 7;
}

//领取怪物图鉴奖励
message CollectMonsterBookRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 MonsterId = 7;
	int32 Status = 8;
}

//领取怪物图鉴奖励
message CollectMonsterBookRewardReply {
	map<int32, int64> Result = 7;
}

// status 0:未解锁 1:已解锁 2:已领取
//英雄被杀死
message HeroBeKilledRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 HeroId = 7;
}

// status 0:未解锁 1:已解锁 2:已领取
//英雄被杀死
message HeroBeKilledReply {
	bool Result = 7;
}

//获取每日任务列表
message GetDailyTaskRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//获取每日任务列表
message GetDailyTaskReply {
	DailyResult Result = 7;
}

//领取每日任务奖励
message CollectDailyTaskRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	repeated int32 QuestId = 7;
}

//领取每日任务奖励
message CollectDailyTaskRewardReply {
	repeated Rewards Result = 7;
}

//领取每日任务宝箱奖励
message CollectDailyChestRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	repeated int32 QuestId = 7;
}

//领取每日任务宝箱奖励
message CollectDailyChestRewardReply {
	repeated Rewards Result = 7;
}

//获取邮件列表
message GetMailListRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//获取邮件列表
message GetMailListReply {
	GetMailListResult Result = 7;
}

//获取联盟信息
message GetAllianceInfoRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//获取联盟信息
message GetAllianceInfoReply {
	AllianceInfoRet Result = 7;
}

//创建联盟
message CreateAllianceRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	string Name = 7;
	int32 RecruitSetting = 8;
	int32 FlagBase = 9;
	int32 FlagEmblem = 10;
	bool IsFree = 11;
}

//创建联盟
message CreateAllianceReply {
	AllianceInfoRet Result = 7;
}

//recruitSetting 0:不需要申请，1：需要申请
//申请加入联盟
message ApplyJoinAllianceRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 AllianceId = 7;
}

//recruitSetting 0:不需要申请，1：需要申请
//申请加入联盟
message ApplyJoinAllianceReply {
	AllianceInfoRet Result = 7;
}

//获取联盟成员信息
message GetAllianceMembersInfosRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 AllianceId = 7;
}

//获取联盟成员信息
message GetAllianceMembersInfosReply {
	GetAllianceMembersInfosRet Result = 7;
}

//退出联盟
message LeaveAllianceRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//退出联盟
message LeaveAllianceReply {
	bool Result = 7;
}

//修改联盟名称
message EditAllianceNameRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 AllianceId = 7;
	string Name = 8;
	bool IsFree = 9;
}

//修改联盟名称
message EditAllianceNameReply {
	bool Result = 7;
}

//修改联盟简称
message EditAllianceAcronymRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 AllianceId = 7;
	string Name = 8;
	bool IsFree = 9;
}

//修改联盟简称
message EditAllianceAcronymReply {
	bool Result = 7;
}

//变更招募信息
message EditRecruitSettingRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 AllianceId = 7;
	int32 Status = 8;
	int32 PowerCondition = 9;
	int32 MaxStageCondition = 10;
}

//变更招募信息
message EditRecruitSettingReply {
	bool Result = 7;
}

//status 0:不需要申请，1：需要申请
//变更阶级头衔
message EditAllianceStepNameRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 AllianceId = 7;
	map<int32, string> StepName = 8;
}

//status 0:不需要申请，1：需要申请
//变更阶级头衔
message EditAllianceStepNameReply {
	bool Result = 7;
}

//自定义旗帜
message EditAllianceFlagRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 AllianceId = 7;
	int32 FlagBase = 8;
	int32 FlagEmblem = 9;
}

//自定义旗帜
message EditAllianceFlagReply {
	bool Result = 7;
}

//转让会长
message TransferPresidentRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 DestUid = 7;
}

//转让会长
message TransferPresidentReply {
	bool Result = 7;
}

//移除成员
message RemoveMemberRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 DestUid = 7;
}

//移除成员
message RemoveMemberReply {
	bool Result = 7;
}

//变更成员阶级
message ChangeMemberStepRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 DestUid = 7;
	int32 Step = 8;
}

//变更成员阶级
message ChangeMemberStepReply {
	bool Result = 7;
}

//获取申请列表
message GetAllianceAppListRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//获取申请列表
message GetAllianceAppListReply {
	GetAllianceMembersInfosRet Result = 7;
}

//处理申请人
message HandleAllianceAppRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 DestUid = 7;
	bool IsAgree = 8;
}

//处理申请人
message HandleAllianceAppReply {
	bool Result = 7;
}

//解散联盟
message DisbandAllianceRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//解散联盟
message DisbandAllianceReply {
	bool Result = 7;
}

//修改联盟公告
message EditAllianceNoticeRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 AllianceId = 7;
	string Notice = 8;
}

//修改联盟公告
message EditAllianceNoticeReply {
	bool Result = 7;
}

//获取联盟列表
message GetAllianceListRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//获取联盟列表
message GetAllianceListReply {
	GetAllianceListRet Result = 7;
}

//撤回联盟申请
message CancerJoinAllianceRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 AllianceId = 7;
}

//撤回联盟申请
message CancerJoinAllianceReply {
	bool Result = 7;
}

//购买联盟商店物品
message BuyAllianceShopRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 CommodityId = 7;
	int32 Amount = 8;
}

//购买联盟商店物品
message BuyAllianceShopReply {
	bool Result = 7;
}

//领取联盟任务奖励
message CollectAllianceTaskRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 QuestId = 7;
}

//领取联盟任务奖励
message CollectAllianceTaskRewardReply {
	repeated Rewards Result = 7;
}

//领取联盟任务宝箱奖励
message CollectAllianceChestRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 QuestId = 7;
}

//领取联盟任务宝箱奖励
message CollectAllianceChestRewardReply {
	repeated Rewards Result = 7;
}

//获取玩家个人信息
message GetUserInfoListRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	repeated int64 UidList = 7;
}

//获取玩家个人信息
message GetUserInfoListReply {
	repeated GetUserInfo Result = 7;
}

//申请添加好友
message AddFriendsRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 DestUid = 7;
}

//申请添加好友
message AddFriendsReply {
	bool Result = 7;
}

//删除好友
message DelFriendsRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 DestUid = 7;
}

//删除好友
message DelFriendsReply {
	bool Result = 7;
}

//获取好友列表
message GetFriendsListRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//获取好友列表
message GetFriendsListReply {
	repeated GetUserInfo Result = 7;
}

//加入黑名单
message AddBlackListRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 DestUid = 7;
}

//加入黑名单
message AddBlackListReply {
	bool Result = 7;
}

//移除黑名单
message DelBlackListRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 DestUid = 7;
}

//移除黑名单
message DelBlackListReply {
	bool Result = 7;
}

//获取黑名单列表
message GetBlackListRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//获取黑名单列表
message GetBlackListReply {
	repeated GetUserInfo Result = 7;
}

//获取好友推荐列表
message GetFriendRecommendationListRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	string Filter = 7;
}

//获取好友推荐列表
message GetFriendRecommendationListReply {
	repeated GetUserInfo Result = 7;
}

//获取好友申请列表
message GetFriendAppListRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//获取好友申请列表
message GetFriendAppListReply {
	repeated GetUserInfo Result = 7;
}

//处理好友申请
message HandleFriendAppRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	repeated int64 DestUid = 7;
	bool IsAgree = 8;
}

//处理好友申请
message HandleFriendAppReply {
	bool Result = 7;
}

//设置加好友条件
message SettingAddFriendConditionRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	bool IsAllow = 7;
	int32 StageLimit = 8;
}

//设置加好友条件
message SettingAddFriendConditionReply {
	bool Result = 7;
}

//频道发言
message HookSendMessageRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 Type = 7;
}

//频道发言
message HookSendMessageReply {
	bool Result = 7;
}

//type =1 好友 ，2 联盟 ，3 世界
//测试礼包
message SubmitOrderRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 PackageId = 7;
	string OrderId = 8;
	string ProductId = 9;
	int32 Seq = 10;
}

//type =1 好友 ，2 联盟 ，3 世界
//测试礼包
message SubmitOrderReply {
	bool Result = 7;
}

//orderId 为 test
//获取力量排行榜信息
message GetPowerRankInfoRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//orderId 为 test
//获取力量排行榜信息
message GetPowerRankInfoReply {
	repeated PowerRankInfo Result = 7;
}

//升级领主装备
message UpgradeLordEquipLevelRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 TypeId = 7;
}

//升级领主装备
message UpgradeLordEquipLevelReply {
	bool Result = 7;
}

//合成宝石
message GemCraftRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	repeated LordGemCraft Crafts = 7;
}

//合成宝石
message GemCraftReply {
	repeated int32 Result = 7;
}

//洗炼宝石
message EnhanceGemRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 GemId = 7;
}

//洗炼宝石
message EnhanceGemReply {
	int32 Result = 7;
}

//装备宝石
message EquipGemRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 GemId = 7;
	int32 Pos = 8;
}

//装备宝石
message EquipGemReply {
	bool Result = 7;
}

//卸下宝石
message UnEquipGemRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 EquipId = 7;
	int32 Pos = 8;
}

//卸下宝石
message UnEquipGemReply {
	bool Result = 7;
}

//装备升阶
message UpgradeLordEquipGradeRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 TypeId = 7;
}

//装备升阶
message UpgradeLordEquipGradeReply {
	bool Result = 7;
}

//锁定宝石
message LockGemRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 GemId = 7;
}

//锁定宝石
message LockGemReply {
	bool Result = 7;
}

//解锁宝石
message UnlockGemRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 GemId = 7;
}

//解锁宝石
message UnlockGemReply {
	bool Result = 7;
}

//切换宝石
message SwitchEquipGemRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 Id = 7;
}

//切换宝石
message SwitchEquipGemReply {
	bool Result = 7;
}

//宝石随机
message LordGemRandomRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 ConfigId = 7;
	int32 Amount = 8;
	bool IsFree = 9;
}

//宝石随机
message LordGemRandomReply {
	repeated Rewards Result = 7;
}

//英雄升品
message HeroUpgradeQualityRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 HeroId = 7;
	int32 StarId = 8;
}

//英雄升品
message HeroUpgradeQualityReply {
	bool Result = 7;
}

//副本扫荡
message SweepDungeonRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 DungeonType = 7;
	int32 DungeonId = 8;
}

//副本扫荡
message SweepDungeonReply {
	repeated Rewards Result = 7;
}

//副本选择肉鸽卡牌
message SelectDungeonRougeTabRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 DungeonType = 7;
	repeated int32 SkillId = 8;
}

//副本选择肉鸽卡牌
message SelectDungeonRougeTabReply {
	bool Result = 7;
}

//开始副本
message StartDungeonRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 DungeonType = 7;
	int32 DungeonId = 8;
}

//开始副本
message StartDungeonReply {
	bool Result = 7;
}

//副本肉鸽卡牌刷新
message RefreshDungeonRougeTabRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 RougeType = 7;
}

//副本肉鸽卡牌刷新
message RefreshDungeonRougeTabReply {
	repeated int32 Result = 7;
}

//保存功能开放
message SaveFunctionOpenRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 FunctionId = 7;
	int32 Status = 8;
}

//保存功能开放
message SaveFunctionOpenReply {
	bool Result = 7;
}

//保存新手引导进度
message SaveGuideProgressRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 GuildId = 7;
	int32 Progress = 8;
}

//保存新手引导进度
message SaveGuideProgressReply {
	bool Result = 7;
}

//获取活动信息
message GetActivityListRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//获取活动信息
message GetActivityListReply {
	repeated ActivityInfo Result = 7;
}

//领取七日签到奖励
message CollectSign7RewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//领取七日签到奖励
message CollectSign7RewardReply {
	repeated Rewards Result = 7;
}

//领取 7 日任务活动奖励
message CollectDay7RewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	repeated int32 QuestId = 7;
}

//领取 7 日任务活动奖励
message CollectDay7RewardReply {
	repeated Rewards Result = 7;
}

//领取 7 日任务宝箱奖励
message CollectDay7ChestRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	repeated int32 ChestId = 7;
}

//领取 7 日任务宝箱奖励
message CollectDay7ChestRewardReply {
	repeated Rewards Result = 7;
}

//选择 7 日任务奖励
message SelectDay7RewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 SelectId = 7;
}

//选择 7 日任务奖励
message SelectDay7RewardReply {
	bool Result = 7;
}

//领取邮件奖励
message CollectMailRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 MailId = 7;
}

//领取邮件奖励
message CollectMailRewardReply {
	repeated Rewards Result = 7;
}

//阅读邮件
message ReadMailRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 MailId = 7;
}

//阅读邮件
message ReadMailReply {
	bool Result = 7;
}

//删除邮件
message DelMailRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int64 MailId = 7;
}

//删除邮件
message DelMailReply {
	bool Result = 7;
}

message DeleteAllReadMailRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

message DeleteAllReadMailReply {
	bool Result = 7;
}

//删除所有已读邮件
message ReadAndCollectAllMailRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//删除所有已读邮件
message ReadAndCollectAllMailReply {
	repeated Rewards Result = 7;
}

//一键阅读并领取邮件
//领取成长基金奖励
message CollectGrowthFundRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 FundId = 7;
}

//一键阅读并领取邮件
//领取成长基金奖励
message CollectGrowthFundRewardReply {
	repeated Rewards Result = 7;
}

//领取首充奖励
message GetFirstChargeRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 ChargeId = 7;
	int32 Day = 8;
}

//领取首充奖励
message GetFirstChargeRewardReply {
	repeated Rewards Result = 7;
}

//领取月卡奖励
message ReceiveMonthCardRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 CardId = 7;
}

//领取月卡奖励
message ReceiveMonthCardRewardReply {
	repeated Rewards Result = 7;
}

//领取每日特惠免费礼包
message CollectDailySaleFreeRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//领取每日特惠免费礼包
message CollectDailySaleFreeRewardReply {
	repeated Rewards Result = 7;
}

//领取每日任务周宝箱奖励
message CollectDailyWeekTaskChestRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	repeated int32 QuestId = 7;
}

//领取每日任务周宝箱奖励
message CollectDailyWeekTaskChestReply {
	repeated Rewards Result = 7;
}

//初始化共生系统
message InitSymbioticRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
}

//初始化共生系统
message InitSymbioticReply {
	InitSymbioticRet Result = 7;
}

//切换共生英雄
message SwitchSymbioticHeroRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 InHeroId = 7;
	int32 OutHeroId = 8;
}

//切换共生英雄
message SwitchSymbioticHeroReply {
	bool Result = 7;
}

//领取成就任务奖励
message CollectAchievementTaskRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 QuestId = 7;
}

//领取成就任务奖励
message CollectAchievementTaskRewardReply {
	repeated Rewards Result = 7;
}

//一键升级英雄
message OneKeyUpgradeHeroRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	repeated OneKeyHeroLevelUpgradeRet HeroId = 7;
}

//一键升级英雄
message OneKeyUpgradeHeroReply {
	bool Result = 7;
}

//一键装备宝石
message OneKeyEquipGemRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	repeated OneKeyEquipGemReq GemInfos = 7;
}

//一键装备宝石
message OneKeyEquipGemReply {
	bool Result = 7;
}

//领取章节总奖励
message CollectGroupRewardRequest {
	int64 UID = 1;
	int64 UserConnID = 5;
	int32 MainId = 7;
}

//领取章节总奖励
message CollectGroupRewardReply {
	map<int32, int64> Result = 7;
}

// PushMsgs [6001]...
message PushMsgsNtf {
	uint32 MsgType = 7;
	int64 MsgId = 8;
	string Msg = 9;
}

// PushPBMsgs [6004]...
message PushPBMsgsNtf {
	uint32 MsgType = 7;
	int64 MsgId = 8;
	bytes Msg = 9;
}

message PushPBTopicMsgNtf {
	uint32 MsgType = 7;
	string Msg = 8;
}

message PushOrderFinishNtf {
	int32 OrderId = 7;
	int32 Seq = 8;
}
