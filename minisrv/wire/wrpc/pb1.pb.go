// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pb1.proto

package wrpc

import (
	minirpc "bitbucket.org/kingsgroup/gog-knights/minirpc"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type RequestParams struct {
	Type                 string   `protobuf:"bytes,1,opt,name=Type,proto3" json:"Type"`
	Data                 []byte   `protobuf:"bytes,2,opt,name=Data,proto3" json:"Data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RequestParams) Reset()         { *m = RequestParams{} }
func (m *RequestParams) String() string { return proto.CompactTextString(m) }
func (*RequestParams) ProtoMessage()    {}
func (*RequestParams) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{0}
}
func (m *RequestParams) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RequestParams) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RequestParams.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RequestParams) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RequestParams.Merge(m, src)
}
func (m *RequestParams) XXX_Size() int {
	return m.Size()
}
func (m *RequestParams) XXX_DiscardUnknown() {
	xxx_messageInfo_RequestParams.DiscardUnknown(m)
}

var xxx_messageInfo_RequestParams proto.InternalMessageInfo

func (m *RequestParams) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *RequestParams) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type UserData struct {
	Ui1                  *minirpc.UserInfo          `protobuf:"bytes,1,opt,name=Ui1,proto3" json:"Ui1"`
	Building             []*minirpc.BuildingInfo    `protobuf:"bytes,2,rep,name=building,proto3" json:"building"`
	Hero                 []*minirpc.HeroInfo        `protobuf:"bytes,3,rep,name=hero,proto3" json:"hero"`
	HeroLottery          *minirpc.HeroLottery       `protobuf:"bytes,4,opt,name=hero_lottery,json=heroLottery,proto3" json:"hero_lottery"`
	Consumables          []*minirpc.Consumable      `protobuf:"bytes,5,rep,name=consumables,proto3" json:"consumables"`
	UserBenefits         []*minirpc.UserBenefit     `protobuf:"bytes,6,rep,name=user_benefits,json=userBenefits,proto3" json:"user_benefits"`
	UserJob              []*minirpc.UserJob         `protobuf:"bytes,7,rep,name=user_job,json=userJob,proto3" json:"user_job"`
	UserTroop            []*minirpc.UserTroop       `protobuf:"bytes,8,rep,name=user_troop,json=userTroop,proto3" json:"user_troop"`
	Research             []*minirpc.Research        `protobuf:"bytes,9,rep,name=research,proto3" json:"research"`
	MainTask             []*minirpc.MainTask        `protobuf:"bytes,10,rep,name=main_task,json=mainTask,proto3" json:"main_task"`
	MapEvent             *minirpc.MapEvent          `protobuf:"bytes,11,opt,name=map_event,json=mapEvent,proto3" json:"map_event"`
	HeroSkill            []*minirpc.HeroSkill       `protobuf:"bytes,12,rep,name=hero_skill,json=heroSkill,proto3" json:"hero_skill"`
	Dave                 *minirpc.Dave              `protobuf:"bytes,13,opt,name=dave,proto3" json:"dave"`
	UserData             []*minirpc.UserData        `protobuf:"bytes,14,rep,name=user_data,json=userData,proto3" json:"user_data"`
	MainLineStage        *minirpc.MainLineStage     `protobuf:"bytes,15,opt,name=main_line_stage,json=mainLineStage,proto3" json:"main_line_stage"`
	CurServerTime        int64                      `protobuf:"varint,16,opt,name=cur_server_time,json=curServerTime,proto3" json:"cur_server_time"`
	IsNewPlayer          bool                       `protobuf:"varint,17,opt,name=is_new_player,json=isNewPlayer,proto3" json:"is_new_player"`
	RtmToken             string                     `protobuf:"bytes,18,opt,name=rtm_token,json=rtmToken,proto3" json:"rtm_token"`
	UserIapBuyLog        *minirpc.UserIapBuyTimes   `protobuf:"bytes,19,opt,name=user_iap_buy_log,json=userIapBuyLog,proto3" json:"user_iap_buy_log"`
	LordEquip            []*minirpc.LordEquip       `protobuf:"bytes,20,rep,name=lord_equip,json=lordEquip,proto3" json:"lord_equip"`
	LordGem              []*minirpc.LordGem         `protobuf:"bytes,21,rep,name=lord_gem,json=lordGem,proto3" json:"lord_gem"`
	LordGemRandom        []*minirpc.LordGemRandom   `protobuf:"bytes,22,rep,name=lord_gem_random,json=lordGemRandom,proto3" json:"lord_gem_random"`
	Dungeon              []*minirpc.Dungeon         `protobuf:"bytes,23,rep,name=dungeon,proto3" json:"dungeon"`
	FunctionOpen         *minirpc.FunctionOpen      `protobuf:"bytes,24,opt,name=function_open,json=functionOpen,proto3" json:"function_open"`
	NewbieGuide          *minirpc.NewbieGuide       `protobuf:"bytes,25,opt,name=newbie_guide,json=newbieGuide,proto3" json:"newbie_guide"`
	ActivityTask         []*minirpc.ActivityTask    `protobuf:"bytes,26,rep,name=activity_task,json=activityTask,proto3" json:"activity_task"`
	FirstCharge          []*minirpc.FirstCharge     `protobuf:"bytes,27,rep,name=first_charge,json=firstCharge,proto3" json:"first_charge"`
	GrowthFund           []*minirpc.GrowthFund      `protobuf:"bytes,28,rep,name=growth_fund,json=growthFund,proto3" json:"growth_fund"`
	MonthCard            []*minirpc.MonthCard       `protobuf:"bytes,29,rep,name=month_card,json=monthCard,proto3" json:"month_card"`
	RegularPack          []*minirpc.RegularPack     `protobuf:"bytes,30,rep,name=regular_pack,json=regularPack,proto3" json:"regular_pack"`
	AchievementTask      []*minirpc.AchievementTask `protobuf:"bytes,31,rep,name=achievement_task,json=achievementTask,proto3" json:"achievement_task"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *UserData) Reset()         { *m = UserData{} }
func (m *UserData) String() string { return proto.CompactTextString(m) }
func (*UserData) ProtoMessage()    {}
func (*UserData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{1}
}
func (m *UserData) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UserData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UserData.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UserData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserData.Merge(m, src)
}
func (m *UserData) XXX_Size() int {
	return m.Size()
}
func (m *UserData) XXX_DiscardUnknown() {
	xxx_messageInfo_UserData.DiscardUnknown(m)
}

var xxx_messageInfo_UserData proto.InternalMessageInfo

func (m *UserData) GetUi1() *minirpc.UserInfo {
	if m != nil {
		return m.Ui1
	}
	return nil
}

func (m *UserData) GetBuilding() []*minirpc.BuildingInfo {
	if m != nil {
		return m.Building
	}
	return nil
}

func (m *UserData) GetHero() []*minirpc.HeroInfo {
	if m != nil {
		return m.Hero
	}
	return nil
}

func (m *UserData) GetHeroLottery() *minirpc.HeroLottery {
	if m != nil {
		return m.HeroLottery
	}
	return nil
}

func (m *UserData) GetConsumables() []*minirpc.Consumable {
	if m != nil {
		return m.Consumables
	}
	return nil
}

func (m *UserData) GetUserBenefits() []*minirpc.UserBenefit {
	if m != nil {
		return m.UserBenefits
	}
	return nil
}

func (m *UserData) GetUserJob() []*minirpc.UserJob {
	if m != nil {
		return m.UserJob
	}
	return nil
}

func (m *UserData) GetUserTroop() []*minirpc.UserTroop {
	if m != nil {
		return m.UserTroop
	}
	return nil
}

func (m *UserData) GetResearch() []*minirpc.Research {
	if m != nil {
		return m.Research
	}
	return nil
}

func (m *UserData) GetMainTask() []*minirpc.MainTask {
	if m != nil {
		return m.MainTask
	}
	return nil
}

func (m *UserData) GetMapEvent() *minirpc.MapEvent {
	if m != nil {
		return m.MapEvent
	}
	return nil
}

func (m *UserData) GetHeroSkill() []*minirpc.HeroSkill {
	if m != nil {
		return m.HeroSkill
	}
	return nil
}

func (m *UserData) GetDave() *minirpc.Dave {
	if m != nil {
		return m.Dave
	}
	return nil
}

func (m *UserData) GetUserData() []*minirpc.UserData {
	if m != nil {
		return m.UserData
	}
	return nil
}

func (m *UserData) GetMainLineStage() *minirpc.MainLineStage {
	if m != nil {
		return m.MainLineStage
	}
	return nil
}

func (m *UserData) GetCurServerTime() int64 {
	if m != nil {
		return m.CurServerTime
	}
	return 0
}

func (m *UserData) GetIsNewPlayer() bool {
	if m != nil {
		return m.IsNewPlayer
	}
	return false
}

func (m *UserData) GetRtmToken() string {
	if m != nil {
		return m.RtmToken
	}
	return ""
}

func (m *UserData) GetUserIapBuyLog() *minirpc.UserIapBuyTimes {
	if m != nil {
		return m.UserIapBuyLog
	}
	return nil
}

func (m *UserData) GetLordEquip() []*minirpc.LordEquip {
	if m != nil {
		return m.LordEquip
	}
	return nil
}

func (m *UserData) GetLordGem() []*minirpc.LordGem {
	if m != nil {
		return m.LordGem
	}
	return nil
}

func (m *UserData) GetLordGemRandom() []*minirpc.LordGemRandom {
	if m != nil {
		return m.LordGemRandom
	}
	return nil
}

func (m *UserData) GetDungeon() []*minirpc.Dungeon {
	if m != nil {
		return m.Dungeon
	}
	return nil
}

func (m *UserData) GetFunctionOpen() *minirpc.FunctionOpen {
	if m != nil {
		return m.FunctionOpen
	}
	return nil
}

func (m *UserData) GetNewbieGuide() *minirpc.NewbieGuide {
	if m != nil {
		return m.NewbieGuide
	}
	return nil
}

func (m *UserData) GetActivityTask() []*minirpc.ActivityTask {
	if m != nil {
		return m.ActivityTask
	}
	return nil
}

func (m *UserData) GetFirstCharge() []*minirpc.FirstCharge {
	if m != nil {
		return m.FirstCharge
	}
	return nil
}

func (m *UserData) GetGrowthFund() []*minirpc.GrowthFund {
	if m != nil {
		return m.GrowthFund
	}
	return nil
}

func (m *UserData) GetMonthCard() []*minirpc.MonthCard {
	if m != nil {
		return m.MonthCard
	}
	return nil
}

func (m *UserData) GetRegularPack() []*minirpc.RegularPack {
	if m != nil {
		return m.RegularPack
	}
	return nil
}

func (m *UserData) GetAchievementTask() []*minirpc.AchievementTask {
	if m != nil {
		return m.AchievementTask
	}
	return nil
}

type ManiFest struct {
	LangUrl              string   `protobuf:"bytes,1,opt,name=langUrl,proto3" json:"langUrl"`
	AppId                string   `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId"`
	BundleVersionR       string   `protobuf:"bytes,3,opt,name=bundleVersionR,proto3" json:"bundleVersionR"`
	BundleVersionG       string   `protobuf:"bytes,4,opt,name=bundleVersionG,proto3" json:"bundleVersionG"`
	Cdn                  string   `protobuf:"bytes,5,opt,name=cdn,proto3" json:"cdn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManiFest) Reset()         { *m = ManiFest{} }
func (m *ManiFest) String() string { return proto.CompactTextString(m) }
func (*ManiFest) ProtoMessage()    {}
func (*ManiFest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{2}
}
func (m *ManiFest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ManiFest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ManiFest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ManiFest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManiFest.Merge(m, src)
}
func (m *ManiFest) XXX_Size() int {
	return m.Size()
}
func (m *ManiFest) XXX_DiscardUnknown() {
	xxx_messageInfo_ManiFest.DiscardUnknown(m)
}

var xxx_messageInfo_ManiFest proto.InternalMessageInfo

func (m *ManiFest) GetLangUrl() string {
	if m != nil {
		return m.LangUrl
	}
	return ""
}

func (m *ManiFest) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *ManiFest) GetBundleVersionR() string {
	if m != nil {
		return m.BundleVersionR
	}
	return ""
}

func (m *ManiFest) GetBundleVersionG() string {
	if m != nil {
		return m.BundleVersionG
	}
	return ""
}

func (m *ManiFest) GetCdn() string {
	if m != nil {
		return m.Cdn
	}
	return ""
}

type Rewards struct {
	ItemId               int32    `protobuf:"varint,1,opt,name=itemId,proto3" json:"itemId"`
	ItemValue            int64    `protobuf:"varint,2,opt,name=itemValue,proto3" json:"itemValue"`
	Type                 int32    `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Rewards) Reset()         { *m = Rewards{} }
func (m *Rewards) String() string { return proto.CompactTextString(m) }
func (*Rewards) ProtoMessage()    {}
func (*Rewards) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{3}
}
func (m *Rewards) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Rewards) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Rewards.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Rewards) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Rewards.Merge(m, src)
}
func (m *Rewards) XXX_Size() int {
	return m.Size()
}
func (m *Rewards) XXX_DiscardUnknown() {
	xxx_messageInfo_Rewards.DiscardUnknown(m)
}

var xxx_messageInfo_Rewards proto.InternalMessageInfo

func (m *Rewards) GetItemId() int32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *Rewards) GetItemValue() int64 {
	if m != nil {
		return m.ItemValue
	}
	return 0
}

func (m *Rewards) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type KillMonsterRet struct {
	Rewards              []*Rewards `protobuf:"bytes,1,rep,name=rewards,proto3" json:"rewards"`
	SelectRougeIds       []int32    `protobuf:"varint,2,rep,packed,name=selectRougeIds,proto3" json:"selectRougeIds"`
	UnSelectRougeIds     []int32    `protobuf:"varint,3,rep,packed,name=unSelectRougeIds,proto3" json:"unSelectRougeIds"`
	KillAmount           int32      `protobuf:"varint,4,opt,name=kill_amount,json=killAmount,proto3" json:"kill_amount"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *KillMonsterRet) Reset()         { *m = KillMonsterRet{} }
func (m *KillMonsterRet) String() string { return proto.CompactTextString(m) }
func (*KillMonsterRet) ProtoMessage()    {}
func (*KillMonsterRet) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{4}
}
func (m *KillMonsterRet) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *KillMonsterRet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_KillMonsterRet.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *KillMonsterRet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KillMonsterRet.Merge(m, src)
}
func (m *KillMonsterRet) XXX_Size() int {
	return m.Size()
}
func (m *KillMonsterRet) XXX_DiscardUnknown() {
	xxx_messageInfo_KillMonsterRet.DiscardUnknown(m)
}

var xxx_messageInfo_KillMonsterRet proto.InternalMessageInfo

func (m *KillMonsterRet) GetRewards() []*Rewards {
	if m != nil {
		return m.Rewards
	}
	return nil
}

func (m *KillMonsterRet) GetSelectRougeIds() []int32 {
	if m != nil {
		return m.SelectRougeIds
	}
	return nil
}

func (m *KillMonsterRet) GetUnSelectRougeIds() []int32 {
	if m != nil {
		return m.UnSelectRougeIds
	}
	return nil
}

func (m *KillMonsterRet) GetKillAmount() int32 {
	if m != nil {
		return m.KillAmount
	}
	return 0
}

type GetRewardReturn struct {
	Rewards              map[int32]int64 `protobuf:"bytes,1,rep,name=rewards,proto3" json:"rewards" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	IsFull               bool            `protobuf:"varint,2,opt,name=isFull,proto3" json:"isFull"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetRewardReturn) Reset()         { *m = GetRewardReturn{} }
func (m *GetRewardReturn) String() string { return proto.CompactTextString(m) }
func (*GetRewardReturn) ProtoMessage()    {}
func (*GetRewardReturn) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{5}
}
func (m *GetRewardReturn) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRewardReturn) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRewardReturn.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetRewardReturn) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRewardReturn.Merge(m, src)
}
func (m *GetRewardReturn) XXX_Size() int {
	return m.Size()
}
func (m *GetRewardReturn) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRewardReturn.DiscardUnknown(m)
}

var xxx_messageInfo_GetRewardReturn proto.InternalMessageInfo

func (m *GetRewardReturn) GetRewards() map[int32]int64 {
	if m != nil {
		return m.Rewards
	}
	return nil
}

func (m *GetRewardReturn) GetIsFull() bool {
	if m != nil {
		return m.IsFull
	}
	return false
}

type SendMsgParams struct {
	FromUid              int64    `protobuf:"varint,1,opt,name=FromUid,proto3" json:"FromUid"`
	Uids                 []int64  `protobuf:"varint,2,rep,packed,name=Uids,proto3" json:"Uids"`
	MsgType              uint32   `protobuf:"varint,3,opt,name=MsgType,proto3" json:"MsgType"`
	Msg                  string   `protobuf:"bytes,4,opt,name=Msg,proto3" json:"Msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendMsgParams) Reset()         { *m = SendMsgParams{} }
func (m *SendMsgParams) String() string { return proto.CompactTextString(m) }
func (*SendMsgParams) ProtoMessage()    {}
func (*SendMsgParams) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{6}
}
func (m *SendMsgParams) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SendMsgParams) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SendMsgParams.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SendMsgParams) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendMsgParams.Merge(m, src)
}
func (m *SendMsgParams) XXX_Size() int {
	return m.Size()
}
func (m *SendMsgParams) XXX_DiscardUnknown() {
	xxx_messageInfo_SendMsgParams.DiscardUnknown(m)
}

var xxx_messageInfo_SendMsgParams proto.InternalMessageInfo

func (m *SendMsgParams) GetFromUid() int64 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *SendMsgParams) GetUids() []int64 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *SendMsgParams) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

func (m *SendMsgParams) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type SendPBMsgParams struct {
	FromUid              int64    `protobuf:"varint,1,opt,name=FromUid,proto3" json:"FromUid"`
	Uids                 []int64  `protobuf:"varint,2,rep,packed,name=Uids,proto3" json:"Uids"`
	MsgType              uint32   `protobuf:"varint,3,opt,name=MsgType,proto3" json:"MsgType"`
	Msg                  []byte   `protobuf:"bytes,4,opt,name=Msg,proto3" json:"Msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendPBMsgParams) Reset()         { *m = SendPBMsgParams{} }
func (m *SendPBMsgParams) String() string { return proto.CompactTextString(m) }
func (*SendPBMsgParams) ProtoMessage()    {}
func (*SendPBMsgParams) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{7}
}
func (m *SendPBMsgParams) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SendPBMsgParams) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SendPBMsgParams.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SendPBMsgParams) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPBMsgParams.Merge(m, src)
}
func (m *SendPBMsgParams) XXX_Size() int {
	return m.Size()
}
func (m *SendPBMsgParams) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPBMsgParams.DiscardUnknown(m)
}

var xxx_messageInfo_SendPBMsgParams proto.InternalMessageInfo

func (m *SendPBMsgParams) GetFromUid() int64 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *SendPBMsgParams) GetUids() []int64 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *SendPBMsgParams) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

func (m *SendPBMsgParams) GetMsg() []byte {
	if m != nil {
		return m.Msg
	}
	return nil
}

type MultiSendMsgParams struct {
	Params               []*SendMsgParams `protobuf:"bytes,1,rep,name=Params,proto3" json:"Params"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *MultiSendMsgParams) Reset()         { *m = MultiSendMsgParams{} }
func (m *MultiSendMsgParams) String() string { return proto.CompactTextString(m) }
func (*MultiSendMsgParams) ProtoMessage()    {}
func (*MultiSendMsgParams) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{8}
}
func (m *MultiSendMsgParams) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MultiSendMsgParams) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MultiSendMsgParams.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MultiSendMsgParams) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiSendMsgParams.Merge(m, src)
}
func (m *MultiSendMsgParams) XXX_Size() int {
	return m.Size()
}
func (m *MultiSendMsgParams) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiSendMsgParams.DiscardUnknown(m)
}

var xxx_messageInfo_MultiSendMsgParams proto.InternalMessageInfo

func (m *MultiSendMsgParams) GetParams() []*SendMsgParams {
	if m != nil {
		return m.Params
	}
	return nil
}

type MultiSendPBMsgParams struct {
	Params               []*SendPBMsgParams `protobuf:"bytes,1,rep,name=Params,proto3" json:"Params"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MultiSendPBMsgParams) Reset()         { *m = MultiSendPBMsgParams{} }
func (m *MultiSendPBMsgParams) String() string { return proto.CompactTextString(m) }
func (*MultiSendPBMsgParams) ProtoMessage()    {}
func (*MultiSendPBMsgParams) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{9}
}
func (m *MultiSendPBMsgParams) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MultiSendPBMsgParams) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MultiSendPBMsgParams.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MultiSendPBMsgParams) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiSendPBMsgParams.Merge(m, src)
}
func (m *MultiSendPBMsgParams) XXX_Size() int {
	return m.Size()
}
func (m *MultiSendPBMsgParams) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiSendPBMsgParams.DiscardUnknown(m)
}

var xxx_messageInfo_MultiSendPBMsgParams proto.InternalMessageInfo

func (m *MultiSendPBMsgParams) GetParams() []*SendPBMsgParams {
	if m != nil {
		return m.Params
	}
	return nil
}

type SendTopicMsgParams struct {
	Topic                string   `protobuf:"bytes,1,opt,name=Topic,proto3" json:"Topic"`
	MsgType              uint32   `protobuf:"varint,2,opt,name=MsgType,proto3" json:"MsgType"`
	Msg                  string   `protobuf:"bytes,3,opt,name=Msg,proto3" json:"Msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendTopicMsgParams) Reset()         { *m = SendTopicMsgParams{} }
func (m *SendTopicMsgParams) String() string { return proto.CompactTextString(m) }
func (*SendTopicMsgParams) ProtoMessage()    {}
func (*SendTopicMsgParams) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{10}
}
func (m *SendTopicMsgParams) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SendTopicMsgParams) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SendTopicMsgParams.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SendTopicMsgParams) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendTopicMsgParams.Merge(m, src)
}
func (m *SendTopicMsgParams) XXX_Size() int {
	return m.Size()
}
func (m *SendTopicMsgParams) XXX_DiscardUnknown() {
	xxx_messageInfo_SendTopicMsgParams.DiscardUnknown(m)
}

var xxx_messageInfo_SendTopicMsgParams proto.InternalMessageInfo

func (m *SendTopicMsgParams) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *SendTopicMsgParams) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

func (m *SendTopicMsgParams) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type SendPBTopicMsgParams struct {
	Topic                string   `protobuf:"bytes,1,opt,name=Topic,proto3" json:"Topic"`
	MsgType              uint32   `protobuf:"varint,2,opt,name=MsgType,proto3" json:"MsgType"`
	Msg                  string   `protobuf:"bytes,3,opt,name=Msg,proto3" json:"Msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendPBTopicMsgParams) Reset()         { *m = SendPBTopicMsgParams{} }
func (m *SendPBTopicMsgParams) String() string { return proto.CompactTextString(m) }
func (*SendPBTopicMsgParams) ProtoMessage()    {}
func (*SendPBTopicMsgParams) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{11}
}
func (m *SendPBTopicMsgParams) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SendPBTopicMsgParams) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SendPBTopicMsgParams.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SendPBTopicMsgParams) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPBTopicMsgParams.Merge(m, src)
}
func (m *SendPBTopicMsgParams) XXX_Size() int {
	return m.Size()
}
func (m *SendPBTopicMsgParams) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPBTopicMsgParams.DiscardUnknown(m)
}

var xxx_messageInfo_SendPBTopicMsgParams proto.InternalMessageInfo

func (m *SendPBTopicMsgParams) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *SendPBTopicMsgParams) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

func (m *SendPBTopicMsgParams) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type FinishEventReturn struct {
	Success              bool            `protobuf:"varint,1,opt,name=success,proto3" json:"success"`
	Rewards              map[int32]int64 `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *FinishEventReturn) Reset()         { *m = FinishEventReturn{} }
func (m *FinishEventReturn) String() string { return proto.CompactTextString(m) }
func (*FinishEventReturn) ProtoMessage()    {}
func (*FinishEventReturn) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{12}
}
func (m *FinishEventReturn) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FinishEventReturn) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FinishEventReturn.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FinishEventReturn) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishEventReturn.Merge(m, src)
}
func (m *FinishEventReturn) XXX_Size() int {
	return m.Size()
}
func (m *FinishEventReturn) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishEventReturn.DiscardUnknown(m)
}

var xxx_messageInfo_FinishEventReturn proto.InternalMessageInfo

func (m *FinishEventReturn) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

func (m *FinishEventReturn) GetRewards() map[int32]int64 {
	if m != nil {
		return m.Rewards
	}
	return nil
}

// 关卡数据
type LevelStruct struct {
	CardIds              []int32  `protobuf:"varint,1,rep,packed,name=card_ids,json=cardIds,proto3" json:"card_ids"`
	TotalExp             int32    `protobuf:"varint,2,opt,name=total_exp,json=totalExp,proto3" json:"total_exp"`
	HeroHp               []int32  `protobuf:"varint,3,rep,packed,name=hero_hp,json=heroHp,proto3" json:"hero_hp"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelStruct) Reset()         { *m = LevelStruct{} }
func (m *LevelStruct) String() string { return proto.CompactTextString(m) }
func (*LevelStruct) ProtoMessage()    {}
func (*LevelStruct) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{13}
}
func (m *LevelStruct) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LevelStruct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LevelStruct.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LevelStruct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelStruct.Merge(m, src)
}
func (m *LevelStruct) XXX_Size() int {
	return m.Size()
}
func (m *LevelStruct) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelStruct.DiscardUnknown(m)
}

var xxx_messageInfo_LevelStruct proto.InternalMessageInfo

func (m *LevelStruct) GetCardIds() []int32 {
	if m != nil {
		return m.CardIds
	}
	return nil
}

func (m *LevelStruct) GetTotalExp() int32 {
	if m != nil {
		return m.TotalExp
	}
	return 0
}

func (m *LevelStruct) GetHeroHp() []int32 {
	if m != nil {
		return m.HeroHp
	}
	return nil
}

// 礼包数据
type GiftStruct struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiftStruct) Reset()         { *m = GiftStruct{} }
func (m *GiftStruct) String() string { return proto.CompactTextString(m) }
func (*GiftStruct) ProtoMessage()    {}
func (*GiftStruct) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{14}
}
func (m *GiftStruct) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GiftStruct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GiftStruct.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GiftStruct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiftStruct.Merge(m, src)
}
func (m *GiftStruct) XXX_Size() int {
	return m.Size()
}
func (m *GiftStruct) XXX_DiscardUnknown() {
	xxx_messageInfo_GiftStruct.DiscardUnknown(m)
}

var xxx_messageInfo_GiftStruct proto.InternalMessageInfo

func (m *GiftStruct) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 关卡排行榜信息
type LevelRankInfo struct {
	Rank                 int32    `protobuf:"varint,1,opt,name=rank,proto3" json:"rank"`
	Uid                  int64    `protobuf:"varint,2,opt,name=uid,proto3" json:"uid"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	Level                int32    `protobuf:"varint,4,opt,name=level,proto3" json:"level"`
	Score                int32    `protobuf:"varint,5,opt,name=score,proto3" json:"score"`
	Icon                 int32    `protobuf:"varint,6,opt,name=icon,proto3" json:"icon"`
	FinishTime           int32    `protobuf:"varint,7,opt,name=finish_time,json=finishTime,proto3" json:"finish_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelRankInfo) Reset()         { *m = LevelRankInfo{} }
func (m *LevelRankInfo) String() string { return proto.CompactTextString(m) }
func (*LevelRankInfo) ProtoMessage()    {}
func (*LevelRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{15}
}
func (m *LevelRankInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LevelRankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LevelRankInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LevelRankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelRankInfo.Merge(m, src)
}
func (m *LevelRankInfo) XXX_Size() int {
	return m.Size()
}
func (m *LevelRankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelRankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LevelRankInfo proto.InternalMessageInfo

func (m *LevelRankInfo) GetRank() int32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *LevelRankInfo) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LevelRankInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *LevelRankInfo) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *LevelRankInfo) GetScore() int32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *LevelRankInfo) GetIcon() int32 {
	if m != nil {
		return m.Icon
	}
	return 0
}

func (m *LevelRankInfo) GetFinishTime() int32 {
	if m != nil {
		return m.FinishTime
	}
	return 0
}

// 力量排行榜信息
type PowerRankInfo struct {
	Rank                 int32    `protobuf:"varint,1,opt,name=rank,proto3" json:"rank"`
	Uid                  int64    `protobuf:"varint,2,opt,name=uid,proto3" json:"uid"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	Power                int64    `protobuf:"varint,4,opt,name=power,proto3" json:"power"`
	Score                int32    `protobuf:"varint,5,opt,name=score,proto3" json:"score"`
	Icon                 int32    `protobuf:"varint,6,opt,name=icon,proto3" json:"icon"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PowerRankInfo) Reset()         { *m = PowerRankInfo{} }
func (m *PowerRankInfo) String() string { return proto.CompactTextString(m) }
func (*PowerRankInfo) ProtoMessage()    {}
func (*PowerRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{16}
}
func (m *PowerRankInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PowerRankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PowerRankInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PowerRankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PowerRankInfo.Merge(m, src)
}
func (m *PowerRankInfo) XXX_Size() int {
	return m.Size()
}
func (m *PowerRankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PowerRankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PowerRankInfo proto.InternalMessageInfo

func (m *PowerRankInfo) GetRank() int32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *PowerRankInfo) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PowerRankInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PowerRankInfo) GetPower() int64 {
	if m != nil {
		return m.Power
	}
	return 0
}

func (m *PowerRankInfo) GetScore() int32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *PowerRankInfo) GetIcon() int32 {
	if m != nil {
		return m.Icon
	}
	return 0
}

// 宝石合成
type LordGemCraft struct {
	GemCraftId           int32           `protobuf:"varint,1,opt,name=gem_craft_id,json=gemCraftId,proto3" json:"gem_craft_id"`
	GemIds               map[int32]int32 `protobuf:"bytes,2,rep,name=gem_ids,json=gemIds,proto3" json:"gem_ids" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	Amount               int32           `protobuf:"varint,3,opt,name=amount,proto3" json:"amount"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *LordGemCraft) Reset()         { *m = LordGemCraft{} }
func (m *LordGemCraft) String() string { return proto.CompactTextString(m) }
func (*LordGemCraft) ProtoMessage()    {}
func (*LordGemCraft) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{17}
}
func (m *LordGemCraft) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LordGemCraft) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LordGemCraft.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LordGemCraft) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LordGemCraft.Merge(m, src)
}
func (m *LordGemCraft) XXX_Size() int {
	return m.Size()
}
func (m *LordGemCraft) XXX_DiscardUnknown() {
	xxx_messageInfo_LordGemCraft.DiscardUnknown(m)
}

var xxx_messageInfo_LordGemCraft proto.InternalMessageInfo

func (m *LordGemCraft) GetGemCraftId() int32 {
	if m != nil {
		return m.GemCraftId
	}
	return 0
}

func (m *LordGemCraft) GetGemIds() map[int32]int32 {
	if m != nil {
		return m.GemIds
	}
	return nil
}

func (m *LordGemCraft) GetAmount() int32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

// 每日任务返回
type DailyResult struct {
	DailyTask            []*minirpc.DailyTask `protobuf:"bytes,1,rep,name=daily_task,json=dailyTask,proto3" json:"daily_task"`
	DailyChest           *minirpc.UserData    `protobuf:"bytes,2,opt,name=daily_chest,json=dailyChest,proto3" json:"daily_chest"`
	WeeklyChest          *minirpc.UserData    `protobuf:"bytes,3,opt,name=weekly_chest,json=weeklyChest,proto3" json:"weekly_chest"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *DailyResult) Reset()         { *m = DailyResult{} }
func (m *DailyResult) String() string { return proto.CompactTextString(m) }
func (*DailyResult) ProtoMessage()    {}
func (*DailyResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{18}
}
func (m *DailyResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DailyResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DailyResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DailyResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DailyResult.Merge(m, src)
}
func (m *DailyResult) XXX_Size() int {
	return m.Size()
}
func (m *DailyResult) XXX_DiscardUnknown() {
	xxx_messageInfo_DailyResult.DiscardUnknown(m)
}

var xxx_messageInfo_DailyResult proto.InternalMessageInfo

func (m *DailyResult) GetDailyTask() []*minirpc.DailyTask {
	if m != nil {
		return m.DailyTask
	}
	return nil
}

func (m *DailyResult) GetDailyChest() *minirpc.UserData {
	if m != nil {
		return m.DailyChest
	}
	return nil
}

func (m *DailyResult) GetWeeklyChest() *minirpc.UserData {
	if m != nil {
		return m.WeeklyChest
	}
	return nil
}

// 邮件列表返回
type GetMailListResult struct {
	MailList             []*minirpc.UserMail `protobuf:"bytes,1,rep,name=mail_list,json=mailList,proto3" json:"mail_list"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetMailListResult) Reset()         { *m = GetMailListResult{} }
func (m *GetMailListResult) String() string { return proto.CompactTextString(m) }
func (*GetMailListResult) ProtoMessage()    {}
func (*GetMailListResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{19}
}
func (m *GetMailListResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetMailListResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetMailListResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetMailListResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMailListResult.Merge(m, src)
}
func (m *GetMailListResult) XXX_Size() int {
	return m.Size()
}
func (m *GetMailListResult) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMailListResult.DiscardUnknown(m)
}

var xxx_messageInfo_GetMailListResult proto.InternalMessageInfo

func (m *GetMailListResult) GetMailList() []*minirpc.UserMail {
	if m != nil {
		return m.MailList
	}
	return nil
}

// 联盟信息返回
type AllianceInfoRet struct {
	Alliance             *minirpc.Alliance          `protobuf:"bytes,1,opt,name=alliance,proto3" json:"alliance"`
	AllianceMembers      []*minirpc.AllianceMember  `protobuf:"bytes,2,rep,name=alliance_members,json=allianceMembers,proto3" json:"alliance_members"`
	AllianceShop         []*minirpc.AllianceShopBuy `protobuf:"bytes,3,rep,name=alliance_shop,json=allianceShop,proto3" json:"alliance_shop"`
	AllianceMemberInfo   []*AllianceMemberInfo      `protobuf:"bytes,4,rep,name=alliance_member_info,json=allianceMemberInfo,proto3" json:"alliance_member_info"`
	AllianceTask         []*minirpc.AllianceTask    `protobuf:"bytes,5,rep,name=alliance_task,json=allianceTask,proto3" json:"alliance_task"`
	AllianceChest        *minirpc.UserData          `protobuf:"bytes,6,opt,name=alliance_chest,json=allianceChest,proto3" json:"alliance_chest"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *AllianceInfoRet) Reset()         { *m = AllianceInfoRet{} }
func (m *AllianceInfoRet) String() string { return proto.CompactTextString(m) }
func (*AllianceInfoRet) ProtoMessage()    {}
func (*AllianceInfoRet) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{20}
}
func (m *AllianceInfoRet) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AllianceInfoRet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AllianceInfoRet.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AllianceInfoRet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllianceInfoRet.Merge(m, src)
}
func (m *AllianceInfoRet) XXX_Size() int {
	return m.Size()
}
func (m *AllianceInfoRet) XXX_DiscardUnknown() {
	xxx_messageInfo_AllianceInfoRet.DiscardUnknown(m)
}

var xxx_messageInfo_AllianceInfoRet proto.InternalMessageInfo

func (m *AllianceInfoRet) GetAlliance() *minirpc.Alliance {
	if m != nil {
		return m.Alliance
	}
	return nil
}

func (m *AllianceInfoRet) GetAllianceMembers() []*minirpc.AllianceMember {
	if m != nil {
		return m.AllianceMembers
	}
	return nil
}

func (m *AllianceInfoRet) GetAllianceShop() []*minirpc.AllianceShopBuy {
	if m != nil {
		return m.AllianceShop
	}
	return nil
}

func (m *AllianceInfoRet) GetAllianceMemberInfo() []*AllianceMemberInfo {
	if m != nil {
		return m.AllianceMemberInfo
	}
	return nil
}

func (m *AllianceInfoRet) GetAllianceTask() []*minirpc.AllianceTask {
	if m != nil {
		return m.AllianceTask
	}
	return nil
}

func (m *AllianceInfoRet) GetAllianceChest() *minirpc.UserData {
	if m != nil {
		return m.AllianceChest
	}
	return nil
}

// 联盟成员信息，用于排行榜
type AllianceMemberInfo struct {
	Uid                  int64    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	StageId              int32    `protobuf:"varint,2,opt,name=stage_id,json=stageId,proto3" json:"stage_id"`
	Power                int64    `protobuf:"varint,3,opt,name=power,proto3" json:"power"`
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	AvatarConfigId       int32    `protobuf:"varint,5,opt,name=avatar_config_id,json=avatarConfigId,proto3" json:"avatar_config_id"`
	StepId               int32    `protobuf:"varint,6,opt,name=step_id,json=stepId,proto3" json:"step_id"`
	FinishTime           int32    `protobuf:"varint,7,opt,name=finish_time,json=finishTime,proto3" json:"finish_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AllianceMemberInfo) Reset()         { *m = AllianceMemberInfo{} }
func (m *AllianceMemberInfo) String() string { return proto.CompactTextString(m) }
func (*AllianceMemberInfo) ProtoMessage()    {}
func (*AllianceMemberInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{21}
}
func (m *AllianceMemberInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AllianceMemberInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AllianceMemberInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AllianceMemberInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllianceMemberInfo.Merge(m, src)
}
func (m *AllianceMemberInfo) XXX_Size() int {
	return m.Size()
}
func (m *AllianceMemberInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AllianceMemberInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AllianceMemberInfo proto.InternalMessageInfo

func (m *AllianceMemberInfo) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AllianceMemberInfo) GetStageId() int32 {
	if m != nil {
		return m.StageId
	}
	return 0
}

func (m *AllianceMemberInfo) GetPower() int64 {
	if m != nil {
		return m.Power
	}
	return 0
}

func (m *AllianceMemberInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AllianceMemberInfo) GetAvatarConfigId() int32 {
	if m != nil {
		return m.AvatarConfigId
	}
	return 0
}

func (m *AllianceMemberInfo) GetStepId() int32 {
	if m != nil {
		return m.StepId
	}
	return 0
}

func (m *AllianceMemberInfo) GetFinishTime() int32 {
	if m != nil {
		return m.FinishTime
	}
	return 0
}

type GetAllianceMembersInfosRet struct {
	Infos                []*AllianceMemberInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetAllianceMembersInfosRet) Reset()         { *m = GetAllianceMembersInfosRet{} }
func (m *GetAllianceMembersInfosRet) String() string { return proto.CompactTextString(m) }
func (*GetAllianceMembersInfosRet) ProtoMessage()    {}
func (*GetAllianceMembersInfosRet) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{22}
}
func (m *GetAllianceMembersInfosRet) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetAllianceMembersInfosRet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetAllianceMembersInfosRet.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetAllianceMembersInfosRet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllianceMembersInfosRet.Merge(m, src)
}
func (m *GetAllianceMembersInfosRet) XXX_Size() int {
	return m.Size()
}
func (m *GetAllianceMembersInfosRet) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllianceMembersInfosRet.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllianceMembersInfosRet proto.InternalMessageInfo

func (m *GetAllianceMembersInfosRet) GetInfos() []*AllianceMemberInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

type GetAllianceListRet struct {
	Alliance             []*minirpc.Alliance `protobuf:"bytes,1,rep,name=alliance,proto3" json:"alliance"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAllianceListRet) Reset()         { *m = GetAllianceListRet{} }
func (m *GetAllianceListRet) String() string { return proto.CompactTextString(m) }
func (*GetAllianceListRet) ProtoMessage()    {}
func (*GetAllianceListRet) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{23}
}
func (m *GetAllianceListRet) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetAllianceListRet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetAllianceListRet.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetAllianceListRet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllianceListRet.Merge(m, src)
}
func (m *GetAllianceListRet) XXX_Size() int {
	return m.Size()
}
func (m *GetAllianceListRet) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllianceListRet.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllianceListRet proto.InternalMessageInfo

func (m *GetAllianceListRet) GetAlliance() []*minirpc.Alliance {
	if m != nil {
		return m.Alliance
	}
	return nil
}

type GetUserInfo struct {
	Uid                  int64                `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Name                 string               `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	AllianceName         string               `protobuf:"bytes,3,opt,name=alliance_name,json=allianceName,proto3" json:"alliance_name"`
	AllianceAcronym      string               `protobuf:"bytes,4,opt,name=alliance_acronym,json=allianceAcronym,proto3" json:"alliance_acronym"`
	MaxStage             int32                `protobuf:"varint,5,opt,name=max_stage,json=maxStage,proto3" json:"max_stage"`
	Power                int64                `protobuf:"varint,6,opt,name=power,proto3" json:"power"`
	FriendStageLimit     int32                `protobuf:"varint,7,opt,name=friend_stage_limit,json=friendStageLimit,proto3" json:"friend_stage_limit"`
	AllowStrangers       bool                 `protobuf:"varint,8,opt,name=allow_strangers,json=allowStrangers,proto3" json:"allow_strangers"`
	AvatarConfigId       int32                `protobuf:"varint,9,opt,name=avatar_config_id,json=avatarConfigId,proto3" json:"avatar_config_id"`
	Hero                 []*minirpc.HeroInfo  `protobuf:"bytes,10,rep,name=hero,proto3" json:"hero"`
	LordEquip            []*minirpc.LordEquip `protobuf:"bytes,11,rep,name=lord_equip,json=lordEquip,proto3" json:"lord_equip"`
	LordGemPower         map[int32]int32      `protobuf:"bytes,12,rep,name=lordGemPower,proto3" json:"lordGemPower" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetUserInfo) Reset()         { *m = GetUserInfo{} }
func (m *GetUserInfo) String() string { return proto.CompactTextString(m) }
func (*GetUserInfo) ProtoMessage()    {}
func (*GetUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{24}
}
func (m *GetUserInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetUserInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInfo.Merge(m, src)
}
func (m *GetUserInfo) XXX_Size() int {
	return m.Size()
}
func (m *GetUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInfo proto.InternalMessageInfo

func (m *GetUserInfo) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetUserInfo) GetAllianceName() string {
	if m != nil {
		return m.AllianceName
	}
	return ""
}

func (m *GetUserInfo) GetAllianceAcronym() string {
	if m != nil {
		return m.AllianceAcronym
	}
	return ""
}

func (m *GetUserInfo) GetMaxStage() int32 {
	if m != nil {
		return m.MaxStage
	}
	return 0
}

func (m *GetUserInfo) GetPower() int64 {
	if m != nil {
		return m.Power
	}
	return 0
}

func (m *GetUserInfo) GetFriendStageLimit() int32 {
	if m != nil {
		return m.FriendStageLimit
	}
	return 0
}

func (m *GetUserInfo) GetAllowStrangers() bool {
	if m != nil {
		return m.AllowStrangers
	}
	return false
}

func (m *GetUserInfo) GetAvatarConfigId() int32 {
	if m != nil {
		return m.AvatarConfigId
	}
	return 0
}

func (m *GetUserInfo) GetHero() []*minirpc.HeroInfo {
	if m != nil {
		return m.Hero
	}
	return nil
}

func (m *GetUserInfo) GetLordEquip() []*minirpc.LordEquip {
	if m != nil {
		return m.LordEquip
	}
	return nil
}

func (m *GetUserInfo) GetLordGemPower() map[int32]int32 {
	if m != nil {
		return m.LordGemPower
	}
	return nil
}

type ActivityInfo struct {
	StartTime            int64    `protobuf:"varint,1,opt,name=startTime,proto3" json:"startTime"`
	EndTime              int64    `protobuf:"varint,2,opt,name=endTime,proto3" json:"endTime"`
	Status               int32    `protobuf:"varint,3,opt,name=status,proto3" json:"status"`
	ActivityName         string   `protobuf:"bytes,4,opt,name=activityName,proto3" json:"activityName"`
	ActivityId           int32    `protobuf:"varint,5,opt,name=activityId,proto3" json:"activityId"`
	PublicTime           int64    `protobuf:"varint,6,opt,name=publicTime,proto3" json:"publicTime"`
	ActivityType         int32    `protobuf:"varint,7,opt,name=activityType,proto3" json:"activityType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActivityInfo) Reset()         { *m = ActivityInfo{} }
func (m *ActivityInfo) String() string { return proto.CompactTextString(m) }
func (*ActivityInfo) ProtoMessage()    {}
func (*ActivityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{25}
}
func (m *ActivityInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ActivityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ActivityInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ActivityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityInfo.Merge(m, src)
}
func (m *ActivityInfo) XXX_Size() int {
	return m.Size()
}
func (m *ActivityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityInfo proto.InternalMessageInfo

func (m *ActivityInfo) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ActivityInfo) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ActivityInfo) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ActivityInfo) GetActivityName() string {
	if m != nil {
		return m.ActivityName
	}
	return ""
}

func (m *ActivityInfo) GetActivityId() int32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *ActivityInfo) GetPublicTime() int64 {
	if m != nil {
		return m.PublicTime
	}
	return 0
}

func (m *ActivityInfo) GetActivityType() int32 {
	if m != nil {
		return m.ActivityType
	}
	return 0
}

type InitSymbioticRet struct {
	ResetHeroId          []int32         `protobuf:"varint,1,rep,packed,name=resetHeroId,proto3" json:"resetHeroId"`
	ResetItemMap         map[int32]int32 `protobuf:"bytes,2,rep,name=resetItemMap,proto3" json:"resetItemMap" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *InitSymbioticRet) Reset()         { *m = InitSymbioticRet{} }
func (m *InitSymbioticRet) String() string { return proto.CompactTextString(m) }
func (*InitSymbioticRet) ProtoMessage()    {}
func (*InitSymbioticRet) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{26}
}
func (m *InitSymbioticRet) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *InitSymbioticRet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_InitSymbioticRet.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *InitSymbioticRet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InitSymbioticRet.Merge(m, src)
}
func (m *InitSymbioticRet) XXX_Size() int {
	return m.Size()
}
func (m *InitSymbioticRet) XXX_DiscardUnknown() {
	xxx_messageInfo_InitSymbioticRet.DiscardUnknown(m)
}

var xxx_messageInfo_InitSymbioticRet proto.InternalMessageInfo

func (m *InitSymbioticRet) GetResetHeroId() []int32 {
	if m != nil {
		return m.ResetHeroId
	}
	return nil
}

func (m *InitSymbioticRet) GetResetItemMap() map[int32]int32 {
	if m != nil {
		return m.ResetItemMap
	}
	return nil
}

type OneKeyHeroLevelUpgradeRet struct {
	HeroId               int32    `protobuf:"varint,1,opt,name=heroId,proto3" json:"heroId"`
	Amount               int32    `protobuf:"varint,2,opt,name=amount,proto3" json:"amount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OneKeyHeroLevelUpgradeRet) Reset()         { *m = OneKeyHeroLevelUpgradeRet{} }
func (m *OneKeyHeroLevelUpgradeRet) String() string { return proto.CompactTextString(m) }
func (*OneKeyHeroLevelUpgradeRet) ProtoMessage()    {}
func (*OneKeyHeroLevelUpgradeRet) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{27}
}
func (m *OneKeyHeroLevelUpgradeRet) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OneKeyHeroLevelUpgradeRet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OneKeyHeroLevelUpgradeRet.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OneKeyHeroLevelUpgradeRet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneKeyHeroLevelUpgradeRet.Merge(m, src)
}
func (m *OneKeyHeroLevelUpgradeRet) XXX_Size() int {
	return m.Size()
}
func (m *OneKeyHeroLevelUpgradeRet) XXX_DiscardUnknown() {
	xxx_messageInfo_OneKeyHeroLevelUpgradeRet.DiscardUnknown(m)
}

var xxx_messageInfo_OneKeyHeroLevelUpgradeRet proto.InternalMessageInfo

func (m *OneKeyHeroLevelUpgradeRet) GetHeroId() int32 {
	if m != nil {
		return m.HeroId
	}
	return 0
}

func (m *OneKeyHeroLevelUpgradeRet) GetAmount() int32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

type OneKeyEquipGemReq struct {
	GemId                int32    `protobuf:"varint,1,opt,name=GemId,proto3" json:"GemId"`
	Pos                  int32    `protobuf:"varint,2,opt,name=Pos,proto3" json:"Pos"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OneKeyEquipGemReq) Reset()         { *m = OneKeyEquipGemReq{} }
func (m *OneKeyEquipGemReq) String() string { return proto.CompactTextString(m) }
func (*OneKeyEquipGemReq) ProtoMessage()    {}
func (*OneKeyEquipGemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ab8f08a44d4f49e, []int{28}
}
func (m *OneKeyEquipGemReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OneKeyEquipGemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OneKeyEquipGemReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OneKeyEquipGemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneKeyEquipGemReq.Merge(m, src)
}
func (m *OneKeyEquipGemReq) XXX_Size() int {
	return m.Size()
}
func (m *OneKeyEquipGemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OneKeyEquipGemReq.DiscardUnknown(m)
}

var xxx_messageInfo_OneKeyEquipGemReq proto.InternalMessageInfo

func (m *OneKeyEquipGemReq) GetGemId() int32 {
	if m != nil {
		return m.GemId
	}
	return 0
}

func (m *OneKeyEquipGemReq) GetPos() int32 {
	if m != nil {
		return m.Pos
	}
	return 0
}

func init() {
	proto.RegisterType((*RequestParams)(nil), "wrpc.RequestParams")
	proto.RegisterType((*UserData)(nil), "wrpc.UserData")
	proto.RegisterType((*ManiFest)(nil), "wrpc.ManiFest")
	proto.RegisterType((*Rewards)(nil), "wrpc.Rewards")
	proto.RegisterType((*KillMonsterRet)(nil), "wrpc.KillMonsterRet")
	proto.RegisterType((*GetRewardReturn)(nil), "wrpc.GetRewardReturn")
	proto.RegisterMapType((map[int32]int64)(nil), "wrpc.GetRewardReturn.RewardsEntry")
	proto.RegisterType((*SendMsgParams)(nil), "wrpc.SendMsgParams")
	proto.RegisterType((*SendPBMsgParams)(nil), "wrpc.SendPBMsgParams")
	proto.RegisterType((*MultiSendMsgParams)(nil), "wrpc.MultiSendMsgParams")
	proto.RegisterType((*MultiSendPBMsgParams)(nil), "wrpc.MultiSendPBMsgParams")
	proto.RegisterType((*SendTopicMsgParams)(nil), "wrpc.SendTopicMsgParams")
	proto.RegisterType((*SendPBTopicMsgParams)(nil), "wrpc.SendPBTopicMsgParams")
	proto.RegisterType((*FinishEventReturn)(nil), "wrpc.finishEventReturn")
	proto.RegisterMapType((map[int32]int64)(nil), "wrpc.finishEventReturn.RewardsEntry")
	proto.RegisterType((*LevelStruct)(nil), "wrpc.LevelStruct")
	proto.RegisterType((*GiftStruct)(nil), "wrpc.GiftStruct")
	proto.RegisterType((*LevelRankInfo)(nil), "wrpc.LevelRankInfo")
	proto.RegisterType((*PowerRankInfo)(nil), "wrpc.PowerRankInfo")
	proto.RegisterType((*LordGemCraft)(nil), "wrpc.LordGemCraft")
	proto.RegisterMapType((map[int32]int32)(nil), "wrpc.LordGemCraft.GemIdsEntry")
	proto.RegisterType((*DailyResult)(nil), "wrpc.DailyResult")
	proto.RegisterType((*GetMailListResult)(nil), "wrpc.GetMailListResult")
	proto.RegisterType((*AllianceInfoRet)(nil), "wrpc.AllianceInfoRet")
	proto.RegisterType((*AllianceMemberInfo)(nil), "wrpc.AllianceMemberInfo")
	proto.RegisterType((*GetAllianceMembersInfosRet)(nil), "wrpc.GetAllianceMembersInfosRet")
	proto.RegisterType((*GetAllianceListRet)(nil), "wrpc.GetAllianceListRet")
	proto.RegisterType((*GetUserInfo)(nil), "wrpc.GetUserInfo")
	proto.RegisterMapType((map[int32]int32)(nil), "wrpc.GetUserInfo.LordGemPowerEntry")
	proto.RegisterType((*ActivityInfo)(nil), "wrpc.ActivityInfo")
	proto.RegisterType((*InitSymbioticRet)(nil), "wrpc.InitSymbioticRet")
	proto.RegisterMapType((map[int32]int32)(nil), "wrpc.InitSymbioticRet.ResetItemMapEntry")
	proto.RegisterType((*OneKeyHeroLevelUpgradeRet)(nil), "wrpc.OneKeyHeroLevelUpgradeRet")
	proto.RegisterType((*OneKeyEquipGemReq)(nil), "wrpc.OneKeyEquipGemReq")
}

func init() { proto.RegisterFile("pb1.proto", fileDescriptor_5ab8f08a44d4f49e) }

var fileDescriptor_5ab8f08a44d4f49e = []byte{
	// 2314 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x38, 0x4f, 0x73, 0xe4, 0x46,
	0xf5, 0x3f, 0x79, 0x3c, 0x9e, 0x99, 0x9e, 0x19, 0xdb, 0xdb, 0x71, 0x36, 0x5a, 0x67, 0x7f, 0x8e,
	0x99, 0x40, 0x62, 0x12, 0xe2, 0xd4, 0x86, 0x50, 0xf9, 0x03, 0x84, 0xda, 0xf5, 0xee, 0x4e, 0x9c,
	0xd8, 0x9b, 0xad, 0xb6, 0x37, 0xc5, 0x09, 0x55, 0x8f, 0xd4, 0xa3, 0x69, 0x2c, 0xb5, 0x94, 0xee,
	0x96, 0xbd, 0xfe, 0x00, 0x1c, 0xb8, 0x71, 0x87, 0x1b, 0x27, 0x72, 0xe0, 0xc0, 0x99, 0x1b, 0x97,
	0xdc, 0xe0, 0x23, 0x40, 0xa8, 0xe2, 0x73, 0x50, 0xef, 0xb5, 0x34, 0xa3, 0x91, 0x1d, 0x36, 0x29,
	0xc2, 0x49, 0xef, 0x7f, 0xbf, 0x7e, 0x7f, 0xba, 0x5f, 0x8b, 0xf4, 0xf2, 0xc9, 0x9d, 0xfd, 0x5c,
	0x67, 0x36, 0xa3, 0xab, 0x17, 0x3a, 0x0f, 0xb7, 0xdf, 0x88, 0xa5, 0x9d, 0x15, 0x93, 0xfd, 0x30,
	0x4b, 0xdf, 0x8c, 0xb3, 0x38, 0x7b, 0x13, 0x99, 0x93, 0x62, 0x8a, 0x18, 0x22, 0x08, 0x39, 0xa5,
	0xed, 0x6d, 0x47, 0x31, 0x33, 0xae, 0x45, 0xa0, 0x45, 0x9e, 0xc8, 0x90, 0x1b, 0xc7, 0x1b, 0xbd,
	0x43, 0x86, 0x4c, 0x7c, 0x56, 0x08, 0x63, 0x1f, 0x73, 0xcd, 0x53, 0x43, 0x29, 0x59, 0x3d, 0xbd,
	0xcc, 0x85, 0xef, 0xed, 0x7a, 0x7b, 0x3d, 0x86, 0x30, 0xd0, 0xee, 0x73, 0xcb, 0xfd, 0x95, 0x5d,
	0x6f, 0x6f, 0xc0, 0x10, 0x1e, 0xfd, 0x69, 0x40, 0xba, 0x4f, 0x8c, 0xd0, 0x80, 0xd0, 0x97, 0x49,
	0xeb, 0x89, 0xbc, 0x83, 0x3a, 0xfd, 0xb7, 0x6e, 0xec, 0xa7, 0x52, 0x49, 0x9d, 0x87, 0xfb, 0xc0,
	0x3f, 0x54, 0xd3, 0x8c, 0x01, 0x97, 0xde, 0x21, 0xdd, 0x49, 0x21, 0x93, 0x48, 0xaa, 0xd8, 0x5f,
	0xd9, 0x6d, 0xed, 0xf5, 0xdf, 0x7a, 0x7e, 0x2e, 0x79, 0xaf, 0x64, 0xa0, 0xf4, 0x5c, 0x8c, 0x7e,
	0x8f, 0xac, 0xce, 0x84, 0xce, 0xfc, 0x16, 0x8a, 0x2f, 0x0c, 0x7f, 0x28, 0x74, 0x86, 0xa2, 0xc8,
	0xa6, 0xef, 0x90, 0x01, 0x7c, 0x83, 0x24, 0xb3, 0x56, 0xe8, 0x4b, 0x7f, 0x15, 0xfd, 0xd8, 0x5a,
	0x12, 0x3f, 0x72, 0x3c, 0xd6, 0x9f, 0x2d, 0x10, 0xfa, 0x23, 0xd2, 0x0f, 0x33, 0x65, 0x8a, 0x94,
	0x4f, 0x12, 0x61, 0xfc, 0x36, 0x2e, 0xf3, 0xdc, 0x5c, 0xef, 0x60, 0xce, 0x63, 0x75, 0x39, 0xfa,
	0x1e, 0x19, 0x16, 0x46, 0xe8, 0x60, 0x22, 0x94, 0x98, 0x4a, 0x6b, 0xfc, 0x35, 0x54, 0xdc, 0x5a,
	0xda, 0xf8, 0x3d, 0xc7, 0x64, 0x83, 0x62, 0x81, 0x18, 0xfa, 0x3a, 0xe9, 0xa2, 0xea, 0x2f, 0xb3,
	0x89, 0xdf, 0x41, 0xad, 0xcd, 0x25, 0xad, 0x8f, 0xb2, 0x09, 0xeb, 0x14, 0x0e, 0xa0, 0x77, 0x08,
	0x41, 0x61, 0xab, 0xb3, 0x2c, 0xf7, 0xbb, 0x28, 0x4e, 0x97, 0xc4, 0x4f, 0x81, 0xc3, 0x7a, 0x45,
	0x05, 0xd2, 0x37, 0x48, 0x57, 0x0b, 0x23, 0xb8, 0x0e, 0x67, 0x7e, 0xaf, 0x11, 0x35, 0x56, 0x32,
	0xd8, 0x5c, 0x84, 0xee, 0x93, 0x5e, 0xca, 0xa5, 0x0a, 0x2c, 0x37, 0x67, 0x3e, 0x69, 0xc8, 0x1f,
	0x73, 0xa9, 0x4e, 0xb9, 0x39, 0x63, 0xdd, 0xb4, 0x84, 0x9c, 0x7c, 0x1e, 0x88, 0x73, 0xa1, 0xac,
	0xdf, 0x6f, 0xa4, 0xfb, 0x98, 0xe7, 0x0f, 0x80, 0x01, 0xf2, 0x0e, 0x82, 0x1d, 0x60, 0x66, 0xcc,
	0x99, 0x4c, 0x12, 0x7f, 0xd0, 0xd8, 0x01, 0xe4, 0xe5, 0x04, 0x38, 0xac, 0x37, 0xab, 0x40, 0xfa,
	0x1d, 0xb2, 0x1a, 0xf1, 0x73, 0xe1, 0x0f, 0xd1, 0xfa, 0x70, 0x2e, 0x7c, 0x9f, 0x9f, 0x0b, 0x86,
	0x2c, 0xf0, 0x02, 0xe3, 0x12, 0x41, 0x51, 0xae, 0x37, 0xbc, 0xae, 0x8a, 0x92, 0x61, 0xa0, 0xb1,
	0x3c, 0x3f, 0x20, 0x1b, 0xb8, 0xcb, 0x44, 0x2a, 0x11, 0x18, 0xcb, 0x63, 0xe1, 0x6f, 0xa0, 0xf5,
	0x9b, 0x4b, 0x7b, 0x3d, 0x92, 0x4a, 0x9c, 0x00, 0x97, 0x0d, 0xd3, 0x3a, 0x4a, 0x5f, 0x21, 0x1b,
	0x61, 0xa1, 0x03, 0x23, 0xf4, 0x39, 0x64, 0x43, 0xa6, 0xc2, 0xdf, 0xdc, 0xf5, 0xf6, 0x5a, 0x6c,
	0x18, 0x16, 0xfa, 0x04, 0xa9, 0xa7, 0x32, 0x15, 0x74, 0x44, 0x86, 0xd2, 0x04, 0x4a, 0x5c, 0x04,
	0x79, 0xc2, 0x2f, 0x85, 0xf6, 0x6f, 0xec, 0x7a, 0x7b, 0x5d, 0xd6, 0x97, 0xe6, 0x91, 0xb8, 0x78,
	0x8c, 0x24, 0xfa, 0x22, 0xe9, 0x69, 0x9b, 0x06, 0x36, 0x3b, 0x13, 0xca, 0xa7, 0xd8, 0x64, 0x5d,
	0x6d, 0xd3, 0x53, 0xc0, 0xe9, 0x5d, 0xb2, 0x89, 0x1b, 0x93, 0x3c, 0x0f, 0x26, 0xc5, 0x65, 0x90,
	0x64, 0xb1, 0xff, 0x1c, 0x7a, 0xea, 0x2f, 0x37, 0x15, 0xcf, 0xef, 0x15, 0x97, 0xb0, 0xa6, 0x61,
	0x58, 0x8a, 0x8e, 0x70, 0x94, 0xc5, 0x10, 0xf1, 0x24, 0xd3, 0x51, 0x20, 0x3e, 0x2b, 0x64, 0xee,
	0x6f, 0x35, 0x22, 0x7e, 0x94, 0xe9, 0xe8, 0x01, 0x70, 0x58, 0x2f, 0xa9, 0x40, 0xa8, 0x49, 0x54,
	0x89, 0x45, 0xea, 0x3f, 0xdf, 0xa8, 0x49, 0x50, 0x18, 0x8b, 0x94, 0x75, 0x12, 0x07, 0x40, 0x2c,
	0x2b, 0xe1, 0x40, 0x73, 0x15, 0x65, 0xa9, 0x7f, 0x13, 0x75, 0x6e, 0x5e, 0xd1, 0x41, 0x2e, 0x1b,
	0x26, 0x75, 0x94, 0xbe, 0x46, 0x3a, 0x51, 0xa1, 0x62, 0x91, 0x29, 0xff, 0x85, 0xc6, 0x5a, 0xf7,
	0x1d, 0x9d, 0x55, 0x02, 0xf4, 0x7d, 0x32, 0x9c, 0x16, 0x2a, 0xb4, 0x32, 0x53, 0x41, 0x96, 0x0b,
	0xe5, 0xfb, 0x18, 0x8b, 0xc5, 0xb1, 0xf1, 0xb0, 0xe4, 0x7e, 0x92, 0x0b, 0xc5, 0x06, 0xd3, 0x1a,
	0x06, 0x67, 0x82, 0x12, 0x17, 0x13, 0x29, 0x82, 0xb8, 0x90, 0x91, 0xf0, 0x6f, 0x35, 0xce, 0x84,
	0x47, 0xc8, 0x1c, 0x03, 0x8f, 0xf5, 0xd5, 0x02, 0x81, 0x45, 0x79, 0x68, 0xe5, 0xb9, 0xb4, 0x97,
	0xae, 0x2d, 0xb6, 0x1b, 0x67, 0xd5, 0xdd, 0x92, 0x8b, 0xad, 0x31, 0xe0, 0x35, 0x0c, 0x16, 0x9d,
	0x4a, 0x6d, 0x6c, 0x10, 0xce, 0xb8, 0x8e, 0x85, 0xff, 0x62, 0xe3, 0x5c, 0x78, 0x08, 0xcc, 0x03,
	0xe4, 0xb1, 0xfe, 0x74, 0x81, 0xd0, 0xb7, 0x49, 0x3f, 0xd6, 0xd9, 0x85, 0x9d, 0x05, 0xd3, 0x42,
	0x45, 0xfe, 0xed, 0xc6, 0x41, 0x34, 0x46, 0xde, 0xc3, 0x42, 0x45, 0x8c, 0xc4, 0x73, 0x18, 0x72,
	0x9d, 0x66, 0xca, 0xce, 0x82, 0x90, 0xeb, 0xc8, 0xff, 0xff, 0x46, 0xae, 0x8f, 0x81, 0x75, 0xc0,
	0x75, 0xc4, 0x7a, 0x69, 0x05, 0x82, 0x87, 0x5a, 0xc4, 0x45, 0xc2, 0x75, 0x90, 0xf3, 0xf0, 0xcc,
	0xdf, 0x69, 0x78, 0xc8, 0x1c, 0xf3, 0x31, 0x0f, 0xcf, 0x58, 0x5f, 0x2f, 0x10, 0x7a, 0x40, 0x36,
	0x79, 0x38, 0x93, 0xe2, 0x5c, 0xa4, 0x42, 0x59, 0x17, 0x99, 0x97, 0x50, 0xd9, 0xaf, 0x45, 0x66,
	0x2e, 0x80, 0xc1, 0xd9, 0xe0, 0xcb, 0x84, 0xd1, 0xef, 0x3c, 0xd2, 0x3d, 0xe6, 0x4a, 0x3e, 0x14,
	0xc6, 0x52, 0x9f, 0x74, 0x12, 0xae, 0xe2, 0x27, 0x3a, 0x29, 0x2f, 0x9b, 0x0a, 0xa5, 0x5b, 0xa4,
	0xcd, 0xf3, 0xfc, 0x30, 0xc2, 0x0b, 0xa7, 0xc7, 0x1c, 0x42, 0x5f, 0x21, 0xeb, 0x93, 0x42, 0x45,
	0x89, 0xf8, 0x54, 0x68, 0x23, 0x33, 0xc5, 0xfc, 0x16, 0xb2, 0x1b, 0xd4, 0x2b, 0x72, 0x63, 0xbc,
	0x0f, 0x9a, 0x72, 0x63, 0xba, 0x49, 0x5a, 0x61, 0xa4, 0xfc, 0x36, 0x32, 0x01, 0x1c, 0x9d, 0x90,
	0x0e, 0x13, 0x17, 0x5c, 0x47, 0x86, 0xde, 0x24, 0x6b, 0xd2, 0x8a, 0xf4, 0x30, 0x42, 0xdf, 0xda,
	0xac, 0xc4, 0xe8, 0x6d, 0xd2, 0x03, 0xe8, 0x53, 0x9e, 0x14, 0x02, 0xdd, 0x6b, 0xb1, 0x05, 0x01,
	0x2e, 0x4a, 0x0b, 0x97, 0x67, 0x0b, 0x75, 0x10, 0x1e, 0xfd, 0xd1, 0x23, 0xeb, 0x1f, 0xcb, 0x24,
	0x39, 0xce, 0x94, 0xb1, 0x42, 0x33, 0x61, 0xe9, 0xab, 0xa4, 0xa3, 0xdd, 0x3a, 0xbe, 0x87, 0x21,
	0x1c, 0xee, 0x5f, 0xb8, 0xe0, 0x23, 0x91, 0x55, 0x5c, 0xd8, 0x8a, 0x11, 0x89, 0x08, 0x2d, 0xcb,
	0x8a, 0x58, 0x1c, 0x46, 0x06, 0x2f, 0xce, 0x36, 0x6b, 0x50, 0xe9, 0x6b, 0x64, 0xb3, 0x50, 0x27,
	0xcb, 0x92, 0x2d, 0x94, 0xbc, 0x42, 0xa7, 0x2f, 0x91, 0x3e, 0x9c, 0xb3, 0x01, 0x4f, 0xb3, 0x42,
	0x59, 0x8c, 0x4d, 0x9b, 0x11, 0x20, 0xdd, 0x45, 0xca, 0xe8, 0xf7, 0x1e, 0xd9, 0x18, 0x0b, 0xeb,
	0x9c, 0x61, 0xc2, 0x16, 0x5a, 0xd1, 0x9f, 0x34, 0x3d, 0x1e, 0x39, 0x8f, 0x1b, 0x72, 0xd5, 0x0e,
	0x1e, 0x28, 0xab, 0x2f, 0x17, 0xdb, 0x80, 0x60, 0x9a, 0x87, 0x45, 0x92, 0x60, 0xc4, 0xba, 0xac,
	0xc4, 0xb6, 0xdf, 0x27, 0x83, 0xba, 0x02, 0x64, 0xe4, 0x4c, 0x5c, 0x96, 0x11, 0x07, 0x10, 0x2a,
	0xe1, 0xbc, 0x16, 0x6a, 0x87, 0xbc, 0xbf, 0xf2, 0xae, 0x37, 0x92, 0x64, 0x78, 0x22, 0x54, 0x74,
	0x6c, 0xe2, 0x72, 0x70, 0xf1, 0x49, 0xe7, 0xa1, 0xce, 0xd2, 0x27, 0xd2, 0xa5, 0xac, 0xc5, 0x2a,
	0x14, 0xb2, 0xf2, 0x44, 0x96, 0xb1, 0x6b, 0x31, 0x84, 0x41, 0xfa, 0xd8, 0xc4, 0xa7, 0x55, 0xb2,
	0x86, 0xac, 0x42, 0xc1, 0x89, 0x63, 0x13, 0x97, 0x35, 0x03, 0xe0, 0xe8, 0x8c, 0x6c, 0xc0, 0x52,
	0x8f, 0xef, 0xfd, 0x4f, 0x17, 0x1b, 0xb8, 0xc5, 0xee, 0x12, 0x7a, 0x5c, 0x24, 0x56, 0x2e, 0x6f,
	0xee, 0x75, 0xb2, 0xe6, 0xa0, 0x32, 0xfc, 0xcf, 0xb9, 0xf0, 0x2f, 0x09, 0xb1, 0x52, 0x64, 0xf4,
	0x80, 0x6c, 0xcd, 0x4d, 0xd4, 0x9d, 0x7e, 0xa3, 0x61, 0xe4, 0xf9, 0x85, 0x91, 0x9a, 0xd8, 0xdc,
	0xcc, 0xa7, 0x84, 0x02, 0xeb, 0x34, 0xcb, 0x65, 0xb8, 0x30, 0xb2, 0x45, 0xda, 0x48, 0x29, 0x7b,
	0xd6, 0x21, 0xf5, 0x1d, 0xae, 0x5c, 0xbb, 0xc3, 0xd6, 0x22, 0x9c, 0x3f, 0x27, 0x5b, 0x6e, 0xc9,
	0x6f, 0xdd, 0xf2, 0x1f, 0x3c, 0x72, 0x63, 0x2a, 0x95, 0x34, 0x33, 0x37, 0x87, 0xb8, 0xda, 0xf5,
	0x49, 0xc7, 0x14, 0x61, 0x28, 0x8c, 0x41, 0xcb, 0x5d, 0x56, 0xa1, 0xf4, 0x83, 0x45, 0x55, 0xbb,
	0x81, 0xf4, 0xbb, 0x2e, 0x22, 0x57, 0x6c, 0x5c, 0x5f, 0xd7, 0xff, 0x55, 0xfd, 0xfe, 0x82, 0xf4,
	0x8f, 0xc4, 0xb9, 0x48, 0x4e, 0xac, 0x2e, 0x42, 0x4b, 0x6f, 0x91, 0x2e, 0x1c, 0xe2, 0x81, 0x2c,
	0x3b, 0xac, 0xcd, 0x3a, 0x80, 0x43, 0xc3, 0xbe, 0x48, 0x7a, 0x36, 0xb3, 0x3c, 0x09, 0xc4, 0xd3,
	0x1c, 0xed, 0xb4, 0x59, 0x17, 0x09, 0x0f, 0x9e, 0xe6, 0xf4, 0x05, 0xd2, 0xc1, 0x01, 0x6b, 0x96,
	0x97, 0x0d, 0xbf, 0x06, 0xe8, 0x87, 0xf9, 0xe8, 0x36, 0x21, 0x63, 0x39, 0xb5, 0xa5, 0xf9, 0x75,
	0xb2, 0x22, 0xab, 0xa3, 0x6c, 0x45, 0x46, 0xa3, 0xcf, 0x3d, 0x32, 0xc4, 0xe5, 0x19, 0x57, 0x67,
	0x30, 0x49, 0x43, 0xdd, 0x6a, 0xae, 0xce, 0x4a, 0x19, 0x84, 0x61, 0x3f, 0x85, 0x8c, 0x4a, 0xdf,
	0x01, 0x04, 0x29, 0xc5, 0x53, 0x51, 0x06, 0x1d, 0x61, 0xd8, 0x63, 0x02, 0xa6, 0xca, 0xa3, 0xc4,
	0x21, 0x40, 0x35, 0x61, 0xa6, 0x05, 0x9e, 0xaf, 0x6d, 0xe6, 0x10, 0xd0, 0x97, 0x61, 0xa6, 0xfc,
	0x35, 0xb7, 0x0a, 0xc0, 0x70, 0x20, 0xb9, 0x80, 0xbb, 0xc9, 0xaa, 0xe3, 0x0e, 0x24, 0x47, 0x82,
	0x11, 0x67, 0xf4, 0x6b, 0x8f, 0x0c, 0x1f, 0x67, 0x17, 0x42, 0x7f, 0x3b, 0xce, 0xe6, 0x60, 0x0a,
	0x9d, 0x6d, 0x31, 0x87, 0x7c, 0x7d, 0x67, 0x47, 0x7f, 0xf6, 0xc8, 0xa0, 0x9c, 0x6f, 0x0e, 0x34,
	0x9f, 0x5a, 0xba, 0x4b, 0x06, 0x30, 0x0a, 0x85, 0x80, 0x04, 0xf3, 0x18, 0x93, 0xb8, 0xe4, 0x1f,
	0xc2, 0x95, 0xdb, 0x01, 0x09, 0x39, 0xaf, 0xb2, 0x1d, 0x57, 0x65, 0x75, 0x33, 0xfb, 0x63, 0xb8,
	0x5d, 0xca, 0xfa, 0x5a, 0x8b, 0x11, 0x81, 0x63, 0xb3, 0x3c, 0xa4, 0xdd, 0x7d, 0x52, 0x62, 0xdb,
	0xef, 0x91, 0x7e, 0x4d, 0xfc, 0x59, 0x55, 0xd7, 0xae, 0x57, 0xdd, 0xe7, 0x1e, 0xe9, 0xdf, 0xe7,
	0x32, 0xb9, 0x64, 0xc2, 0x14, 0x09, 0xce, 0xe7, 0x11, 0xa0, 0xee, 0x3e, 0xf7, 0x1a, 0x13, 0x04,
	0x4a, 0xe2, 0x4d, 0xde, 0x8b, 0x2a, 0x90, 0xbe, 0x45, 0xfa, 0x4e, 0x25, 0x9c, 0x09, 0x63, 0x71,
	0x89, 0x6b, 0xc7, 0x6f, 0x67, 0xf8, 0x00, 0x84, 0xe8, 0xdb, 0x64, 0x70, 0x21, 0xc4, 0xd9, 0x5c,
	0xa9, 0xf5, 0x55, 0x4a, 0x7d, 0x27, 0x86, 0x5a, 0xa3, 0x03, 0x72, 0x63, 0x2c, 0xec, 0x31, 0x97,
	0xc9, 0x91, 0x34, 0xb6, 0xf4, 0xd8, 0xbd, 0x58, 0x92, 0x20, 0x91, 0xc6, 0x96, 0x0e, 0x2f, 0xdb,
	0x01, 0x79, 0x7c, 0xb1, 0xa0, 0xd6, 0xe8, 0x57, 0x2d, 0xb2, 0x71, 0x37, 0x49, 0x24, 0x57, 0xa1,
	0xc0, 0x27, 0xa3, 0xb0, 0xf0, 0x48, 0xe2, 0x25, 0xe9, 0xca, 0x9b, 0xb5, 0x92, 0x65, 0x73, 0x11,
	0x7a, 0x8f, 0x6c, 0x56, 0x70, 0x90, 0x8a, 0x74, 0x22, 0x74, 0x95, 0xc9, 0x17, 0xae, 0xa8, 0x1d,
	0x23, 0x9f, 0x6d, 0xf0, 0x25, 0xdc, 0xd0, 0x9f, 0x92, 0xe1, 0xdc, 0x86, 0x99, 0x65, 0x79, 0xf9,
	0xa4, 0xf5, 0xaf, 0x18, 0x38, 0x99, 0x65, 0x30, 0xca, 0xb3, 0x01, 0xaf, 0x11, 0xe8, 0x47, 0x64,
	0xab, 0xe1, 0x42, 0x20, 0xd5, 0x34, 0xf3, 0x57, 0x4b, 0x2b, 0x17, 0x57, 0x7d, 0xc0, 0xcd, 0x52,
	0x7e, 0x85, 0x86, 0x03, 0x6e, 0x65, 0x0b, 0xd3, 0xde, 0x6e, 0x0e, 0xb8, 0x25, 0xb7, 0x1c, 0x70,
	0x6b, 0x18, 0x7d, 0x97, 0xac, 0xcf, 0x75, 0x5d, 0x2a, 0xd7, 0xbe, 0x2a, 0x95, 0xf3, 0x45, 0x5c,
	0x32, 0xff, 0xea, 0x11, 0x7a, 0xd5, 0xc1, 0xaa, 0x6b, 0xbd, 0x45, 0xd7, 0xde, 0x22, 0x5d, 0x7c,
	0xa2, 0x05, 0x65, 0x33, 0xb7, 0x59, 0x07, 0xf1, 0xc3, 0x68, 0xd1, 0xbc, 0xad, 0x7a, 0xf3, 0x56,
	0x6d, 0xbe, 0x5a, 0x6b, 0xf3, 0x3d, 0xb2, 0xc9, 0xcf, 0xb9, 0xe5, 0x3a, 0x08, 0x33, 0x35, 0x95,
	0x31, 0x18, 0x73, 0xbd, 0xbd, 0xee, 0xe8, 0x07, 0x48, 0x3e, 0x8c, 0xe0, 0x00, 0x35, 0x56, 0xe4,
	0x20, 0xe0, 0xfa, 0x7c, 0x0d, 0xd0, 0xc3, 0xe8, 0xd9, 0xc7, 0xd2, 0x11, 0xd9, 0x1e, 0x0b, 0xbb,
	0xbc, 0x27, 0x03, 0x9b, 0x32, 0x50, 0x63, 0xfb, 0xa4, 0x0d, 0x19, 0xaa, 0xee, 0xda, 0xaf, 0x4e,
	0x91, 0x13, 0x1b, 0x1d, 0x10, 0x5a, 0xb3, 0xe6, 0x0a, 0xbe, 0x59, 0xa9, 0xad, 0x67, 0x54, 0xea,
	0xe8, 0xb7, 0xab, 0x70, 0x34, 0xd8, 0xea, 0xbf, 0xcb, 0x35, 0xd1, 0xad, 0x82, 0xb5, 0x52, 0x0b,
	0xd6, 0xcb, 0xb5, 0x82, 0xa8, 0x1d, 0x98, 0xf3, 0xcc, 0x3f, 0x02, 0xa1, 0xef, 0xd7, 0x9a, 0x80,
	0x87, 0x3a, 0x53, 0x97, 0x69, 0x19, 0xf1, 0x79, 0xad, 0xdf, 0x75, 0x64, 0xb8, 0xb0, 0x52, 0xfe,
	0xb4, 0x7c, 0x68, 0xbb, 0xa8, 0x77, 0x53, 0xfe, 0xd4, 0xbd, 0xa5, 0xe7, 0x39, 0x5c, 0xab, 0xe7,
	0xf0, 0x07, 0x84, 0x4e, 0xb5, 0x14, 0x2a, 0x72, 0x5a, 0x41, 0x22, 0x53, 0x69, 0xcb, 0x98, 0x6f,
	0x3a, 0x0e, 0xaa, 0x1f, 0x01, 0x9d, 0xbe, 0x4a, 0x60, 0xcd, 0xec, 0x22, 0x30, 0x56, 0x73, 0x15,
	0x43, 0x3f, 0x76, 0xf1, 0x66, 0x5f, 0x47, 0xf2, 0x49, 0x45, 0xbd, 0xb6, 0x0c, 0x7a, 0xd7, 0x96,
	0x41, 0xf5, 0xa7, 0x89, 0xfc, 0xe7, 0x3f, 0x4d, 0xcb, 0xaf, 0xeb, 0xfe, 0xd7, 0x79, 0x5d, 0x8f,
	0xc9, 0xa0, 0x7c, 0x01, 0xe3, 0x1d, 0x56, 0xfe, 0x04, 0x79, 0x79, 0x3e, 0x3f, 0x57, 0xc9, 0xaa,
	0xee, 0x03, 0x94, 0x72, 0x17, 0xc1, 0x92, 0xe2, 0xf6, 0xcf, 0xc8, 0x8d, 0x2b, 0x22, 0xdf, 0xe8,
	0xf0, 0xff, 0x97, 0x47, 0x06, 0xd5, 0xe3, 0x15, 0xcb, 0xe3, 0x36, 0xe9, 0x19, 0xcb, 0xb5, 0x85,
	0x72, 0x2e, 0x8b, 0x64, 0x41, 0x80, 0xb9, 0x09, 0xc6, 0x3f, 0x99, 0x56, 0xd3, 0x4b, 0x85, 0xc2,
	0xc5, 0x64, 0x2c, 0xb7, 0x85, 0xa9, 0x2e, 0x26, 0x87, 0xd1, 0x11, 0x99, 0x3f, 0x87, 0x1f, 0x2d,
	0x3a, 0x72, 0x89, 0x46, 0x77, 0x08, 0xa9, 0xf0, 0xc3, 0xaa, 0x27, 0x6b, 0x14, 0xe0, 0xe7, 0xc5,
	0x24, 0x91, 0x21, 0x2e, 0xec, 0x8a, 0xa4, 0x46, 0xa9, 0xaf, 0x81, 0x43, 0xa1, 0xab, 0x91, 0x25,
	0xda, 0xe8, 0x2f, 0x1e, 0xd9, 0x3c, 0x54, 0xd2, 0x9e, 0x5c, 0xa6, 0x13, 0x99, 0x59, 0x19, 0x42,
	0x2b, 0xed, 0x92, 0xbe, 0x16, 0x46, 0x58, 0xcc, 0x68, 0x54, 0x0e, 0x59, 0x75, 0x12, 0x3d, 0x82,
	0xb7, 0xb1, 0x11, 0xf6, 0xd0, 0x8a, 0xf4, 0x98, 0xe7, 0xe5, 0x19, 0xbf, 0xe7, 0x32, 0xd5, 0xb4,
	0x87, 0x7f, 0xd3, 0x2a, 0xd1, 0x32, 0x5d, 0x75, 0x6d, 0x48, 0xd7, 0x15, 0x91, 0x6f, 0x94, 0xae,
	0x8f, 0xc9, 0xad, 0x4f, 0x94, 0xf8, 0x58, 0x5c, 0xe2, 0xef, 0x4b, 0x18, 0xaa, 0x9e, 0xe4, 0xb1,
	0xe6, 0x91, 0x80, 0xdd, 0xdc, 0x24, 0x38, 0xe8, 0x2d, 0xde, 0xa7, 0x0e, 0xab, 0xcd, 0x0c, 0x2b,
	0xf5, 0x99, 0x61, 0xf4, 0x63, 0x72, 0xc3, 0x19, 0xc3, 0xa2, 0x1c, 0x8b, 0x94, 0x89, 0xcf, 0x60,
	0xed, 0x71, 0xed, 0x8d, 0xeb, 0x10, 0xf0, 0xf1, 0x71, 0x66, 0x4a, 0x7d, 0x00, 0xef, 0x6d, 0x7d,
	0xf1, 0x8f, 0x1d, 0xef, 0x8b, 0x2f, 0x77, 0xbc, 0xbf, 0x7d, 0xb9, 0xe3, 0xfd, 0xfd, 0xcb, 0x1d,
	0xef, 0x37, 0xff, 0xdc, 0xf9, 0xbf, 0xc9, 0x1a, 0xfe, 0x41, 0xfe, 0xe1, 0xbf, 0x03, 0x00, 0x00,
	0xff, 0xff, 0x4a, 0x62, 0xa2, 0x4c, 0x9f, 0x16, 0x00, 0x00,
}

func (m *RequestParams) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RequestParams) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RequestParams) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Data) > 0 {
		i -= len(m.Data)
		copy(dAtA[i:], m.Data)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.Data)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Type) > 0 {
		i -= len(m.Type)
		copy(dAtA[i:], m.Type)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.Type)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *UserData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserData) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UserData) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.AchievementTask) > 0 {
		for iNdEx := len(m.AchievementTask) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.AchievementTask[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xfa
		}
	}
	if len(m.RegularPack) > 0 {
		for iNdEx := len(m.RegularPack) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.RegularPack[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xf2
		}
	}
	if len(m.MonthCard) > 0 {
		for iNdEx := len(m.MonthCard) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.MonthCard[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xea
		}
	}
	if len(m.GrowthFund) > 0 {
		for iNdEx := len(m.GrowthFund) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.GrowthFund[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xe2
		}
	}
	if len(m.FirstCharge) > 0 {
		for iNdEx := len(m.FirstCharge) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.FirstCharge[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xda
		}
	}
	if len(m.ActivityTask) > 0 {
		for iNdEx := len(m.ActivityTask) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.ActivityTask[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xd2
		}
	}
	if m.NewbieGuide != nil {
		{
			size, err := m.NewbieGuide.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintPb1(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xca
	}
	if m.FunctionOpen != nil {
		{
			size, err := m.FunctionOpen.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintPb1(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xc2
	}
	if len(m.Dungeon) > 0 {
		for iNdEx := len(m.Dungeon) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Dungeon[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xba
		}
	}
	if len(m.LordGemRandom) > 0 {
		for iNdEx := len(m.LordGemRandom) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.LordGemRandom[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xb2
		}
	}
	if len(m.LordGem) > 0 {
		for iNdEx := len(m.LordGem) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.LordGem[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xaa
		}
	}
	if len(m.LordEquip) > 0 {
		for iNdEx := len(m.LordEquip) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.LordEquip[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xa2
		}
	}
	if m.UserIapBuyLog != nil {
		{
			size, err := m.UserIapBuyLog.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintPb1(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x9a
	}
	if len(m.RtmToken) > 0 {
		i -= len(m.RtmToken)
		copy(dAtA[i:], m.RtmToken)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.RtmToken)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x92
	}
	if m.IsNewPlayer {
		i--
		if m.IsNewPlayer {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x88
	}
	if m.CurServerTime != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.CurServerTime))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x80
	}
	if m.MainLineStage != nil {
		{
			size, err := m.MainLineStage.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintPb1(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x7a
	}
	if len(m.UserData) > 0 {
		for iNdEx := len(m.UserData) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.UserData[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x72
		}
	}
	if m.Dave != nil {
		{
			size, err := m.Dave.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintPb1(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x6a
	}
	if len(m.HeroSkill) > 0 {
		for iNdEx := len(m.HeroSkill) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.HeroSkill[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x62
		}
	}
	if m.MapEvent != nil {
		{
			size, err := m.MapEvent.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintPb1(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x5a
	}
	if len(m.MainTask) > 0 {
		for iNdEx := len(m.MainTask) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.MainTask[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x52
		}
	}
	if len(m.Research) > 0 {
		for iNdEx := len(m.Research) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Research[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x4a
		}
	}
	if len(m.UserTroop) > 0 {
		for iNdEx := len(m.UserTroop) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.UserTroop[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x42
		}
	}
	if len(m.UserJob) > 0 {
		for iNdEx := len(m.UserJob) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.UserJob[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x3a
		}
	}
	if len(m.UserBenefits) > 0 {
		for iNdEx := len(m.UserBenefits) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.UserBenefits[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x32
		}
	}
	if len(m.Consumables) > 0 {
		for iNdEx := len(m.Consumables) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Consumables[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.HeroLottery != nil {
		{
			size, err := m.HeroLottery.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintPb1(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if len(m.Hero) > 0 {
		for iNdEx := len(m.Hero) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Hero[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.Building) > 0 {
		for iNdEx := len(m.Building) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Building[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Ui1 != nil {
		{
			size, err := m.Ui1.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintPb1(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ManiFest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ManiFest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ManiFest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Cdn) > 0 {
		i -= len(m.Cdn)
		copy(dAtA[i:], m.Cdn)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.Cdn)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.BundleVersionG) > 0 {
		i -= len(m.BundleVersionG)
		copy(dAtA[i:], m.BundleVersionG)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.BundleVersionG)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.BundleVersionR) > 0 {
		i -= len(m.BundleVersionR)
		copy(dAtA[i:], m.BundleVersionR)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.BundleVersionR)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.AppId) > 0 {
		i -= len(m.AppId)
		copy(dAtA[i:], m.AppId)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.AppId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.LangUrl) > 0 {
		i -= len(m.LangUrl)
		copy(dAtA[i:], m.LangUrl)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.LangUrl)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Rewards) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Rewards) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Rewards) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Type != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x18
	}
	if m.ItemValue != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.ItemValue))
		i--
		dAtA[i] = 0x10
	}
	if m.ItemId != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.ItemId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *KillMonsterRet) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KillMonsterRet) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *KillMonsterRet) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.KillAmount != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.KillAmount))
		i--
		dAtA[i] = 0x20
	}
	if len(m.UnSelectRougeIds) > 0 {
		dAtA10 := make([]byte, len(m.UnSelectRougeIds)*10)
		var j9 int
		for _, num1 := range m.UnSelectRougeIds {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA10[j9] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j9++
			}
			dAtA10[j9] = uint8(num)
			j9++
		}
		i -= j9
		copy(dAtA[i:], dAtA10[:j9])
		i = encodeVarintPb1(dAtA, i, uint64(j9))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.SelectRougeIds) > 0 {
		dAtA12 := make([]byte, len(m.SelectRougeIds)*10)
		var j11 int
		for _, num1 := range m.SelectRougeIds {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA12[j11] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j11++
			}
			dAtA12[j11] = uint8(num)
			j11++
		}
		i -= j11
		copy(dAtA[i:], dAtA12[:j11])
		i = encodeVarintPb1(dAtA, i, uint64(j11))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Rewards) > 0 {
		for iNdEx := len(m.Rewards) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rewards[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetRewardReturn) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRewardReturn) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetRewardReturn) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.IsFull {
		i--
		if m.IsFull {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if len(m.Rewards) > 0 {
		for k := range m.Rewards {
			v := m.Rewards[k]
			baseI := i
			i = encodeVarintPb1(dAtA, i, uint64(v))
			i--
			dAtA[i] = 0x10
			i = encodeVarintPb1(dAtA, i, uint64(k))
			i--
			dAtA[i] = 0x8
			i = encodeVarintPb1(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *SendMsgParams) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendMsgParams) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SendMsgParams) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Msg) > 0 {
		i -= len(m.Msg)
		copy(dAtA[i:], m.Msg)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.Msg)))
		i--
		dAtA[i] = 0x22
	}
	if m.MsgType != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.MsgType))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Uids) > 0 {
		dAtA14 := make([]byte, len(m.Uids)*10)
		var j13 int
		for _, num1 := range m.Uids {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA14[j13] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j13++
			}
			dAtA14[j13] = uint8(num)
			j13++
		}
		i -= j13
		copy(dAtA[i:], dAtA14[:j13])
		i = encodeVarintPb1(dAtA, i, uint64(j13))
		i--
		dAtA[i] = 0x12
	}
	if m.FromUid != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.FromUid))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SendPBMsgParams) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendPBMsgParams) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SendPBMsgParams) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Msg) > 0 {
		i -= len(m.Msg)
		copy(dAtA[i:], m.Msg)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.Msg)))
		i--
		dAtA[i] = 0x22
	}
	if m.MsgType != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.MsgType))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Uids) > 0 {
		dAtA16 := make([]byte, len(m.Uids)*10)
		var j15 int
		for _, num1 := range m.Uids {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA16[j15] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j15++
			}
			dAtA16[j15] = uint8(num)
			j15++
		}
		i -= j15
		copy(dAtA[i:], dAtA16[:j15])
		i = encodeVarintPb1(dAtA, i, uint64(j15))
		i--
		dAtA[i] = 0x12
	}
	if m.FromUid != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.FromUid))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *MultiSendMsgParams) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MultiSendMsgParams) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MultiSendMsgParams) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Params) > 0 {
		for iNdEx := len(m.Params) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Params[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *MultiSendPBMsgParams) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MultiSendPBMsgParams) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MultiSendPBMsgParams) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Params) > 0 {
		for iNdEx := len(m.Params) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Params[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *SendTopicMsgParams) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendTopicMsgParams) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SendTopicMsgParams) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Msg) > 0 {
		i -= len(m.Msg)
		copy(dAtA[i:], m.Msg)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.Msg)))
		i--
		dAtA[i] = 0x1a
	}
	if m.MsgType != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.MsgType))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Topic) > 0 {
		i -= len(m.Topic)
		copy(dAtA[i:], m.Topic)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.Topic)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SendPBTopicMsgParams) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendPBTopicMsgParams) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SendPBTopicMsgParams) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Msg) > 0 {
		i -= len(m.Msg)
		copy(dAtA[i:], m.Msg)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.Msg)))
		i--
		dAtA[i] = 0x1a
	}
	if m.MsgType != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.MsgType))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Topic) > 0 {
		i -= len(m.Topic)
		copy(dAtA[i:], m.Topic)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.Topic)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *FinishEventReturn) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FinishEventReturn) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FinishEventReturn) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rewards) > 0 {
		for k := range m.Rewards {
			v := m.Rewards[k]
			baseI := i
			i = encodeVarintPb1(dAtA, i, uint64(v))
			i--
			dAtA[i] = 0x10
			i = encodeVarintPb1(dAtA, i, uint64(k))
			i--
			dAtA[i] = 0x8
			i = encodeVarintPb1(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Success {
		i--
		if m.Success {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *LevelStruct) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LevelStruct) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LevelStruct) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.HeroHp) > 0 {
		dAtA18 := make([]byte, len(m.HeroHp)*10)
		var j17 int
		for _, num1 := range m.HeroHp {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA18[j17] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j17++
			}
			dAtA18[j17] = uint8(num)
			j17++
		}
		i -= j17
		copy(dAtA[i:], dAtA18[:j17])
		i = encodeVarintPb1(dAtA, i, uint64(j17))
		i--
		dAtA[i] = 0x1a
	}
	if m.TotalExp != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.TotalExp))
		i--
		dAtA[i] = 0x10
	}
	if len(m.CardIds) > 0 {
		dAtA20 := make([]byte, len(m.CardIds)*10)
		var j19 int
		for _, num1 := range m.CardIds {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA20[j19] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j19++
			}
			dAtA20[j19] = uint8(num)
			j19++
		}
		i -= j19
		copy(dAtA[i:], dAtA20[:j19])
		i = encodeVarintPb1(dAtA, i, uint64(j19))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GiftStruct) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GiftStruct) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GiftStruct) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Id != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *LevelRankInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LevelRankInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LevelRankInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.FinishTime != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.FinishTime))
		i--
		dAtA[i] = 0x38
	}
	if m.Icon != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Icon))
		i--
		dAtA[i] = 0x30
	}
	if m.Score != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Score))
		i--
		dAtA[i] = 0x28
	}
	if m.Level != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Level))
		i--
		dAtA[i] = 0x20
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Uid != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Uid))
		i--
		dAtA[i] = 0x10
	}
	if m.Rank != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Rank))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *PowerRankInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PowerRankInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PowerRankInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Icon != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Icon))
		i--
		dAtA[i] = 0x30
	}
	if m.Score != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Score))
		i--
		dAtA[i] = 0x28
	}
	if m.Power != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Power))
		i--
		dAtA[i] = 0x20
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Uid != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Uid))
		i--
		dAtA[i] = 0x10
	}
	if m.Rank != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Rank))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *LordGemCraft) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LordGemCraft) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LordGemCraft) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Amount != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Amount))
		i--
		dAtA[i] = 0x18
	}
	if len(m.GemIds) > 0 {
		for k := range m.GemIds {
			v := m.GemIds[k]
			baseI := i
			i = encodeVarintPb1(dAtA, i, uint64(v))
			i--
			dAtA[i] = 0x10
			i = encodeVarintPb1(dAtA, i, uint64(k))
			i--
			dAtA[i] = 0x8
			i = encodeVarintPb1(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x12
		}
	}
	if m.GemCraftId != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.GemCraftId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DailyResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DailyResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DailyResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.WeeklyChest != nil {
		{
			size, err := m.WeeklyChest.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintPb1(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.DailyChest != nil {
		{
			size, err := m.DailyChest.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintPb1(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.DailyTask) > 0 {
		for iNdEx := len(m.DailyTask) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.DailyTask[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetMailListResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMailListResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetMailListResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.MailList) > 0 {
		for iNdEx := len(m.MailList) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.MailList[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *AllianceInfoRet) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AllianceInfoRet) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AllianceInfoRet) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.AllianceChest != nil {
		{
			size, err := m.AllianceChest.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintPb1(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if len(m.AllianceTask) > 0 {
		for iNdEx := len(m.AllianceTask) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.AllianceTask[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.AllianceMemberInfo) > 0 {
		for iNdEx := len(m.AllianceMemberInfo) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.AllianceMemberInfo[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.AllianceShop) > 0 {
		for iNdEx := len(m.AllianceShop) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.AllianceShop[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.AllianceMembers) > 0 {
		for iNdEx := len(m.AllianceMembers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.AllianceMembers[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Alliance != nil {
		{
			size, err := m.Alliance.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintPb1(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *AllianceMemberInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AllianceMemberInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AllianceMemberInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.FinishTime != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.FinishTime))
		i--
		dAtA[i] = 0x38
	}
	if m.StepId != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.StepId))
		i--
		dAtA[i] = 0x30
	}
	if m.AvatarConfigId != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.AvatarConfigId))
		i--
		dAtA[i] = 0x28
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x22
	}
	if m.Power != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Power))
		i--
		dAtA[i] = 0x18
	}
	if m.StageId != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.StageId))
		i--
		dAtA[i] = 0x10
	}
	if m.Uid != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Uid))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetAllianceMembersInfosRet) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllianceMembersInfosRet) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetAllianceMembersInfosRet) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Infos) > 0 {
		for iNdEx := len(m.Infos) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Infos[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetAllianceListRet) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllianceListRet) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetAllianceListRet) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Alliance) > 0 {
		for iNdEx := len(m.Alliance) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Alliance[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetUserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetUserInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.LordGemPower) > 0 {
		for k := range m.LordGemPower {
			v := m.LordGemPower[k]
			baseI := i
			i = encodeVarintPb1(dAtA, i, uint64(v))
			i--
			dAtA[i] = 0x10
			i = encodeVarintPb1(dAtA, i, uint64(k))
			i--
			dAtA[i] = 0x8
			i = encodeVarintPb1(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x62
		}
	}
	if len(m.LordEquip) > 0 {
		for iNdEx := len(m.LordEquip) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.LordEquip[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x5a
		}
	}
	if len(m.Hero) > 0 {
		for iNdEx := len(m.Hero) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Hero[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPb1(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x52
		}
	}
	if m.AvatarConfigId != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.AvatarConfigId))
		i--
		dAtA[i] = 0x48
	}
	if m.AllowStrangers {
		i--
		if m.AllowStrangers {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x40
	}
	if m.FriendStageLimit != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.FriendStageLimit))
		i--
		dAtA[i] = 0x38
	}
	if m.Power != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Power))
		i--
		dAtA[i] = 0x30
	}
	if m.MaxStage != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.MaxStage))
		i--
		dAtA[i] = 0x28
	}
	if len(m.AllianceAcronym) > 0 {
		i -= len(m.AllianceAcronym)
		copy(dAtA[i:], m.AllianceAcronym)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.AllianceAcronym)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.AllianceName) > 0 {
		i -= len(m.AllianceName)
		copy(dAtA[i:], m.AllianceName)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.AllianceName)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x12
	}
	if m.Uid != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Uid))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ActivityInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActivityInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ActivityInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ActivityType != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.ActivityType))
		i--
		dAtA[i] = 0x38
	}
	if m.PublicTime != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.PublicTime))
		i--
		dAtA[i] = 0x30
	}
	if m.ActivityId != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.ActivityId))
		i--
		dAtA[i] = 0x28
	}
	if len(m.ActivityName) > 0 {
		i -= len(m.ActivityName)
		copy(dAtA[i:], m.ActivityName)
		i = encodeVarintPb1(dAtA, i, uint64(len(m.ActivityName)))
		i--
		dAtA[i] = 0x22
	}
	if m.Status != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Status))
		i--
		dAtA[i] = 0x18
	}
	if m.EndTime != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.EndTime))
		i--
		dAtA[i] = 0x10
	}
	if m.StartTime != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.StartTime))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *InitSymbioticRet) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InitSymbioticRet) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *InitSymbioticRet) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ResetItemMap) > 0 {
		for k := range m.ResetItemMap {
			v := m.ResetItemMap[k]
			baseI := i
			i = encodeVarintPb1(dAtA, i, uint64(v))
			i--
			dAtA[i] = 0x10
			i = encodeVarintPb1(dAtA, i, uint64(k))
			i--
			dAtA[i] = 0x8
			i = encodeVarintPb1(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.ResetHeroId) > 0 {
		dAtA26 := make([]byte, len(m.ResetHeroId)*10)
		var j25 int
		for _, num1 := range m.ResetHeroId {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA26[j25] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j25++
			}
			dAtA26[j25] = uint8(num)
			j25++
		}
		i -= j25
		copy(dAtA[i:], dAtA26[:j25])
		i = encodeVarintPb1(dAtA, i, uint64(j25))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *OneKeyHeroLevelUpgradeRet) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OneKeyHeroLevelUpgradeRet) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OneKeyHeroLevelUpgradeRet) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Amount != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Amount))
		i--
		dAtA[i] = 0x10
	}
	if m.HeroId != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.HeroId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *OneKeyEquipGemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OneKeyEquipGemReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OneKeyEquipGemReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Pos != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.Pos))
		i--
		dAtA[i] = 0x10
	}
	if m.GemId != 0 {
		i = encodeVarintPb1(dAtA, i, uint64(m.GemId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintPb1(dAtA []byte, offset int, v uint64) int {
	offset -= sovPb1(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *RequestParams) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Type)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	return n
}

func (m *UserData) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Ui1 != nil {
		l = m.Ui1.Size()
		n += 1 + l + sovPb1(uint64(l))
	}
	if len(m.Building) > 0 {
		for _, e := range m.Building {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if len(m.Hero) > 0 {
		for _, e := range m.Hero {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if m.HeroLottery != nil {
		l = m.HeroLottery.Size()
		n += 1 + l + sovPb1(uint64(l))
	}
	if len(m.Consumables) > 0 {
		for _, e := range m.Consumables {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if len(m.UserBenefits) > 0 {
		for _, e := range m.UserBenefits {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if len(m.UserJob) > 0 {
		for _, e := range m.UserJob {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if len(m.UserTroop) > 0 {
		for _, e := range m.UserTroop {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if len(m.Research) > 0 {
		for _, e := range m.Research {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if len(m.MainTask) > 0 {
		for _, e := range m.MainTask {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if m.MapEvent != nil {
		l = m.MapEvent.Size()
		n += 1 + l + sovPb1(uint64(l))
	}
	if len(m.HeroSkill) > 0 {
		for _, e := range m.HeroSkill {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if m.Dave != nil {
		l = m.Dave.Size()
		n += 1 + l + sovPb1(uint64(l))
	}
	if len(m.UserData) > 0 {
		for _, e := range m.UserData {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if m.MainLineStage != nil {
		l = m.MainLineStage.Size()
		n += 1 + l + sovPb1(uint64(l))
	}
	if m.CurServerTime != 0 {
		n += 2 + sovPb1(uint64(m.CurServerTime))
	}
	if m.IsNewPlayer {
		n += 3
	}
	l = len(m.RtmToken)
	if l > 0 {
		n += 2 + l + sovPb1(uint64(l))
	}
	if m.UserIapBuyLog != nil {
		l = m.UserIapBuyLog.Size()
		n += 2 + l + sovPb1(uint64(l))
	}
	if len(m.LordEquip) > 0 {
		for _, e := range m.LordEquip {
			l = e.Size()
			n += 2 + l + sovPb1(uint64(l))
		}
	}
	if len(m.LordGem) > 0 {
		for _, e := range m.LordGem {
			l = e.Size()
			n += 2 + l + sovPb1(uint64(l))
		}
	}
	if len(m.LordGemRandom) > 0 {
		for _, e := range m.LordGemRandom {
			l = e.Size()
			n += 2 + l + sovPb1(uint64(l))
		}
	}
	if len(m.Dungeon) > 0 {
		for _, e := range m.Dungeon {
			l = e.Size()
			n += 2 + l + sovPb1(uint64(l))
		}
	}
	if m.FunctionOpen != nil {
		l = m.FunctionOpen.Size()
		n += 2 + l + sovPb1(uint64(l))
	}
	if m.NewbieGuide != nil {
		l = m.NewbieGuide.Size()
		n += 2 + l + sovPb1(uint64(l))
	}
	if len(m.ActivityTask) > 0 {
		for _, e := range m.ActivityTask {
			l = e.Size()
			n += 2 + l + sovPb1(uint64(l))
		}
	}
	if len(m.FirstCharge) > 0 {
		for _, e := range m.FirstCharge {
			l = e.Size()
			n += 2 + l + sovPb1(uint64(l))
		}
	}
	if len(m.GrowthFund) > 0 {
		for _, e := range m.GrowthFund {
			l = e.Size()
			n += 2 + l + sovPb1(uint64(l))
		}
	}
	if len(m.MonthCard) > 0 {
		for _, e := range m.MonthCard {
			l = e.Size()
			n += 2 + l + sovPb1(uint64(l))
		}
	}
	if len(m.RegularPack) > 0 {
		for _, e := range m.RegularPack {
			l = e.Size()
			n += 2 + l + sovPb1(uint64(l))
		}
	}
	if len(m.AchievementTask) > 0 {
		for _, e := range m.AchievementTask {
			l = e.Size()
			n += 2 + l + sovPb1(uint64(l))
		}
	}
	return n
}

func (m *ManiFest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.LangUrl)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	l = len(m.AppId)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	l = len(m.BundleVersionR)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	l = len(m.BundleVersionG)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	l = len(m.Cdn)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	return n
}

func (m *Rewards) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ItemId != 0 {
		n += 1 + sovPb1(uint64(m.ItemId))
	}
	if m.ItemValue != 0 {
		n += 1 + sovPb1(uint64(m.ItemValue))
	}
	if m.Type != 0 {
		n += 1 + sovPb1(uint64(m.Type))
	}
	return n
}

func (m *KillMonsterRet) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rewards) > 0 {
		for _, e := range m.Rewards {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if len(m.SelectRougeIds) > 0 {
		l = 0
		for _, e := range m.SelectRougeIds {
			l += sovPb1(uint64(e))
		}
		n += 1 + sovPb1(uint64(l)) + l
	}
	if len(m.UnSelectRougeIds) > 0 {
		l = 0
		for _, e := range m.UnSelectRougeIds {
			l += sovPb1(uint64(e))
		}
		n += 1 + sovPb1(uint64(l)) + l
	}
	if m.KillAmount != 0 {
		n += 1 + sovPb1(uint64(m.KillAmount))
	}
	return n
}

func (m *GetRewardReturn) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rewards) > 0 {
		for k, v := range m.Rewards {
			_ = k
			_ = v
			mapEntrySize := 1 + sovPb1(uint64(k)) + 1 + sovPb1(uint64(v))
			n += mapEntrySize + 1 + sovPb1(uint64(mapEntrySize))
		}
	}
	if m.IsFull {
		n += 2
	}
	return n
}

func (m *SendMsgParams) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.FromUid != 0 {
		n += 1 + sovPb1(uint64(m.FromUid))
	}
	if len(m.Uids) > 0 {
		l = 0
		for _, e := range m.Uids {
			l += sovPb1(uint64(e))
		}
		n += 1 + sovPb1(uint64(l)) + l
	}
	if m.MsgType != 0 {
		n += 1 + sovPb1(uint64(m.MsgType))
	}
	l = len(m.Msg)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	return n
}

func (m *SendPBMsgParams) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.FromUid != 0 {
		n += 1 + sovPb1(uint64(m.FromUid))
	}
	if len(m.Uids) > 0 {
		l = 0
		for _, e := range m.Uids {
			l += sovPb1(uint64(e))
		}
		n += 1 + sovPb1(uint64(l)) + l
	}
	if m.MsgType != 0 {
		n += 1 + sovPb1(uint64(m.MsgType))
	}
	l = len(m.Msg)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	return n
}

func (m *MultiSendMsgParams) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Params) > 0 {
		for _, e := range m.Params {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	return n
}

func (m *MultiSendPBMsgParams) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Params) > 0 {
		for _, e := range m.Params {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	return n
}

func (m *SendTopicMsgParams) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Topic)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	if m.MsgType != 0 {
		n += 1 + sovPb1(uint64(m.MsgType))
	}
	l = len(m.Msg)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	return n
}

func (m *SendPBTopicMsgParams) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Topic)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	if m.MsgType != 0 {
		n += 1 + sovPb1(uint64(m.MsgType))
	}
	l = len(m.Msg)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	return n
}

func (m *FinishEventReturn) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Success {
		n += 2
	}
	if len(m.Rewards) > 0 {
		for k, v := range m.Rewards {
			_ = k
			_ = v
			mapEntrySize := 1 + sovPb1(uint64(k)) + 1 + sovPb1(uint64(v))
			n += mapEntrySize + 1 + sovPb1(uint64(mapEntrySize))
		}
	}
	return n
}

func (m *LevelStruct) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.CardIds) > 0 {
		l = 0
		for _, e := range m.CardIds {
			l += sovPb1(uint64(e))
		}
		n += 1 + sovPb1(uint64(l)) + l
	}
	if m.TotalExp != 0 {
		n += 1 + sovPb1(uint64(m.TotalExp))
	}
	if len(m.HeroHp) > 0 {
		l = 0
		for _, e := range m.HeroHp {
			l += sovPb1(uint64(e))
		}
		n += 1 + sovPb1(uint64(l)) + l
	}
	return n
}

func (m *GiftStruct) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovPb1(uint64(m.Id))
	}
	return n
}

func (m *LevelRankInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Rank != 0 {
		n += 1 + sovPb1(uint64(m.Rank))
	}
	if m.Uid != 0 {
		n += 1 + sovPb1(uint64(m.Uid))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	if m.Level != 0 {
		n += 1 + sovPb1(uint64(m.Level))
	}
	if m.Score != 0 {
		n += 1 + sovPb1(uint64(m.Score))
	}
	if m.Icon != 0 {
		n += 1 + sovPb1(uint64(m.Icon))
	}
	if m.FinishTime != 0 {
		n += 1 + sovPb1(uint64(m.FinishTime))
	}
	return n
}

func (m *PowerRankInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Rank != 0 {
		n += 1 + sovPb1(uint64(m.Rank))
	}
	if m.Uid != 0 {
		n += 1 + sovPb1(uint64(m.Uid))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	if m.Power != 0 {
		n += 1 + sovPb1(uint64(m.Power))
	}
	if m.Score != 0 {
		n += 1 + sovPb1(uint64(m.Score))
	}
	if m.Icon != 0 {
		n += 1 + sovPb1(uint64(m.Icon))
	}
	return n
}

func (m *LordGemCraft) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.GemCraftId != 0 {
		n += 1 + sovPb1(uint64(m.GemCraftId))
	}
	if len(m.GemIds) > 0 {
		for k, v := range m.GemIds {
			_ = k
			_ = v
			mapEntrySize := 1 + sovPb1(uint64(k)) + 1 + sovPb1(uint64(v))
			n += mapEntrySize + 1 + sovPb1(uint64(mapEntrySize))
		}
	}
	if m.Amount != 0 {
		n += 1 + sovPb1(uint64(m.Amount))
	}
	return n
}

func (m *DailyResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.DailyTask) > 0 {
		for _, e := range m.DailyTask {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if m.DailyChest != nil {
		l = m.DailyChest.Size()
		n += 1 + l + sovPb1(uint64(l))
	}
	if m.WeeklyChest != nil {
		l = m.WeeklyChest.Size()
		n += 1 + l + sovPb1(uint64(l))
	}
	return n
}

func (m *GetMailListResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.MailList) > 0 {
		for _, e := range m.MailList {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	return n
}

func (m *AllianceInfoRet) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Alliance != nil {
		l = m.Alliance.Size()
		n += 1 + l + sovPb1(uint64(l))
	}
	if len(m.AllianceMembers) > 0 {
		for _, e := range m.AllianceMembers {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if len(m.AllianceShop) > 0 {
		for _, e := range m.AllianceShop {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if len(m.AllianceMemberInfo) > 0 {
		for _, e := range m.AllianceMemberInfo {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if len(m.AllianceTask) > 0 {
		for _, e := range m.AllianceTask {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if m.AllianceChest != nil {
		l = m.AllianceChest.Size()
		n += 1 + l + sovPb1(uint64(l))
	}
	return n
}

func (m *AllianceMemberInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovPb1(uint64(m.Uid))
	}
	if m.StageId != 0 {
		n += 1 + sovPb1(uint64(m.StageId))
	}
	if m.Power != 0 {
		n += 1 + sovPb1(uint64(m.Power))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	if m.AvatarConfigId != 0 {
		n += 1 + sovPb1(uint64(m.AvatarConfigId))
	}
	if m.StepId != 0 {
		n += 1 + sovPb1(uint64(m.StepId))
	}
	if m.FinishTime != 0 {
		n += 1 + sovPb1(uint64(m.FinishTime))
	}
	return n
}

func (m *GetAllianceMembersInfosRet) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Infos) > 0 {
		for _, e := range m.Infos {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	return n
}

func (m *GetAllianceListRet) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Alliance) > 0 {
		for _, e := range m.Alliance {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	return n
}

func (m *GetUserInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovPb1(uint64(m.Uid))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	l = len(m.AllianceName)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	l = len(m.AllianceAcronym)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	if m.MaxStage != 0 {
		n += 1 + sovPb1(uint64(m.MaxStage))
	}
	if m.Power != 0 {
		n += 1 + sovPb1(uint64(m.Power))
	}
	if m.FriendStageLimit != 0 {
		n += 1 + sovPb1(uint64(m.FriendStageLimit))
	}
	if m.AllowStrangers {
		n += 2
	}
	if m.AvatarConfigId != 0 {
		n += 1 + sovPb1(uint64(m.AvatarConfigId))
	}
	if len(m.Hero) > 0 {
		for _, e := range m.Hero {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if len(m.LordEquip) > 0 {
		for _, e := range m.LordEquip {
			l = e.Size()
			n += 1 + l + sovPb1(uint64(l))
		}
	}
	if len(m.LordGemPower) > 0 {
		for k, v := range m.LordGemPower {
			_ = k
			_ = v
			mapEntrySize := 1 + sovPb1(uint64(k)) + 1 + sovPb1(uint64(v))
			n += mapEntrySize + 1 + sovPb1(uint64(mapEntrySize))
		}
	}
	return n
}

func (m *ActivityInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StartTime != 0 {
		n += 1 + sovPb1(uint64(m.StartTime))
	}
	if m.EndTime != 0 {
		n += 1 + sovPb1(uint64(m.EndTime))
	}
	if m.Status != 0 {
		n += 1 + sovPb1(uint64(m.Status))
	}
	l = len(m.ActivityName)
	if l > 0 {
		n += 1 + l + sovPb1(uint64(l))
	}
	if m.ActivityId != 0 {
		n += 1 + sovPb1(uint64(m.ActivityId))
	}
	if m.PublicTime != 0 {
		n += 1 + sovPb1(uint64(m.PublicTime))
	}
	if m.ActivityType != 0 {
		n += 1 + sovPb1(uint64(m.ActivityType))
	}
	return n
}

func (m *InitSymbioticRet) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.ResetHeroId) > 0 {
		l = 0
		for _, e := range m.ResetHeroId {
			l += sovPb1(uint64(e))
		}
		n += 1 + sovPb1(uint64(l)) + l
	}
	if len(m.ResetItemMap) > 0 {
		for k, v := range m.ResetItemMap {
			_ = k
			_ = v
			mapEntrySize := 1 + sovPb1(uint64(k)) + 1 + sovPb1(uint64(v))
			n += mapEntrySize + 1 + sovPb1(uint64(mapEntrySize))
		}
	}
	return n
}

func (m *OneKeyHeroLevelUpgradeRet) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.HeroId != 0 {
		n += 1 + sovPb1(uint64(m.HeroId))
	}
	if m.Amount != 0 {
		n += 1 + sovPb1(uint64(m.Amount))
	}
	return n
}

func (m *OneKeyEquipGemReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.GemId != 0 {
		n += 1 + sovPb1(uint64(m.GemId))
	}
	if m.Pos != 0 {
		n += 1 + sovPb1(uint64(m.Pos))
	}
	return n
}

func sovPb1(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozPb1(x uint64) (n int) {
	return sovPb1(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *RequestParams) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RequestParams: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RequestParams: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UserData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UserData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ui1", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Ui1 == nil {
				m.Ui1 = &minirpc.UserInfo{}
			}
			if err := m.Ui1.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Building", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Building = append(m.Building, &minirpc.BuildingInfo{})
			if err := m.Building[len(m.Building)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Hero", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Hero = append(m.Hero, &minirpc.HeroInfo{})
			if err := m.Hero[len(m.Hero)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HeroLottery", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.HeroLottery == nil {
				m.HeroLottery = &minirpc.HeroLottery{}
			}
			if err := m.HeroLottery.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Consumables", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Consumables = append(m.Consumables, &minirpc.Consumable{})
			if err := m.Consumables[len(m.Consumables)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserBenefits", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserBenefits = append(m.UserBenefits, &minirpc.UserBenefit{})
			if err := m.UserBenefits[len(m.UserBenefits)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserJob", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserJob = append(m.UserJob, &minirpc.UserJob{})
			if err := m.UserJob[len(m.UserJob)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserTroop", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserTroop = append(m.UserTroop, &minirpc.UserTroop{})
			if err := m.UserTroop[len(m.UserTroop)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Research", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Research = append(m.Research, &minirpc.Research{})
			if err := m.Research[len(m.Research)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MainTask", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MainTask = append(m.MainTask, &minirpc.MainTask{})
			if err := m.MainTask[len(m.MainTask)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MapEvent", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.MapEvent == nil {
				m.MapEvent = &minirpc.MapEvent{}
			}
			if err := m.MapEvent.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HeroSkill", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HeroSkill = append(m.HeroSkill, &minirpc.HeroSkill{})
			if err := m.HeroSkill[len(m.HeroSkill)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dave", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Dave == nil {
				m.Dave = &minirpc.Dave{}
			}
			if err := m.Dave.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserData", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserData = append(m.UserData, &minirpc.UserData{})
			if err := m.UserData[len(m.UserData)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MainLineStage", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.MainLineStage == nil {
				m.MainLineStage = &minirpc.MainLineStage{}
			}
			if err := m.MainLineStage.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 16:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CurServerTime", wireType)
			}
			m.CurServerTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurServerTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 17:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsNewPlayer", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNewPlayer = bool(v != 0)
		case 18:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RtmToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RtmToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 19:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserIapBuyLog", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.UserIapBuyLog == nil {
				m.UserIapBuyLog = &minirpc.UserIapBuyTimes{}
			}
			if err := m.UserIapBuyLog.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LordEquip", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LordEquip = append(m.LordEquip, &minirpc.LordEquip{})
			if err := m.LordEquip[len(m.LordEquip)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 21:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LordGem", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LordGem = append(m.LordGem, &minirpc.LordGem{})
			if err := m.LordGem[len(m.LordGem)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 22:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LordGemRandom", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LordGemRandom = append(m.LordGemRandom, &minirpc.LordGemRandom{})
			if err := m.LordGemRandom[len(m.LordGemRandom)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 23:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dungeon", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Dungeon = append(m.Dungeon, &minirpc.Dungeon{})
			if err := m.Dungeon[len(m.Dungeon)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 24:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FunctionOpen", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.FunctionOpen == nil {
				m.FunctionOpen = &minirpc.FunctionOpen{}
			}
			if err := m.FunctionOpen.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 25:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NewbieGuide", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.NewbieGuide == nil {
				m.NewbieGuide = &minirpc.NewbieGuide{}
			}
			if err := m.NewbieGuide.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 26:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActivityTask", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ActivityTask = append(m.ActivityTask, &minirpc.ActivityTask{})
			if err := m.ActivityTask[len(m.ActivityTask)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 27:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FirstCharge", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FirstCharge = append(m.FirstCharge, &minirpc.FirstCharge{})
			if err := m.FirstCharge[len(m.FirstCharge)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 28:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GrowthFund", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GrowthFund = append(m.GrowthFund, &minirpc.GrowthFund{})
			if err := m.GrowthFund[len(m.GrowthFund)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 29:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MonthCard", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MonthCard = append(m.MonthCard, &minirpc.MonthCard{})
			if err := m.MonthCard[len(m.MonthCard)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 30:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegularPack", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RegularPack = append(m.RegularPack, &minirpc.RegularPack{})
			if err := m.RegularPack[len(m.RegularPack)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 31:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AchievementTask", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AchievementTask = append(m.AchievementTask, &minirpc.AchievementTask{})
			if err := m.AchievementTask[len(m.AchievementTask)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ManiFest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ManiFest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ManiFest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LangUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LangUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BundleVersionR", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BundleVersionR = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BundleVersionG", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BundleVersionG = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cdn", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cdn = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Rewards) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Rewards: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Rewards: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ItemValue", wireType)
			}
			m.ItemValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemValue |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *KillMonsterRet) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: KillMonsterRet: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: KillMonsterRet: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rewards", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rewards = append(m.Rewards, &Rewards{})
			if err := m.Rewards[len(m.Rewards)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SelectRougeIds = append(m.SelectRougeIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthPb1
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthPb1
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.SelectRougeIds) == 0 {
					m.SelectRougeIds = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SelectRougeIds = append(m.SelectRougeIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field SelectRougeIds", wireType)
			}
		case 3:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UnSelectRougeIds = append(m.UnSelectRougeIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthPb1
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthPb1
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.UnSelectRougeIds) == 0 {
					m.UnSelectRougeIds = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UnSelectRougeIds = append(m.UnSelectRougeIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field UnSelectRougeIds", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field KillAmount", wireType)
			}
			m.KillAmount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KillAmount |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRewardReturn) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRewardReturn: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRewardReturn: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rewards", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Rewards == nil {
				m.Rewards = make(map[int32]int64)
			}
			var mapkey int32
			var mapvalue int64
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapkey |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
				} else if fieldNum == 2 {
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapvalue |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipPb1(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthPb1
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Rewards[mapkey] = mapvalue
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsFull", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsFull = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendMsgParams) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SendMsgParams: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SendMsgParams: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FromUid", wireType)
			}
			m.FromUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromUid |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Uids = append(m.Uids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthPb1
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthPb1
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Uids) == 0 {
					m.Uids = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Uids = append(m.Uids, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Uids", wireType)
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MsgType", wireType)
			}
			m.MsgType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgType |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendPBMsgParams) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SendPBMsgParams: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SendPBMsgParams: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FromUid", wireType)
			}
			m.FromUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromUid |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Uids = append(m.Uids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthPb1
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthPb1
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Uids) == 0 {
					m.Uids = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Uids = append(m.Uids, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Uids", wireType)
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MsgType", wireType)
			}
			m.MsgType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgType |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Msg = append(m.Msg[:0], dAtA[iNdEx:postIndex]...)
			if m.Msg == nil {
				m.Msg = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MultiSendMsgParams) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MultiSendMsgParams: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MultiSendMsgParams: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Params", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Params = append(m.Params, &SendMsgParams{})
			if err := m.Params[len(m.Params)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MultiSendPBMsgParams) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MultiSendPBMsgParams: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MultiSendPBMsgParams: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Params", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Params = append(m.Params, &SendPBMsgParams{})
			if err := m.Params[len(m.Params)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendTopicMsgParams) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SendTopicMsgParams: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SendTopicMsgParams: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Topic", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Topic = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MsgType", wireType)
			}
			m.MsgType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgType |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendPBTopicMsgParams) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SendPBTopicMsgParams: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SendPBTopicMsgParams: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Topic", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Topic = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MsgType", wireType)
			}
			m.MsgType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgType |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FinishEventReturn) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: finishEventReturn: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: finishEventReturn: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Success", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Success = bool(v != 0)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rewards", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Rewards == nil {
				m.Rewards = make(map[int32]int64)
			}
			var mapkey int32
			var mapvalue int64
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapkey |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
				} else if fieldNum == 2 {
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapvalue |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipPb1(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthPb1
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Rewards[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LevelStruct) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LevelStruct: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LevelStruct: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.CardIds = append(m.CardIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthPb1
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthPb1
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.CardIds) == 0 {
					m.CardIds = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.CardIds = append(m.CardIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field CardIds", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalExp", wireType)
			}
			m.TotalExp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalExp |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.HeroHp = append(m.HeroHp, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthPb1
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthPb1
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.HeroHp) == 0 {
					m.HeroHp = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.HeroHp = append(m.HeroHp, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field HeroHp", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GiftStruct) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GiftStruct: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GiftStruct: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LevelRankInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LevelRankInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LevelRankInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Icon", wireType)
			}
			m.Icon = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Icon |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FinishTime", wireType)
			}
			m.FinishTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinishTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PowerRankInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PowerRankInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PowerRankInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Power", wireType)
			}
			m.Power = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Power |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Icon", wireType)
			}
			m.Icon = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Icon |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LordGemCraft) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LordGemCraft: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LordGemCraft: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GemCraftId", wireType)
			}
			m.GemCraftId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GemCraftId |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GemIds", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.GemIds == nil {
				m.GemIds = make(map[int32]int32)
			}
			var mapkey int32
			var mapvalue int32
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapkey |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
				} else if fieldNum == 2 {
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapvalue |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipPb1(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthPb1
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.GemIds[mapkey] = mapvalue
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DailyResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DailyResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DailyResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DailyTask", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DailyTask = append(m.DailyTask, &minirpc.DailyTask{})
			if err := m.DailyTask[len(m.DailyTask)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DailyChest", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.DailyChest == nil {
				m.DailyChest = &minirpc.UserData{}
			}
			if err := m.DailyChest.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WeeklyChest", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.WeeklyChest == nil {
				m.WeeklyChest = &minirpc.UserData{}
			}
			if err := m.WeeklyChest.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMailListResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetMailListResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetMailListResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MailList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MailList = append(m.MailList, &minirpc.UserMail{})
			if err := m.MailList[len(m.MailList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AllianceInfoRet) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AllianceInfoRet: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AllianceInfoRet: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Alliance", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Alliance == nil {
				m.Alliance = &minirpc.Alliance{}
			}
			if err := m.Alliance.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllianceMembers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AllianceMembers = append(m.AllianceMembers, &minirpc.AllianceMember{})
			if err := m.AllianceMembers[len(m.AllianceMembers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllianceShop", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AllianceShop = append(m.AllianceShop, &minirpc.AllianceShopBuy{})
			if err := m.AllianceShop[len(m.AllianceShop)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllianceMemberInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AllianceMemberInfo = append(m.AllianceMemberInfo, &AllianceMemberInfo{})
			if err := m.AllianceMemberInfo[len(m.AllianceMemberInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllianceTask", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AllianceTask = append(m.AllianceTask, &minirpc.AllianceTask{})
			if err := m.AllianceTask[len(m.AllianceTask)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllianceChest", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AllianceChest == nil {
				m.AllianceChest = &minirpc.UserData{}
			}
			if err := m.AllianceChest.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AllianceMemberInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AllianceMemberInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AllianceMemberInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StageId", wireType)
			}
			m.StageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StageId |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Power", wireType)
			}
			m.Power = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Power |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AvatarConfigId", wireType)
			}
			m.AvatarConfigId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AvatarConfigId |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StepId", wireType)
			}
			m.StepId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StepId |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FinishTime", wireType)
			}
			m.FinishTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinishTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllianceMembersInfosRet) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetAllianceMembersInfosRet: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetAllianceMembersInfosRet: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Infos", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Infos = append(m.Infos, &AllianceMemberInfo{})
			if err := m.Infos[len(m.Infos)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllianceListRet) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetAllianceListRet: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetAllianceListRet: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Alliance", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Alliance = append(m.Alliance, &minirpc.Alliance{})
			if err := m.Alliance[len(m.Alliance)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetUserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetUserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllianceName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AllianceName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllianceAcronym", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AllianceAcronym = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxStage", wireType)
			}
			m.MaxStage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxStage |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Power", wireType)
			}
			m.Power = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Power |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FriendStageLimit", wireType)
			}
			m.FriendStageLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FriendStageLimit |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllowStrangers", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.AllowStrangers = bool(v != 0)
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AvatarConfigId", wireType)
			}
			m.AvatarConfigId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AvatarConfigId |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Hero", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Hero = append(m.Hero, &minirpc.HeroInfo{})
			if err := m.Hero[len(m.Hero)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LordEquip", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LordEquip = append(m.LordEquip, &minirpc.LordEquip{})
			if err := m.LordEquip[len(m.LordEquip)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LordGemPower", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.LordGemPower == nil {
				m.LordGemPower = make(map[int32]int32)
			}
			var mapkey int32
			var mapvalue int32
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapkey |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
				} else if fieldNum == 2 {
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapvalue |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipPb1(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthPb1
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.LordGemPower[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActivityInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ActivityInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ActivityInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActivityName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ActivityName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PublicTime", wireType)
			}
			m.PublicTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PublicTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActivityType", wireType)
			}
			m.ActivityType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *InitSymbioticRet) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: InitSymbioticRet: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: InitSymbioticRet: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ResetHeroId = append(m.ResetHeroId, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthPb1
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthPb1
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.ResetHeroId) == 0 {
					m.ResetHeroId = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ResetHeroId = append(m.ResetHeroId, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field ResetHeroId", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResetItemMap", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPb1
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPb1
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ResetItemMap == nil {
				m.ResetItemMap = make(map[int32]int32)
			}
			var mapkey int32
			var mapvalue int32
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPb1
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapkey |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
				} else if fieldNum == 2 {
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPb1
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapvalue |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipPb1(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthPb1
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.ResetItemMap[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OneKeyHeroLevelUpgradeRet) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OneKeyHeroLevelUpgradeRet: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OneKeyHeroLevelUpgradeRet: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HeroId", wireType)
			}
			m.HeroId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HeroId |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OneKeyEquipGemReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OneKeyEquipGemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OneKeyEquipGemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GemId", wireType)
			}
			m.GemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GemId |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Pos", wireType)
			}
			m.Pos = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Pos |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPb1(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPb1
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipPb1(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowPb1
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowPb1
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthPb1
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupPb1
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthPb1
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthPb1        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowPb1          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupPb1 = fmt.Errorf("proto: unexpected end of group")
)
