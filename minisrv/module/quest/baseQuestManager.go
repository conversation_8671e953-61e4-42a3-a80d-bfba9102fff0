package quest

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/logger"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"net/url"
	"strconv"
	"strings"
	"time"
)

type BaseQuestLine struct {
	Formula string
	Value   int32
}

var QuestCheckFuncMap = make(map[int32]func(ctx context.Context, uid int64) *wrpc.ActivityInfo)

func checkFormula(conditons string, trace map[string]string) bool {
	if conditons == "" {
		return true
	}
	if trace == nil {
		return false
	}
	conditionArray := strings.Split(conditons, "&")
	flag := true
	for _, condition := range conditionArray {
		switch true {
		case strings.Contains(condition, "<="):
			parts := strings.Split(condition, "<=")
			if len(parts) < 2 {
				return false
			}
			// Convert both values to int64 for numeric comparison
			val1, err1 := strconv.ParseInt(trace[parts[0]], 10, 64)
			val2, err2 := strconv.ParseInt(parts[1], 10, 64)
			if err1 == nil && err2 == nil {
				flag = flag && val1 <= val2
			} else {
				flag = flag && trace[parts[0]] <= parts[1] // Fallback to string comparison
			}
		case strings.Contains(condition, ">="):
			parts := strings.Split(condition, ">=")
			if len(parts) < 2 {
				return false
			}
			// Convert both values to int64 for numeric comparison
			val1, err1 := strconv.ParseInt(trace[parts[0]], 10, 64)
			val2, err2 := strconv.ParseInt(parts[1], 10, 64)
			if err1 == nil && err2 == nil {
				flag = flag && val1 >= val2
			} else {
				flag = flag && trace[parts[0]] >= parts[1] // Fallback to string comparison
			}
		case strings.Contains(condition, "<"):
			parts := strings.Split(condition, "<")
			if len(parts) < 2 {
				return false
			}
			// Convert both values to int64 for numeric comparison
			val1, err1 := strconv.ParseInt(trace[parts[0]], 10, 64)
			val2, err2 := strconv.ParseInt(parts[1], 10, 64)
			if err1 == nil && err2 == nil {
				flag = flag && val1 < val2
			} else {
				flag = flag && trace[parts[0]] < parts[1] // Fallback to string comparison
			}
		case strings.Contains(condition, ">"):
			parts := strings.Split(condition, ">")
			if len(parts) < 2 {
				return false
			}
			// Convert both values to int64 for numeric comparison
			val1, err1 := strconv.ParseInt(trace[parts[0]], 10, 64)
			val2, err2 := strconv.ParseInt(parts[1], 10, 64)
			if err1 == nil && err2 == nil {
				flag = flag && val1 > val2
			} else {
				flag = flag && trace[parts[0]] > parts[1] // Fallback to string comparison
			}
		case strings.Contains(condition, "="):
			parts := strings.Split(condition, "=")
			if len(parts) < 2 {
				return false
			}
			// Convert both values to int64 for numeric comparison
			val1, err1 := strconv.ParseInt(trace[parts[0]], 10, 64)
			val2, err2 := strconv.ParseInt(parts[1], 10, 64)
			if err1 == nil && err2 == nil {
				flag = flag && val1 == val2
			} else {
				flag = flag && parts[1] == trace[parts[0]] // Fallback to string comparison
			}
		}

	}
	return flag
}

func Notify(ctx context.Context, uid int64, questType servercfg.TaskType, trace map[string]string, value int64) {
	//filter := bson.M{
	//	"uid":    uid,
	//	"type":   int32(questType),
	//	"status": int32(minirpc.QuestActivity),
	//}

	//filter := bson.D{{"uid", bson.M{"$eq": uid}}, {"type", bson.M{"$eq": int32(questType)}}, {"status", bson.M{"$eq": int32(minirpc.QuestActivity)}}}
	mainModels, _ := orm.GetAll[*model.MainTask](ctx, uid)
	for _, v := range mainModels {
		if checkQuestType(v, questType) {
			update(ctx, v, trace, value)
		}
	}
	//growthModels, _ := orm.Find[*model.GrowthTask](ctx, filter)
	//for _, v := range growthModels {
	//	update(ctx, v, trace, value)
	//}
	dailyModels, _ := orm.GetAll[*model.DailyTask](ctx, uid)
	for _, v := range dailyModels {
		if checkQuestType(v, questType) {
			update(ctx, v, trace, value)
		}
	}
	//WeeklyModels, _ := orm.Find[*model.WeeklyTask](ctx, filter)
	//for _, v := range WeeklyModels {
	//	update(ctx, v, trace, value)
	//}
	//repeatModels, _ := orm.Find[*model.RepeatTask](ctx, filter)
	//for _, v := range repeatModels {
	//	update(ctx, v, trace, value)
	//}
	allianceModels, _ := orm.GetAll[*model.AllianceTask](ctx, uid)
	if allianceModels != nil {
		for _, v := range allianceModels {
			if checkQuestType(v, questType) {
				update(ctx, v, trace, value)
			}
		}
	}
	achievementModels, _ := orm.GetAll[*model.AchievementTaskModel](ctx, uid)
	if achievementModels != nil {
		for _, v := range achievementModels {
			if checkQuestType(v, questType) {
				update(ctx, v, trace, value)
			}
		}
	}
	activityModels, _ := orm.GetAll[*model.ActivityTask](ctx, uid)
	if activityModels != nil {
		for _, v := range activityModels {
			if QuestCheckFuncMap[v.ActivityType()] == nil {
				continue
			}
			activityInfo := QuestCheckFuncMap[v.ActivityType()](ctx, uid)
			if activityInfo == nil {
				continue
			}
			//如果过了公示期，则跳过
			if activityInfo.PublicTime < time.Now().Unix() {
				continue
			}
			if checkQuestType(v, questType) {
				update(ctx, v, trace, value)
			}
		}
	}
}

func checkQuestType(quest model.TaskInterface, questType servercfg.TaskType) bool {
	if quest.Type() != int32(questType) {
		return false
	}
	if quest.Status() != int32(minirpc.QuestActivity) {
		return false
	}
	return true
}

func update(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	questType := quest.Type()
	switch questType {

	case int32(servercfg.TaskType_Login),
		int32(servercfg.TaskType_LevelBegin),
		int32(servercfg.TaskType_LevelPass),
		int32(servercfg.TaskType_HeroLevelUp),
		int32(servercfg.TaskType_HeroGeneUp),
		int32(servercfg.TaskType_KillMonster),
		int32(servercfg.TaskType_claim_idle_reward),
		int32(servercfg.TaskType_claim_pass_level_reward),
		int32(servercfg.TaskType_Sweep),
		int32(servercfg.TaskType_ItemBurn),
		int32(servercfg.TaskType_TotalItemBurn),
		int32(servercfg.TaskType_Chat),
		int32(servercfg.TaskType_LordEquipLvlUp),
		int32(servercfg.TaskType_GemCraft),
		int32(servercfg.TaskType_GemSummon),
		int32(servercfg.TaskType_Shopping),
		int32(servercfg.TaskType_DungeonChallenge),
		int32(servercfg.TaskType_DungeonSweep),
		int32(servercfg.TaskType_Rename),
		int32(servercfg.TaskType_Avatar),
		int32(servercfg.TaskType_Nigger),
		int32(servercfg.TaskType_HeroStarUp),
		int32(servercfg.TaskType_HeroSkillUp),
		int32(servercfg.TaskType_HeroSummon),
		int32(servercfg.TaskType_DailyScore),
		int32(servercfg.TaskType_WeeklyScore),
		int32(servercfg.TaskType_LordEquipGrade),
		int32(servercfg.TaskType_EnergyFactory),
		int32(servercfg.TaskType_CompleteTask):
		defaultCheckQuest(ctx, quest, trace, value)
	case int32(servercfg.TaskType_LevelPassTo):
		checkLevelPassTo(ctx, quest, trace, value)
	case int32(servercfg.TaskType_HeroLevelUpTo):
		checkHeroLevelUpTo(ctx, quest, trace, value)
	case int32(servercfg.TaskType_HeroConfig):
		checkHeroConfig(ctx, quest, trace, value)
	case int32(servercfg.TaskType_LordEquipLvlUpTo):
		checkLordEquipLvlUpTo(ctx, quest, trace, value)
	case int32(servercfg.TaskType_HeroGeneUpTo):
		checkHeroGeneUpTo(ctx, quest, trace, value)
	case int32(servercfg.TaskType_HeroStarUpTo):
		checkHeroStarUpTo(ctx, quest, trace, value)
	case int32(servercfg.TaskType_HeroSkillUpTo):
		checkHeroSkillUpTo(ctx, quest, trace, value)
	case int32(servercfg.TaskType_TotalLogin):
		checkTotalLogin(ctx, quest, trace, value)
	case int32(servercfg.TaskType_TotalActivateHero):
		checkHeroQuality(ctx, quest, trace, value)
	case int32(servercfg.TaskType_TotalMainStar):
		checkTotalMainStar(ctx, quest, trace, value)
	case int32(servercfg.TaskType_DungeonLevel):
		checkDungeonLevel(ctx, quest, trace, value)
	case int32(servercfg.TaskType_GemQuality):
		checkGemQuality(ctx, quest, trace, value)
	case int32(servercfg.TaskType_TotalHeroStarUp):
		checkTotalHeroStarUp(ctx, quest, trace, value)
	case int32(servercfg.TaskType_TotalHeroSkillUp):
		checkTotalHeroSkillUp(ctx, quest, trace, value)
	case int32(servercfg.TaskType_TotalHeroGeneUp):
		checkTotalHeroGeneUp(ctx, quest, trace, value)
	case int32(servercfg.TaskType_TotalLordEquipLvlUp):
		checkTotalLordEquipLvlUp(ctx, quest, trace, value)
	case int32(servercfg.TaskType_TotalLordEquipGrade):
		checkTotalLordEquipGrade(ctx, quest, trace, value)
	case int32(servercfg.TaskType_ChapterTaskComplete):
		checkChapterTaskComplete(ctx, quest, trace, value)
	case int32(servercfg.TaskType_TotalHeroSummon),
		int32(servercfg.TaskType_TotalGemSummon),
		int32(servercfg.TaskType_TotalGemSummon_1),
		int32(servercfg.TaskType_TotalGemSummon_2):
		checkTaskCounter(ctx, quest, trace, value, questType)
		//case int32(servercfg.TaskType_LordEquipGrade):
		//	checkLordEquipGrade(ctx, quest, trace, value)
	}
}

func checkChapterTaskComplete(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	line := getBaseQuestLine(quest)
	chapterQuest, _ := model.ExportAllMainTasks(ctx, quest.Uid())
	for _, v := range chapterQuest {
		questLine := cfg_mgr.Cfg.ChapterTaskTable.Get(v.QuestId)
		if questLine == nil {
			continue
		}
		maxChapter := questLine.Chapter - 1
		if maxChapter >= line.Value {
			setQuestComplete(ctx, quest)
			quest.SetProgress(ctx, int64(line.Value))
			return
		} else {
			quest.SetProgress(ctx, int64(maxChapter))
		}
	}

}

func checkTotalLordEquipGrade(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	equips := model.GetAllLordEquipModels(ctx, quest.Uid())
	count := int32(0)
	for _, equip := range equips {
		gradeLine := cfg_mgr.Cfg.LordEquipGradeTable.Get(equip.GradeId())
		count += gradeLine.Grade - 1
	}
	line := getBaseQuestLine(quest)
	if count >= int32(line.Value) {
		setQuestComplete(ctx, quest)
		quest.SetProgress(ctx, int64(line.Value))
	} else {
		quest.SetProgress(ctx, int64(count))
	}
}

func checkTotalLordEquipLvlUp(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	equips := model.GetAllLordEquipModels(ctx, quest.Uid())
	count := int32(0)
	for _, equip := range equips {
		equipLine := cfg_mgr.Cfg.LordEquipTable.Get(equip.ConfigId())
		count += equipLine.Level - 1
	}
	line := getBaseQuestLine(quest)
	if count >= int32(line.Value) {
		setQuestComplete(ctx, quest)
		quest.SetProgress(ctx, int64(line.Value))
	} else {
		quest.SetProgress(ctx, int64(count))
	}
}

func checkTaskCounter(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64, questType int32) {

	line := getBaseQuestLine(quest)
	num := model.GetTaskCounterValue(ctx, quest.Uid(), int32(questType), "")
	if num >= int32(line.Value) {
		setQuestComplete(ctx, quest)
		quest.SetProgress(ctx, int64(line.Value))
	} else {
		quest.SetProgress(ctx, int64(num))
	}
}

func checkTotalHeroGeneUp(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	heros, _ := model.GetAllHeros(ctx, quest.Uid())
	count := int32(0)
	for _, hero := range heros {
		geneLine := cfg_mgr.Cfg.HeroGeneTable.Get(hero.GeneConfigId())
		count += geneLine.HeroGeneLevel - 1
	}
	line := getBaseQuestLine(quest)
	if count >= int32(line.Value) {
		setQuestComplete(ctx, quest)
		quest.SetProgress(ctx, int64(line.Value))
	} else {
		quest.SetProgress(ctx, int64(count))
	}
}

func checkTotalHeroSkillUp(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	heroSkills, _ := model.ExportHeroSkills(ctx, quest.Uid())
	count := int32(0)
	for _, skill := range heroSkills {
		skillLine := cfg_mgr.Cfg.HeroSkillAwakeTable.Get(skill.ConfigId)
		count += skillLine.Level - 1
	}
	line := getBaseQuestLine(quest)
	if count >= int32(line.Value) {
		setQuestComplete(ctx, quest)
		quest.SetProgress(ctx, int64(line.Value))
	} else {
		quest.SetProgress(ctx, int64(count))
	}
}

func checkTotalHeroStarUp(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	line := getBaseQuestLine(quest)
	heros, _ := model.GetAllHeros(ctx, quest.Uid())
	count := int32(0)
	for _, hero := range heros {
		starLine := cfg_mgr.Cfg.HeroStarTable.Get(hero.StarId())
		count += starLine.HeroStarLevel
	}
	if count >= int32(line.Value) {
		setQuestComplete(ctx, quest)
		quest.SetProgress(ctx, int64(line.Value))
	} else {
		quest.SetProgress(ctx, int64(count))
	}
}

func checkLordEquipGrade(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	line := getBaseQuestLine(quest)
	formula := line.Formula
	formula = strings.ReplaceAll(formula, ">", "")
	formula = strings.ReplaceAll(formula, "<", "")
	values, err := url.ParseQuery(formula)
	if err != nil {
		return
	}
	needLevel := int32(0)
	needLevelStr := values.Get("level")
	if needLevelStr != "" {
		level, _ := strconv.ParseInt(needLevelStr, 10, 32)
		needLevel = int32(level)
	}
	equips := model.GetAllLordEquipModels(ctx, quest.Uid())
	count := 0
	for _, equip := range equips {
		gradeLine := cfg_mgr.Cfg.LordEquipGradeTable.Get(equip.GradeId())
		if gradeLine.Grade >= needLevel {
			count++
		}
	}
	if count >= int(line.Value) {
		setQuestComplete(ctx, quest)
		quest.SetProgress(ctx, int64(line.Value))
	} else {
		quest.SetProgress(ctx, int64(count))
	}
}

func checkGemQuality(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	line := getBaseQuestLine(quest)
	formula := line.Formula
	formula = strings.ReplaceAll(formula, ">", "")
	formula = strings.ReplaceAll(formula, "<", "")
	values, err := url.ParseQuery(formula)
	if err != nil {
		return
	}
	needQuality := int32(0)
	needQualityStr := values.Get("quality")
	if needQualityStr != "" {
		quality, _ := strconv.ParseInt(needQualityStr, 10, 32)
		needQuality = int32(quality)
	}
	gems, _ := model.ExportLordGems(ctx, quest.Uid())
	count := 0
	for _, gem := range gems {
		gemLine := cfg_mgr.Cfg.LordGemTable.Get(gem.ConfigId)
		if gemLine.GemQualityType >= needQuality {
			count++
		}
	}
	if count >= int(line.Value) {
		setQuestComplete(ctx, quest)
		quest.SetProgress(ctx, int64(line.Value))
	} else {
		quest.SetProgress(ctx, int64(count))
	}
}

func checkDungeonLevel(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	line := getBaseQuestLine(quest)
	formula := line.Formula
	formula = strings.ReplaceAll(formula, ">", "")
	formula = strings.ReplaceAll(formula, "<", "")
	values, err := url.ParseQuery(formula)
	if err != nil {
		return
	}
	needLevel := int32(0)
	needLevelStr := values.Get("level")
	if needLevelStr != "" {
		level, _ := strconv.ParseInt(needLevelStr, 10, 32)
		needLevel = int32(level)
	}
	dungeonModels, _ := model.GetAllDungeonModels(ctx, quest.Uid())
	count := 0
	for _, dungeon := range dungeonModels {
		if dungeon.MaxLevel() >= needLevel {
			count++
		}
	}
	if count >= int(line.Value) {
		setQuestComplete(ctx, quest)
		quest.SetProgress(ctx, int64(line.Value))
	} else {
		quest.SetProgress(ctx, int64(count))
	}
}

func checkTotalMainStar(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	mainStage, _ := model.GetMainLineStage(ctx, quest.Uid())
	stageFinishStatus := mainStage.StageFinishStatus()
	if stageFinishStatus == nil {
		return
	}
	totalStar := int32(0)
	for _, v := range stageFinishStatus {
		totalStar += v.PerfectStatus
	}
	line := getBaseQuestLine(quest)
	if totalStar >= int32(line.Value) {
		quest.SetProgress(ctx, int64(line.Value))
		setQuestComplete(ctx, quest)
	} else {
		quest.SetProgress(ctx, int64(totalStar))
	}
}

func checkTotalLogin(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	user, _ := model.GetUserModel(ctx, quest.Uid())
	value = int64(user.TotalLoginDays())
	line := getBaseQuestLine(quest)
	if line == nil {
		return
	}
	if value >= int64(line.Value) {
		setQuestComplete(ctx, quest)
		value = int64(line.Value)
	}
	quest.SetProgress(ctx, value)
}

func checkHeroSkillUpTo(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	line := getBaseQuestLine(quest)
	formula := line.Formula
	formula = strings.ReplaceAll(formula, ">", "")
	formula = strings.ReplaceAll(formula, "<", "")
	values, err := url.ParseQuery(formula)
	if err != nil {
		return
	}
	needLevel := int32(0)
	needLevelStr := values.Get("level")
	if needLevelStr != "" {
		level, _ := strconv.ParseInt(needLevelStr, 10, 32)
		needLevel = int32(level)
	}
	heros, _ := model.GetAllHeros(ctx, quest.Uid())
	count := 0
	for _, hero := range heros {
		skills := model.GetHeroSkills(ctx, quest.Uid(), hero.ConfigId())
		for _, skill := range skills {
			skillLine := cfg_mgr.Cfg.HeroSkillAwakeTable.Get(skill.ConfigId())
			if skillLine.Level >= needLevel {
				count++
			}
		}
	}
	trace["level"] = strconv.Itoa(int(needLevel))
	trace["herocnt"] = strconv.Itoa(int(count))

	if !checkFormula(line.Formula, trace) {
		return
	}
	setQuestComplete(ctx, quest)
}

func checkHeroStarUpTo(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	line := getBaseQuestLine(quest)
	formula := line.Formula
	formula = strings.ReplaceAll(formula, ">", "")
	formula = strings.ReplaceAll(formula, "<", "")
	values, err := url.ParseQuery(formula)
	if err != nil {
		return
	}
	needLevel := int32(0)
	needLevelStr := values.Get("level")
	if needLevelStr != "" {
		level, _ := strconv.ParseInt(needLevelStr, 10, 32)
		needLevel = int32(level)
	}
	heros, _ := model.GetAllHeros(ctx, quest.Uid())
	count := 0
	for _, hero := range heros {
		starLine := cfg_mgr.Cfg.HeroStarTable.Get(hero.StarId())
		if starLine.HeroStarLevel >= needLevel {
			count++
		}
	}
	trace["level"] = strconv.Itoa(int(needLevel))
	trace["herocnt"] = strconv.Itoa(int(count))

	if !checkFormula(line.Formula, trace) {
		return
	}
	setQuestComplete(ctx, quest)
}

func checkHeroGeneUpTo(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	line := getBaseQuestLine(quest)
	formula := line.Formula
	formula = strings.ReplaceAll(formula, ">", "")
	formula = strings.ReplaceAll(formula, "<", "")
	values, err := url.ParseQuery(formula)
	if err != nil {
		return
	}
	needLevel := int32(0)
	needLevelStr := values.Get("level")
	if needLevelStr != "" {
		level, _ := strconv.ParseInt(needLevelStr, 10, 32)
		needLevel = int32(level)
	}
	heros, _ := model.GetAllHeros(ctx, quest.Uid())
	count := 0
	for _, hero := range heros {
		geneLine := cfg_mgr.Cfg.HeroGeneTable.Get(hero.GeneConfigId())
		if geneLine.HeroGeneLevel >= needLevel {
			count++
		}
	}
	trace["level"] = strconv.Itoa(int(needLevel))
	trace["herocnt"] = strconv.Itoa(int(count))

	if !checkFormula(line.Formula, trace) {
		return
	}
	setQuestComplete(ctx, quest)
}

func checkHeroQuality(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	line := getBaseQuestLine(quest)
	formula := line.Formula
	formula = strings.ReplaceAll(formula, ">", "")
	formula = strings.ReplaceAll(formula, "<", "")
	values, err := url.ParseQuery(formula)
	if err != nil {
		return
	}
	needQuality := int32(0)
	needQualityStr := values.Get("quality")
	if needQualityStr != "" {
		quality, _ := strconv.ParseInt(needQualityStr, 10, 32)
		needQuality = int32(quality)
	}
	heros, _ := model.GetAllHeros(ctx, quest.Uid())
	count := 0
	for _, hero := range heros {
		heroLine := cfg_mgr.Cfg.HeroTable.Get(hero.ConfigId())
		if int32(heroLine.HeroQuality) == needQuality {
			count++
		}
	}
	trace["quality"] = strconv.Itoa(int(needQuality))
	trace["herocnt"] = strconv.Itoa(int(count))

	if !checkFormula(line.Formula, trace) {
		return
	}
	setQuestComplete(ctx, quest)
}

func getBaseQuestLine(quest model.TaskInterface) *BaseQuestLine {
	switch quest.GetTaskType() {
	case minirpc.TaskTypeMain: //主线任务
		line := cfg_mgr.Cfg.ChapterTaskTable.Get(quest.QuestId())
		return &BaseQuestLine{Formula: line.Formula, Value: line.Value}
	case minirpc.TaskTypeDaily: //每日任务
		line := cfg_mgr.Cfg.DailyTasksTable.Get(quest.QuestId())
		return &BaseQuestLine{Formula: line.Formula, Value: line.Value}
	case minirpc.TaskTypeAlliance: //联盟任务
		line := cfg_mgr.Cfg.GuildTaskTable.Get(quest.QuestId())
		return &BaseQuestLine{Formula: line.Formula, Value: line.Value}
	case minirpc.TaskTypeActivity: //活动任务
		if quest.GetActivityType() == int32(minirpc.ActivityTypeDay7Quest) {
			line := cfg_mgr.Cfg.SevenDayTasksTable.Get(quest.QuestId())
			return &BaseQuestLine{Formula: line.Formula, Value: line.Value}
		}
		return nil
	case minirpc.TaskTypeAchievement: //成就任务
		line := cfg_mgr.Cfg.AchievementTable.Get(quest.QuestId())
		if line == nil {
			return nil
		}
		return &BaseQuestLine{Formula: line.Formula, Value: line.Value}
	}
	return nil
}

// setQuestComplete 设置任务完成状态并记录日志
func setQuestComplete(ctx context.Context, quest model.TaskInterface) {
	quest.SetStatus(ctx, int32(minirpc.QuestComplete))

	// 获取任务组类型
	var groupType int32
	switch quest.GetTaskType() {
	case minirpc.TaskTypeDaily:
		groupType = 1001 // 每日任务
	case minirpc.TaskTypeAlliance:
		groupType = 1002 // 公会任务
	case minirpc.TaskTypeActivity:
		groupType = 1003 // 活动任务
	case minirpc.TaskTypeAchievement:
		groupType = 1004 // 成就任务
	default:
		return // 其他类型任务暂不记录日志
	}

	// 获取任务组ID
	var groupId int32
	switch quest.GetTaskType() {
	case minirpc.TaskTypeDaily:
		line := cfg_mgr.Cfg.DailyTasksTable.Get(quest.QuestId())
		if line != nil {
			groupId = line.Id // 或其他合适的字段作为组ID
		}
	case minirpc.TaskTypeAlliance:
		line := cfg_mgr.Cfg.GuildTaskTable.Get(quest.QuestId())
		if line != nil {
			groupId = line.Id // 或其他合适的字段作为组ID
		}
	}

	// 记录任务完成日志
	logger.LogCustom(ctx, quest.Uid(), "common_task_group", logger.NewDetail().
		Put("action", "task_finished").
		Put("group_type", groupType).
		Put("group_id", groupId).
		Put("task_id", quest.QuestId()))
}

// 通关指定关卡任务
func checkLevelPassTo(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	stage, _ := model.GetMainLineStage(ctx, quest.Uid())
	trace["level"] = strconv.Itoa(int(stage.UnlockStageId() - 1))
	line := getBaseQuestLine(quest)
	if !checkFormula(line.Formula, trace) {
		return
	}
	quest.SetProgress(ctx, int64(line.Value))
	setQuestComplete(ctx, quest)
}

// 英雄升至等级任务
func checkHeroLevelUpTo(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {

	line := getBaseQuestLine(quest)
	formula := line.Formula
	formula = strings.ReplaceAll(formula, ">", "")
	formula = strings.ReplaceAll(formula, "<", "")
	values, err := url.ParseQuery(formula)
	if err != nil {
		return
	}
	needLevel := int32(0)
	needLevelStr := values.Get("level")
	if needLevelStr != "" {
		level, _ := strconv.ParseInt(needLevelStr, 10, 32)
		needLevel = int32(level)
	}
	heros, _ := model.GetAllHeros(ctx, quest.Uid())
	count := 0
	for _, hero := range heros {
		levelLine := cfg_mgr.Cfg.HeroLevelTable.Get(hero.LevelId())
		if levelLine.HeroLevel >= needLevel {
			count++
		}
	}
	trace["level"] = strconv.Itoa(int(needLevel))
	trace["herocnt"] = strconv.Itoa(int(count))

	if !checkFormula(line.Formula, trace) {
		return
	}
	setQuestComplete(ctx, quest)
}

// 升级装备至指定等级
func checkLordEquipLvlUpTo(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {
	line := getBaseQuestLine(quest)
	formula := line.Formula
	formula = strings.ReplaceAll(formula, ">", "")
	formula = strings.ReplaceAll(formula, "<", "")
	values, err := url.ParseQuery(formula)
	if err != nil {
		return
	}
	needLevel := int32(0)
	needLevelStr := values.Get("level")
	equipType := values.Get("type")
	if needLevelStr != "" {
		level, _ := strconv.ParseInt(needLevelStr, 10, 32)
		needLevel = int32(level)
	}
	equips := model.GetAllLordEquipModels(ctx, quest.Uid())
	count := 0
	for _, equip := range equips {
		equipLine := cfg_mgr.Cfg.LordEquipTable.Get(equip.ConfigId())
		if equipLine == nil {
			continue
		}
		if equipLine != nil && strconv.Itoa(int(equipLine.Id)) != equipType {
			continue
		}
		if equipLine.Level >= needLevel {
			count++
		}
	}
	trace["level"] = strconv.Itoa(int(needLevel))
	trace["equipcnt"] = strconv.Itoa(int(count))

	if !checkFormula(line.Formula, trace) {
		return
	}
	setQuestComplete(ctx, quest)
}

// 上阵英雄
func checkHeroConfig(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {

	line := getBaseQuestLine(quest)
	values, err := url.ParseQuery(line.Formula)
	if err != nil {
		return
	}
	needQuality := int32(0)
	needQualityStr := values.Get("quality")
	if needQualityStr != "" {
		quality, _ := strconv.ParseInt(needQualityStr, 10, 32)
		needQuality = int32(quality)
	}
	user, _ := model.GetUserModel(ctx, quest.Uid())
	posInfo := user.BattlePosInfo()
	count := 0
	for _, v := range posInfo {
		heroLine := cfg_mgr.Cfg.HeroTable.Get(v.HeroId)
		if heroLine == nil {
			continue
		}
		if int32(heroLine.HeroQuality) >= needQuality {
			count++
		}
	}
	trace["quality"] = strconv.Itoa(int(needQuality))
	trace["herocnt"] = strconv.Itoa(int(count))

	if !checkFormula(line.Formula, trace) {
		return
	}
	setQuestComplete(ctx, quest)
}

func defaultCheckQuest(ctx context.Context, quest model.TaskInterface, trace map[string]string, value int64) {

	line := getBaseQuestLine(quest)
	if line == nil {
		return
	}
	if !checkFormula(line.Formula, trace) {
		return
	}
	endProgress := value + quest.Progress()
	if endProgress >= int64(line.Value) {
		if quest.GetTaskType() == minirpc.TaskTypeRepeat {
			quest.SetProgress(ctx, endProgress)
		} else {
			quest.SetProgress(ctx, int64(line.Value))
		}
		setQuestComplete(ctx, quest)
	} else {
		quest.SetProgress(ctx, endProgress)
	}

}
