// Code generated by easyj<PERSON> for marshaling/unmarshaling. DO NOT EDIT.

package minirpc

import (
	json "encoding/json"
	easyjson "github.com/mailru/easyjson"
	jlexer "github.com/mailru/easyjson/jlexer"
	jwriter "github.com/mailru/easyjson/jwriter"
	timestamp "gitlab-ee.funplus.io/backend-platform/zplus-go/timestamp"
)

// suppress unused package warning
var (
	_ *json.RawMessage
	_ *jlexer.Lexer
	_ *jwriter.Writer
	_ easyjson.Marshaler
)

func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc(in *jlexer.Lexer, out *WeeklyTask) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "quest_id":
			out.QuestId = int32(in.Int32())
		case "type":
			out.Type = int32(in.Int32())
		case "progress":
			out.Progress = int64(in.Int64())
		case "status":
			out.Status = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "_id":
			out.XId = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc(out *jwriter.Writer, in WeeklyTask) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"quest_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.QuestId))
	}
	{
		const prefix string = ",\"type\":"
		out.RawString(prefix)
		out.Int32(int32(in.Type))
	}
	{
		const prefix string = ",\"progress\":"
		out.RawString(prefix)
		out.Int64(int64(in.Progress))
	}
	{
		const prefix string = ",\"status\":"
		out.RawString(prefix)
		out.Int32(int32(in.Status))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix)
		out.String(string(in.XId))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v WeeklyTask) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v WeeklyTask) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *WeeklyTask) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *WeeklyTask) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc1(in *jlexer.Lexer, out *Villager) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "config_id":
			out.ConfigId = int32(in.Int32())
		case "work_building":
			out.WorkBuilding = int64(in.Int64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc1(out *jwriter.Writer, in Villager) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"config_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.ConfigId))
	}
	{
		const prefix string = ",\"work_building\":"
		out.RawString(prefix)
		out.Int64(int64(in.WorkBuilding))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v Villager) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc1(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v Villager) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc1(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *Villager) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc1(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *Villager) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc1(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc2(in *jlexer.Lexer, out *UserTroop) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "troop_type":
			out.TroopType = int32(in.Int32())
		case "total_amount":
			out.TotalAmount = int64(in.Int64())
		case "hospital_amount":
			out.HospitalAmount = int64(in.Int64())
		case "hero_troop":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.HeroTroop = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v1 int64
					v1 = int64(in.Int64())
					(out.HeroTroop)[key] = v1
					in.WantComma()
				}
				in.Delim('}')
			}
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "unclaimed_amount":
			out.UnclaimedAmount = int64(in.Int64())
		case "status":
			out.Status = int32(in.Int32())
		case "end_time":
			out.EndTime = timestamp.UTCSeconds(in.Int64())
		case "trace":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Trace = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v2 int64
					v2 = int64(in.Int64())
					(out.Trace)[key] = v2
					in.WantComma()
				}
				in.Delim('}')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc2(out *jwriter.Writer, in UserTroop) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"troop_type\":"
		out.RawString(prefix)
		out.Int32(int32(in.TroopType))
	}
	{
		const prefix string = ",\"total_amount\":"
		out.RawString(prefix)
		out.Int64(int64(in.TotalAmount))
	}
	{
		const prefix string = ",\"hospital_amount\":"
		out.RawString(prefix)
		out.Int64(int64(in.HospitalAmount))
	}
	{
		const prefix string = ",\"hero_troop\":"
		out.RawString(prefix)
		if in.HeroTroop == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v3First := true
			for v3Name, v3Value := range in.HeroTroop {
				if v3First {
					v3First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v3Name))
				out.RawByte(':')
				out.Int64(int64(v3Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"unclaimed_amount\":"
		out.RawString(prefix)
		out.Int64(int64(in.UnclaimedAmount))
	}
	{
		const prefix string = ",\"status\":"
		out.RawString(prefix)
		out.Int32(int32(in.Status))
	}
	{
		const prefix string = ",\"end_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.EndTime))
	}
	{
		const prefix string = ",\"trace\":"
		out.RawString(prefix)
		if in.Trace == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v4First := true
			for v4Name, v4Value := range in.Trace {
				if v4First {
					v4First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v4Name))
				out.RawByte(':')
				out.Int64(int64(v4Value))
			}
			out.RawByte('}')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v UserTroop) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc2(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v UserTroop) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc2(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *UserTroop) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc2(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *UserTroop) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc2(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc3(in *jlexer.Lexer, out *UserPush) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc3(out *jwriter.Writer, in UserPush) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v UserPush) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc3(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v UserPush) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc3(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *UserPush) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc3(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *UserPush) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc3(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc4(in *jlexer.Lexer, out *UserMail) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "mail_id":
			out.MailId = int64(in.Int64())
		case "title_id":
			out.TitleId = string(in.String())
		case "title_attr":
			if in.IsNull() {
				in.Skip()
				out.TitleAttr = nil
			} else {
				in.Delim('[')
				if out.TitleAttr == nil {
					if !in.IsDelim(']') {
						out.TitleAttr = make([]string, 0, 4)
					} else {
						out.TitleAttr = []string{}
					}
				} else {
					out.TitleAttr = (out.TitleAttr)[:0]
				}
				for !in.IsDelim(']') {
					var v5 string
					v5 = string(in.String())
					out.TitleAttr = append(out.TitleAttr, v5)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "body_id":
			out.BodyId = string(in.String())
		case "body_attr":
			if in.IsNull() {
				in.Skip()
				out.BodyAttr = nil
			} else {
				in.Delim('[')
				if out.BodyAttr == nil {
					if !in.IsDelim(']') {
						out.BodyAttr = make([]string, 0, 4)
					} else {
						out.BodyAttr = []string{}
					}
				} else {
					out.BodyAttr = (out.BodyAttr)[:0]
				}
				for !in.IsDelim(']') {
					var v6 string
					v6 = string(in.String())
					out.BodyAttr = append(out.BodyAttr, v6)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "attachment":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Attachment = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v7 int64
					v7 = int64(in.Int64())
					(out.Attachment)[key] = v7
					in.WantComma()
				}
				in.Delim('}')
			}
		case "status":
			out.Status = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "type":
			out.Type = int32(in.Int32())
		case "expire_time":
			out.ExpireTime = int64(in.Int64())
		case "image":
			out.Image = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc4(out *jwriter.Writer, in UserMail) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"mail_id\":"
		out.RawString(prefix)
		out.Int64(int64(in.MailId))
	}
	{
		const prefix string = ",\"title_id\":"
		out.RawString(prefix)
		out.String(string(in.TitleId))
	}
	{
		const prefix string = ",\"title_attr\":"
		out.RawString(prefix)
		if in.TitleAttr == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v8, v9 := range in.TitleAttr {
				if v8 > 0 {
					out.RawByte(',')
				}
				out.String(string(v9))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"body_id\":"
		out.RawString(prefix)
		out.String(string(in.BodyId))
	}
	{
		const prefix string = ",\"body_attr\":"
		out.RawString(prefix)
		if in.BodyAttr == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v10, v11 := range in.BodyAttr {
				if v10 > 0 {
					out.RawByte(',')
				}
				out.String(string(v11))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"attachment\":"
		out.RawString(prefix)
		if in.Attachment == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v12First := true
			for v12Name, v12Value := range in.Attachment {
				if v12First {
					v12First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v12Name))
				out.RawByte(':')
				out.Int64(int64(v12Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"status\":"
		out.RawString(prefix)
		out.Int32(int32(in.Status))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"type\":"
		out.RawString(prefix)
		out.Int32(int32(in.Type))
	}
	{
		const prefix string = ",\"expire_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.ExpireTime))
	}
	{
		const prefix string = ",\"image\":"
		out.RawString(prefix)
		out.String(string(in.Image))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v UserMail) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc4(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v UserMail) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc4(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *UserMail) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc4(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *UserMail) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc4(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc5(in *jlexer.Lexer, out *UserLookup) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "device_id":
			out.DeviceId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc5(out *jwriter.Writer, in UserLookup) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"device_id\":"
		out.RawString(prefix)
		out.String(string(in.DeviceId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v UserLookup) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc5(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v UserLookup) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc5(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *UserLookup) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc5(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *UserLookup) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc5(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc6(in *jlexer.Lexer, out *UserJob) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "world_id":
			out.WorldId = int32(in.Int32())
		case "uid":
			out.Uid = int64(in.Int64())
		case "job_id":
			out.JobId = int64(in.Int64())
		case "hint_id":
			out.HintId = int32(in.Int32())
		case "event_type":
			out.EventType = int32(in.Int32())
		case "state":
			out.State = string(in.String())
		case "retry_times":
			out.RetryTimes = int32(in.Int32())
		case "trace":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Trace = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v13 int64
					v13 = int64(in.Int64())
					(out.Trace)[key] = v13
					in.WantComma()
				}
				in.Delim('}')
			}
		case "stack_trace":
			out.StackTrace = string(in.String())
		case "time_start":
			out.TimeStart = timestamp.UTCSeconds(in.Int64())
		case "time_end":
			out.TimeEnd = timestamp.UTCSeconds(in.Int64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "float_trace":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.FloatTrace = make(map[int32]float32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v14 float32
					v14 = float32(in.Float32())
					(out.FloatTrace)[key] = v14
					in.WantComma()
				}
				in.Delim('}')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc6(out *jwriter.Writer, in UserJob) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"world_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.WorldId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"job_id\":"
		out.RawString(prefix)
		out.Int64(int64(in.JobId))
	}
	{
		const prefix string = ",\"hint_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.HintId))
	}
	{
		const prefix string = ",\"event_type\":"
		out.RawString(prefix)
		out.Int32(int32(in.EventType))
	}
	{
		const prefix string = ",\"state\":"
		out.RawString(prefix)
		out.String(string(in.State))
	}
	{
		const prefix string = ",\"retry_times\":"
		out.RawString(prefix)
		out.Int32(int32(in.RetryTimes))
	}
	{
		const prefix string = ",\"trace\":"
		out.RawString(prefix)
		if in.Trace == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v15First := true
			for v15Name, v15Value := range in.Trace {
				if v15First {
					v15First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v15Name))
				out.RawByte(':')
				out.Int64(int64(v15Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"stack_trace\":"
		out.RawString(prefix)
		out.String(string(in.StackTrace))
	}
	{
		const prefix string = ",\"time_start\":"
		out.RawString(prefix)
		out.Int64(int64(in.TimeStart))
	}
	{
		const prefix string = ",\"time_end\":"
		out.RawString(prefix)
		out.Int64(int64(in.TimeEnd))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"float_trace\":"
		out.RawString(prefix)
		if in.FloatTrace == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v16First := true
			for v16Name, v16Value := range in.FloatTrace {
				if v16First {
					v16First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v16Name))
				out.RawByte(':')
				out.Float32(float32(v16Value))
			}
			out.RawByte('}')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v UserJob) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc6(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v UserJob) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc6(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *UserJob) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc6(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *UserJob) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc6(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc7(in *jlexer.Lexer, out *UserInfo) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "world_id":
			out.WorldId = int32(in.Int32())
		case "uid":
			out.Uid = int64(in.Int64())
		case "name":
			out.Name = string(in.String())
		case "alliance_id":
			out.AllianceId = int64(in.Int64())
		case "power":
			out.Power = int64(in.Int64())
		case "portrait":
			out.Portrait = string(in.String())
		case "icon":
			out.Icon = string(in.String())
		case "chat_channel":
			out.ChatChannel = int64(in.Int64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "frame":
			out.Frame = string(in.String())
		case "formation_power":
			out.FormationPower = int64(in.Int64())
		case "born_zone_id":
			out.BornZoneId = int32(in.Int32())
		case "vip_level":
			out.VipLevel = int32(in.Int32())
		case "last_login_time":
			out.LastLoginTime = timestamp.UTCSeconds(in.Int64())
		case "battle_pos_info":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.BattlePosInfo = make(map[int32]*BattlePosInfo)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v17 *BattlePosInfo
					if in.IsNull() {
						in.Skip()
						v17 = nil
					} else {
						if v17 == nil {
							v17 = new(BattlePosInfo)
						}
						(*v17).UnmarshalEasyJSON(in)
					}
					(out.BattlePosInfo)[key] = v17
					in.WantComma()
				}
				in.Delim('}')
			}
		case "default_battle_pos":
			out.DefaultBattlePos = int32(in.Int32())
		case "research_pay_queue_status":
			out.ResearchPayQueueStatus = bool(in.Bool())
		case "avatar_config_id":
			out.AvatarConfigId = int32(in.Int32())
		case "free_change_name_times":
			out.FreeChangeNameTimes = int32(in.Int32())
		case "alliance_app_list":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.AllianceAppList = make(map[int64]int64)
				for !in.IsDelim('}') {
					key := int64(in.Int64Str())
					in.WantColon()
					var v18 int64
					v18 = int64(in.Int64())
					(out.AllianceAppList)[key] = v18
					in.WantComma()
				}
				in.Delim('}')
			}
		case "free_create_alliance_times":
			out.FreeCreateAllianceTimes = int32(in.Int32())
		case "friend_apply_list":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.FriendApplyList = make(map[int64]int64)
				for !in.IsDelim('}') {
					key := int64(in.Int64Str())
					in.WantColon()
					var v19 int64
					v19 = int64(in.Int64())
					(out.FriendApplyList)[key] = v19
					in.WantComma()
				}
				in.Delim('}')
			}
		case "recommendations_id":
			out.RecommendationsId = string(in.String())
		case "friend_stage_limit":
			out.FriendStageLimit = int32(in.Int32())
		case "allow_strangers":
			out.AllowStrangers = bool(in.Bool())
		case "cur_gem_info_id":
			out.CurGemInfoId = int32(in.Int32())
		case "total_login_days":
			out.TotalLoginDays = int32(in.Int32())
		case "ban_reason":
			out.BanReason = int32(in.Int32())
		case "hero_symbiotic":
			if in.IsNull() {
				in.Skip()
				out.HeroSymbiotic = nil
			} else {
				in.Delim('[')
				if out.HeroSymbiotic == nil {
					if !in.IsDelim(']') {
						out.HeroSymbiotic = make([]int32, 0, 16)
					} else {
						out.HeroSymbiotic = []int32{}
					}
				} else {
					out.HeroSymbiotic = (out.HeroSymbiotic)[:0]
				}
				for !in.IsDelim(']') {
					var v20 int32
					v20 = int32(in.Int32())
					out.HeroSymbiotic = append(out.HeroSymbiotic, v20)
					in.WantComma()
				}
				in.Delim(']')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc7(out *jwriter.Writer, in UserInfo) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"world_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.WorldId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"name\":"
		out.RawString(prefix)
		out.String(string(in.Name))
	}
	{
		const prefix string = ",\"alliance_id\":"
		out.RawString(prefix)
		out.Int64(int64(in.AllianceId))
	}
	{
		const prefix string = ",\"power\":"
		out.RawString(prefix)
		out.Int64(int64(in.Power))
	}
	{
		const prefix string = ",\"portrait\":"
		out.RawString(prefix)
		out.String(string(in.Portrait))
	}
	{
		const prefix string = ",\"icon\":"
		out.RawString(prefix)
		out.String(string(in.Icon))
	}
	{
		const prefix string = ",\"chat_channel\":"
		out.RawString(prefix)
		out.Int64(int64(in.ChatChannel))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"frame\":"
		out.RawString(prefix)
		out.String(string(in.Frame))
	}
	{
		const prefix string = ",\"formation_power\":"
		out.RawString(prefix)
		out.Int64(int64(in.FormationPower))
	}
	{
		const prefix string = ",\"born_zone_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.BornZoneId))
	}
	{
		const prefix string = ",\"vip_level\":"
		out.RawString(prefix)
		out.Int32(int32(in.VipLevel))
	}
	{
		const prefix string = ",\"last_login_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.LastLoginTime))
	}
	{
		const prefix string = ",\"battle_pos_info\":"
		out.RawString(prefix)
		if in.BattlePosInfo == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v21First := true
			for v21Name, v21Value := range in.BattlePosInfo {
				if v21First {
					v21First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v21Name))
				out.RawByte(':')
				if v21Value == nil {
					out.RawString("null")
				} else {
					(*v21Value).MarshalEasyJSON(out)
				}
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"default_battle_pos\":"
		out.RawString(prefix)
		out.Int32(int32(in.DefaultBattlePos))
	}
	{
		const prefix string = ",\"research_pay_queue_status\":"
		out.RawString(prefix)
		out.Bool(bool(in.ResearchPayQueueStatus))
	}
	{
		const prefix string = ",\"avatar_config_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.AvatarConfigId))
	}
	{
		const prefix string = ",\"free_change_name_times\":"
		out.RawString(prefix)
		out.Int32(int32(in.FreeChangeNameTimes))
	}
	{
		const prefix string = ",\"alliance_app_list\":"
		out.RawString(prefix)
		if in.AllianceAppList == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v22First := true
			for v22Name, v22Value := range in.AllianceAppList {
				if v22First {
					v22First = false
				} else {
					out.RawByte(',')
				}
				out.Int64Str(int64(v22Name))
				out.RawByte(':')
				out.Int64(int64(v22Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"free_create_alliance_times\":"
		out.RawString(prefix)
		out.Int32(int32(in.FreeCreateAllianceTimes))
	}
	{
		const prefix string = ",\"friend_apply_list\":"
		out.RawString(prefix)
		if in.FriendApplyList == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v23First := true
			for v23Name, v23Value := range in.FriendApplyList {
				if v23First {
					v23First = false
				} else {
					out.RawByte(',')
				}
				out.Int64Str(int64(v23Name))
				out.RawByte(':')
				out.Int64(int64(v23Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"recommendations_id\":"
		out.RawString(prefix)
		out.String(string(in.RecommendationsId))
	}
	{
		const prefix string = ",\"friend_stage_limit\":"
		out.RawString(prefix)
		out.Int32(int32(in.FriendStageLimit))
	}
	{
		const prefix string = ",\"allow_strangers\":"
		out.RawString(prefix)
		out.Bool(bool(in.AllowStrangers))
	}
	{
		const prefix string = ",\"cur_gem_info_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.CurGemInfoId))
	}
	{
		const prefix string = ",\"total_login_days\":"
		out.RawString(prefix)
		out.Int32(int32(in.TotalLoginDays))
	}
	{
		const prefix string = ",\"ban_reason\":"
		out.RawString(prefix)
		out.Int32(int32(in.BanReason))
	}
	{
		const prefix string = ",\"hero_symbiotic\":"
		out.RawString(prefix)
		if in.HeroSymbiotic == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v24, v25 := range in.HeroSymbiotic {
				if v24 > 0 {
					out.RawByte(',')
				}
				out.Int32(int32(v25))
			}
			out.RawByte(']')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v UserInfo) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc7(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v UserInfo) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc7(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *UserInfo) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc7(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *UserInfo) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc7(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc8(in *jlexer.Lexer, out *UserIapBuyTimes) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "iap_daily_buy_times":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.IapDailyBuyTimes = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v26 int64
					v26 = int64(in.Int64())
					(out.IapDailyBuyTimes)[key] = v26
					in.WantComma()
				}
				in.Delim('}')
			}
		case "iap_weekly_buy_times":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.IapWeeklyBuyTimes = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v27 int64
					v27 = int64(in.Int64())
					(out.IapWeeklyBuyTimes)[key] = v27
					in.WantComma()
				}
				in.Delim('}')
			}
		case "iap_monthly_buy_times":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.IapMonthlyBuyTimes = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v28 int64
					v28 = int64(in.Int64())
					(out.IapMonthlyBuyTimes)[key] = v28
					in.WantComma()
				}
				in.Delim('}')
			}
		case "iap_total_buy_times":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.IapTotalBuyTimes = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v29 int64
					v29 = int64(in.Int64())
					(out.IapTotalBuyTimes)[key] = v29
					in.WantComma()
				}
				in.Delim('}')
			}
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "last_reset_time":
			out.LastResetTime = timestamp.UTCSeconds(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc8(out *jwriter.Writer, in UserIapBuyTimes) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"iap_daily_buy_times\":"
		out.RawString(prefix)
		if in.IapDailyBuyTimes == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v30First := true
			for v30Name, v30Value := range in.IapDailyBuyTimes {
				if v30First {
					v30First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v30Name))
				out.RawByte(':')
				out.Int64(int64(v30Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"iap_weekly_buy_times\":"
		out.RawString(prefix)
		if in.IapWeeklyBuyTimes == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v31First := true
			for v31Name, v31Value := range in.IapWeeklyBuyTimes {
				if v31First {
					v31First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v31Name))
				out.RawByte(':')
				out.Int64(int64(v31Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"iap_monthly_buy_times\":"
		out.RawString(prefix)
		if in.IapMonthlyBuyTimes == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v32First := true
			for v32Name, v32Value := range in.IapMonthlyBuyTimes {
				if v32First {
					v32First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v32Name))
				out.RawByte(':')
				out.Int64(int64(v32Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"iap_total_buy_times\":"
		out.RawString(prefix)
		if in.IapTotalBuyTimes == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v33First := true
			for v33Name, v33Value := range in.IapTotalBuyTimes {
				if v33First {
					v33First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v33Name))
				out.RawByte(':')
				out.Int64(int64(v33Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"last_reset_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.LastResetTime))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v UserIapBuyTimes) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc8(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v UserIapBuyTimes) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc8(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *UserIapBuyTimes) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc8(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *UserIapBuyTimes) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc8(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc9(in *jlexer.Lexer, out *UserDevices) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "android_id":
			out.AndroidId = string(in.String())
		case "app_install_ts":
			out.AppInstallTs = int64(in.Int64())
		case "app_instance_id":
			out.AppInstanceId = string(in.String())
		case "app_version":
			out.AppVersion = string(in.String())
		case "appsflyer_id":
			out.AppsflyerId = string(in.String())
		case "bundle_id":
			out.BundleId = string(in.String())
		case "channel_id":
			out.ChannelId = string(in.String())
		case "device_id":
			out.DeviceId = string(in.String())
		case "device_lang":
			out.DeviceLang = string(in.String())
		case "device_type":
			out.DeviceType = string(in.String())
		case "display_size":
			out.DisplaySize = string(in.String())
		case "du_cdid":
			out.DuCdid = string(in.String())
		case "du_idfa":
			out.DuIdfa = string(in.String())
		case "du_oaid":
			out.DuOaid = string(in.String())
		case "fp_device_id":
			out.FpDeviceId = string(in.String())
		case "fpid_create_ts":
			out.FpidCreateTs = int64(in.Int64())
		case "gaid":
			out.Gaid = string(in.String())
		case "game_uid":
			out.GameUid = string(in.String())
		case "game_uid_create_ts":
			out.GameUidCreateTs = int64(in.Int64())
		case "gameserver_id":
			out.GameserverId = string(in.String())
		case "idfa":
			out.Idfa = string(in.String())
		case "idfv":
			out.Idfv = string(in.String())
		case "imei":
			out.Imei = string(in.String())
		case "ip":
			out.Ip = string(in.String())
		case "lang":
			out.Lang = string(in.String())
		case "level":
			out.Level = int32(in.Int32())
		case "oaid":
			out.Oaid = string(in.String())
		case "os":
			out.Os = string(in.String())
		case "os_version":
			out.OsVersion = string(in.String())
		case "pkg_channel":
			out.PkgChannel = string(in.String())
		case "data_version":
			out.DataVersion = string(in.String())
		case "fpid":
			out.Fpid = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc9(out *jwriter.Writer, in UserDevices) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"android_id\":"
		out.RawString(prefix)
		out.String(string(in.AndroidId))
	}
	{
		const prefix string = ",\"app_install_ts\":"
		out.RawString(prefix)
		out.Int64(int64(in.AppInstallTs))
	}
	{
		const prefix string = ",\"app_instance_id\":"
		out.RawString(prefix)
		out.String(string(in.AppInstanceId))
	}
	{
		const prefix string = ",\"app_version\":"
		out.RawString(prefix)
		out.String(string(in.AppVersion))
	}
	{
		const prefix string = ",\"appsflyer_id\":"
		out.RawString(prefix)
		out.String(string(in.AppsflyerId))
	}
	{
		const prefix string = ",\"bundle_id\":"
		out.RawString(prefix)
		out.String(string(in.BundleId))
	}
	{
		const prefix string = ",\"channel_id\":"
		out.RawString(prefix)
		out.String(string(in.ChannelId))
	}
	{
		const prefix string = ",\"device_id\":"
		out.RawString(prefix)
		out.String(string(in.DeviceId))
	}
	{
		const prefix string = ",\"device_lang\":"
		out.RawString(prefix)
		out.String(string(in.DeviceLang))
	}
	{
		const prefix string = ",\"device_type\":"
		out.RawString(prefix)
		out.String(string(in.DeviceType))
	}
	{
		const prefix string = ",\"display_size\":"
		out.RawString(prefix)
		out.String(string(in.DisplaySize))
	}
	{
		const prefix string = ",\"du_cdid\":"
		out.RawString(prefix)
		out.String(string(in.DuCdid))
	}
	{
		const prefix string = ",\"du_idfa\":"
		out.RawString(prefix)
		out.String(string(in.DuIdfa))
	}
	{
		const prefix string = ",\"du_oaid\":"
		out.RawString(prefix)
		out.String(string(in.DuOaid))
	}
	{
		const prefix string = ",\"fp_device_id\":"
		out.RawString(prefix)
		out.String(string(in.FpDeviceId))
	}
	{
		const prefix string = ",\"fpid_create_ts\":"
		out.RawString(prefix)
		out.Int64(int64(in.FpidCreateTs))
	}
	{
		const prefix string = ",\"gaid\":"
		out.RawString(prefix)
		out.String(string(in.Gaid))
	}
	{
		const prefix string = ",\"game_uid\":"
		out.RawString(prefix)
		out.String(string(in.GameUid))
	}
	{
		const prefix string = ",\"game_uid_create_ts\":"
		out.RawString(prefix)
		out.Int64(int64(in.GameUidCreateTs))
	}
	{
		const prefix string = ",\"gameserver_id\":"
		out.RawString(prefix)
		out.String(string(in.GameserverId))
	}
	{
		const prefix string = ",\"idfa\":"
		out.RawString(prefix)
		out.String(string(in.Idfa))
	}
	{
		const prefix string = ",\"idfv\":"
		out.RawString(prefix)
		out.String(string(in.Idfv))
	}
	{
		const prefix string = ",\"imei\":"
		out.RawString(prefix)
		out.String(string(in.Imei))
	}
	{
		const prefix string = ",\"ip\":"
		out.RawString(prefix)
		out.String(string(in.Ip))
	}
	{
		const prefix string = ",\"lang\":"
		out.RawString(prefix)
		out.String(string(in.Lang))
	}
	{
		const prefix string = ",\"level\":"
		out.RawString(prefix)
		out.Int32(int32(in.Level))
	}
	{
		const prefix string = ",\"oaid\":"
		out.RawString(prefix)
		out.String(string(in.Oaid))
	}
	{
		const prefix string = ",\"os\":"
		out.RawString(prefix)
		out.String(string(in.Os))
	}
	{
		const prefix string = ",\"os_version\":"
		out.RawString(prefix)
		out.String(string(in.OsVersion))
	}
	{
		const prefix string = ",\"pkg_channel\":"
		out.RawString(prefix)
		out.String(string(in.PkgChannel))
	}
	{
		const prefix string = ",\"data_version\":"
		out.RawString(prefix)
		out.String(string(in.DataVersion))
	}
	{
		const prefix string = ",\"fpid\":"
		out.RawString(prefix)
		out.String(string(in.Fpid))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v UserDevices) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc9(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v UserDevices) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc9(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *UserDevices) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc9(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *UserDevices) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc9(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc10(in *jlexer.Lexer, out *UserData) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "type":
			out.Type = int32(in.Int32())
		case "value":
			out.Value = int64(in.Int64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "content":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Content = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v34 int64
					v34 = int64(in.Int64())
					(out.Content)[key] = v34
					in.WantComma()
				}
				in.Delim('}')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc10(out *jwriter.Writer, in UserData) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"type\":"
		out.RawString(prefix)
		out.Int32(int32(in.Type))
	}
	{
		const prefix string = ",\"value\":"
		out.RawString(prefix)
		out.Int64(int64(in.Value))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"content\":"
		out.RawString(prefix)
		if in.Content == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v35First := true
			for v35Name, v35Value := range in.Content {
				if v35First {
					v35First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v35Name))
				out.RawByte(':')
				out.Int64(int64(v35Value))
			}
			out.RawByte('}')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v UserData) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc10(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v UserData) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc10(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *UserData) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc10(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *UserData) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc10(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc11(in *jlexer.Lexer, out *UserBenefit) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "config_id":
			out.ConfigId = int32(in.Int32())
		case "trace":
			out.Trace = string(in.String())
		case "value":
			out.Value = float64(in.Float64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc11(out *jwriter.Writer, in UserBenefit) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"config_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.ConfigId))
	}
	{
		const prefix string = ",\"trace\":"
		out.RawString(prefix)
		out.String(string(in.Trace))
	}
	{
		const prefix string = ",\"value\":"
		out.RawString(prefix)
		out.Float64(float64(in.Value))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v UserBenefit) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc11(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v UserBenefit) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc11(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *UserBenefit) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc11(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *UserBenefit) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc11(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc12(in *jlexer.Lexer, out *TaskCounter) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "counter_id":
			out.CounterId = int32(in.Int32())
		case "counter_value":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.CounterValue = make(map[string]int32)
				for !in.IsDelim('}') {
					key := string(in.String())
					in.WantColon()
					var v36 int32
					v36 = int32(in.Int32())
					(out.CounterValue)[key] = v36
					in.WantComma()
				}
				in.Delim('}')
			}
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc12(out *jwriter.Writer, in TaskCounter) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"counter_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.CounterId))
	}
	{
		const prefix string = ",\"counter_value\":"
		out.RawString(prefix)
		if in.CounterValue == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v37First := true
			for v37Name, v37Value := range in.CounterValue {
				if v37First {
					v37First = false
				} else {
					out.RawByte(',')
				}
				out.String(string(v37Name))
				out.RawByte(':')
				out.Int32(int32(v37Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v TaskCounter) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc12(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v TaskCounter) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc12(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *TaskCounter) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc12(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *TaskCounter) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc12(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc13(in *jlexer.Lexer, out *StageFinishStatus) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "stage_id":
			out.StageId = int32(in.Int32())
		case "perfect_status":
			out.PerfectStatus = int32(in.Int32())
		case "collect_reward":
			if in.IsNull() {
				in.Skip()
				out.CollectReward = nil
			} else {
				in.Delim('[')
				if out.CollectReward == nil {
					if !in.IsDelim(']') {
						out.CollectReward = make([]int32, 0, 16)
					} else {
						out.CollectReward = []int32{}
					}
				} else {
					out.CollectReward = (out.CollectReward)[:0]
				}
				for !in.IsDelim(']') {
					var v38 int32
					v38 = int32(in.Int32())
					out.CollectReward = append(out.CollectReward, v38)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "finish_elite":
			out.FinishElite = bool(in.Bool())
		case "max_time":
			out.MaxTime = int32(in.Int32())
		case "max_hp_percent":
			out.MaxHpPercent = float32(in.Float32())
		case "min_time":
			out.MinTime = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc13(out *jwriter.Writer, in StageFinishStatus) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"stage_id\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.StageId))
	}
	{
		const prefix string = ",\"perfect_status\":"
		out.RawString(prefix)
		out.Int32(int32(in.PerfectStatus))
	}
	{
		const prefix string = ",\"collect_reward\":"
		out.RawString(prefix)
		if in.CollectReward == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v39, v40 := range in.CollectReward {
				if v39 > 0 {
					out.RawByte(',')
				}
				out.Int32(int32(v40))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"finish_elite\":"
		out.RawString(prefix)
		out.Bool(bool(in.FinishElite))
	}
	{
		const prefix string = ",\"max_time\":"
		out.RawString(prefix)
		out.Int32(int32(in.MaxTime))
	}
	{
		const prefix string = ",\"max_hp_percent\":"
		out.RawString(prefix)
		out.Float32(float32(in.MaxHpPercent))
	}
	{
		const prefix string = ",\"min_time\":"
		out.RawString(prefix)
		out.Int32(int32(in.MinTime))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v StageFinishStatus) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc13(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v StageFinishStatus) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc13(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *StageFinishStatus) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc13(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *StageFinishStatus) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc13(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc14(in *jlexer.Lexer, out *Research) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "tech_id":
			out.TechId = int32(in.Int32())
		case "level_id":
			out.LevelId = int32(in.Int32())
		case "job_id":
			out.JobId = int64(in.Int64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "status":
			out.Status = int32(in.Int32())
		case "end_time":
			out.EndTime = timestamp.UTCSeconds(in.Int64())
		case "trace":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Trace = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v41 int64
					v41 = int64(in.Int64())
					(out.Trace)[key] = v41
					in.WantComma()
				}
				in.Delim('}')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc14(out *jwriter.Writer, in Research) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"tech_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.TechId))
	}
	{
		const prefix string = ",\"level_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.LevelId))
	}
	{
		const prefix string = ",\"job_id\":"
		out.RawString(prefix)
		out.Int64(int64(in.JobId))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"status\":"
		out.RawString(prefix)
		out.Int32(int32(in.Status))
	}
	{
		const prefix string = ",\"end_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.EndTime))
	}
	{
		const prefix string = ",\"trace\":"
		out.RawString(prefix)
		if in.Trace == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v42First := true
			for v42Name, v42Value := range in.Trace {
				if v42First {
					v42First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v42Name))
				out.RawByte(':')
				out.Int64(int64(v42Value))
			}
			out.RawByte('}')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v Research) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc14(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v Research) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc14(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *Research) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc14(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *Research) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc14(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc15(in *jlexer.Lexer, out *RepeatTask) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "quest_id":
			out.QuestId = int32(in.Int32())
		case "type":
			out.Type = int32(in.Int32())
		case "progress":
			out.Progress = int64(in.Int64())
		case "status":
			out.Status = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc15(out *jwriter.Writer, in RepeatTask) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"quest_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.QuestId))
	}
	{
		const prefix string = ",\"type\":"
		out.RawString(prefix)
		out.Int32(int32(in.Type))
	}
	{
		const prefix string = ",\"progress\":"
		out.RawString(prefix)
		out.Int64(int64(in.Progress))
	}
	{
		const prefix string = ",\"status\":"
		out.RawString(prefix)
		out.Int32(int32(in.Status))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v RepeatTask) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc15(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v RepeatTask) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc15(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *RepeatTask) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc15(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *RepeatTask) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc15(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc16(in *jlexer.Lexer, out *RegularPack) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "group_id":
			out.GroupId = int32(in.Int32())
		case "iap_buy_times":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.IapBuyTimes = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v43 int32
					v43 = int32(in.Int32())
					(out.IapBuyTimes)[key] = v43
					in.WantComma()
				}
				in.Delim('}')
			}
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "start_time":
			out.StartTime = timestamp.UTCSeconds(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc16(out *jwriter.Writer, in RegularPack) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"group_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.GroupId))
	}
	{
		const prefix string = ",\"iap_buy_times\":"
		out.RawString(prefix)
		if in.IapBuyTimes == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v44First := true
			for v44Name, v44Value := range in.IapBuyTimes {
				if v44First {
					v44First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v44Name))
				out.RawByte(':')
				out.Int32(int32(v44Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"start_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.StartTime))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v RegularPack) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc16(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v RegularPack) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc16(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *RegularPack) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc16(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *RegularPack) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc16(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc17(in *jlexer.Lexer, out *PerPlayerScore) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "score":
			out.Score = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc17(out *jwriter.Writer, in PerPlayerScore) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"score\":"
		out.RawString(prefix)
		out.Int32(int32(in.Score))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v PerPlayerScore) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc17(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v PerPlayerScore) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc17(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *PerPlayerScore) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc17(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *PerPlayerScore) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc17(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc18(in *jlexer.Lexer, out *PaymentOrder) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "order_id":
			out.OrderId = string(in.String())
		case "product_id":
			out.ProductId = int32(in.Int32())
		case "extra":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Extra = make(map[string]string)
				for !in.IsDelim('}') {
					key := string(in.String())
					in.WantColon()
					var v45 string
					v45 = string(in.String())
					(out.Extra)[key] = v45
					in.WantComma()
				}
				in.Delim('}')
			}
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "status":
			out.Status = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc18(out *jwriter.Writer, in PaymentOrder) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"order_id\":"
		out.RawString(prefix)
		out.String(string(in.OrderId))
	}
	{
		const prefix string = ",\"product_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.ProductId))
	}
	{
		const prefix string = ",\"extra\":"
		out.RawString(prefix)
		if in.Extra == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v46First := true
			for v46Name, v46Value := range in.Extra {
				if v46First {
					v46First = false
				} else {
					out.RawByte(',')
				}
				out.String(string(v46Name))
				out.RawByte(':')
				out.String(string(v46Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"status\":"
		out.RawString(prefix)
		out.Int32(int32(in.Status))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v PaymentOrder) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc18(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v PaymentOrder) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc18(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *PaymentOrder) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc18(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *PaymentOrder) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc18(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc19(in *jlexer.Lexer, out *OutConsumable) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "config_id":
			out.ConfigId = int32(in.Int32())
		case "quantity":
			out.Quantity = int64(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc19(out *jwriter.Writer, in OutConsumable) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"config_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.ConfigId))
	}
	{
		const prefix string = ",\"quantity\":"
		out.RawString(prefix)
		out.Int64(int64(in.Quantity))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v OutConsumable) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc19(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v OutConsumable) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc19(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *OutConsumable) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc19(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *OutConsumable) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc19(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc20(in *jlexer.Lexer, out *OrderResult) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "order_id":
			out.OrderId = string(in.String())
		case "package_id":
			out.PackageId = int32(in.Int32())
		case "seq":
			out.Seq = int32(in.Int32())
		case "rewards":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Rewards = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v47 int64
					v47 = int64(in.Int64())
					(out.Rewards)[key] = v47
					in.WantComma()
				}
				in.Delim('}')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc20(out *jwriter.Writer, in OrderResult) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"order_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.OrderId))
	}
	{
		const prefix string = ",\"package_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.PackageId))
	}
	{
		const prefix string = ",\"seq\":"
		out.RawString(prefix)
		out.Int32(int32(in.Seq))
	}
	{
		const prefix string = ",\"rewards\":"
		out.RawString(prefix)
		if in.Rewards == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v48First := true
			for v48Name, v48Value := range in.Rewards {
				if v48First {
					v48First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v48Name))
				out.RawByte(':')
				out.Int64(int64(v48Value))
			}
			out.RawByte('}')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v OrderResult) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc20(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v OrderResult) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc20(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *OrderResult) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc20(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *OrderResult) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc20(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc21(in *jlexer.Lexer, out *NewbieGuide) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "guide_list":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.GuideList = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v49 int32
					v49 = int32(in.Int32())
					(out.GuideList)[key] = v49
					in.WantComma()
				}
				in.Delim('}')
			}
		case "cur_guide_id":
			out.CurGuideId = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc21(out *jwriter.Writer, in NewbieGuide) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"guide_list\":"
		out.RawString(prefix)
		if in.GuideList == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v50First := true
			for v50Name, v50Value := range in.GuideList {
				if v50First {
					v50First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v50Name))
				out.RawByte(':')
				out.Int32(int32(v50Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"cur_guide_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.CurGuideId))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v NewbieGuide) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc21(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v NewbieGuide) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc21(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *NewbieGuide) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc21(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *NewbieGuide) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc21(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc22(in *jlexer.Lexer, out *MonthCard) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "card_id":
			out.CardId = int32(in.Int32())
		case "expire_time":
			out.ExpireTime = timestamp.UTCSeconds(in.Int64())
		case "last_receive_time":
			out.LastReceiveTime = timestamp.UTCSeconds(in.Int64())
		case "has_received_today":
			out.HasReceivedToday = bool(in.Bool())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc22(out *jwriter.Writer, in MonthCard) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"card_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.CardId))
	}
	{
		const prefix string = ",\"expire_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.ExpireTime))
	}
	{
		const prefix string = ",\"last_receive_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.LastReceiveTime))
	}
	{
		const prefix string = ",\"has_received_today\":"
		out.RawString(prefix)
		out.Bool(bool(in.HasReceivedToday))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v MonthCard) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc22(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v MonthCard) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc22(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *MonthCard) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc22(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *MonthCard) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc22(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc23(in *jlexer.Lexer, out *MonsterInfo) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "id":
			out.Id = int64(in.Int64())
		case "config_id":
			out.ConfigId = int64(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc23(out *jwriter.Writer, in MonsterInfo) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"id\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Id))
	}
	{
		const prefix string = ",\"config_id\":"
		out.RawString(prefix)
		out.Int64(int64(in.ConfigId))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v MonsterInfo) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc23(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v MonsterInfo) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc23(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *MonsterInfo) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc23(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *MonsterInfo) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc23(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc24(in *jlexer.Lexer, out *MapEventSection) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "section_status":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.SectionStatus = make(map[int32]*MapEventRegion)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v51 *MapEventRegion
					if in.IsNull() {
						in.Skip()
						v51 = nil
					} else {
						if v51 == nil {
							v51 = new(MapEventRegion)
						}
						(*v51).UnmarshalEasyJSON(in)
					}
					(out.SectionStatus)[key] = v51
					in.WantComma()
				}
				in.Delim('}')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc24(out *jwriter.Writer, in MapEventSection) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"section_status\":"
		out.RawString(prefix[1:])
		if in.SectionStatus == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v52First := true
			for v52Name, v52Value := range in.SectionStatus {
				if v52First {
					v52First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v52Name))
				out.RawByte(':')
				if v52Value == nil {
					out.RawString("null")
				} else {
					(*v52Value).MarshalEasyJSON(out)
				}
			}
			out.RawByte('}')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v MapEventSection) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc24(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v MapEventSection) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc24(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *MapEventSection) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc24(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *MapEventSection) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc24(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc25(in *jlexer.Lexer, out *MapEventRegion) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "unlock":
			out.Unlock = bool(in.Bool())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc25(out *jwriter.Writer, in MapEventRegion) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"unlock\":"
		out.RawString(prefix[1:])
		out.Bool(bool(in.Unlock))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v MapEventRegion) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc25(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v MapEventRegion) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc25(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *MapEventRegion) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc25(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *MapEventRegion) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc25(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc26(in *jlexer.Lexer, out *MapEventLastKillTimes) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "kill_times":
			if in.IsNull() {
				in.Skip()
				out.KillTimes = nil
			} else {
				in.Delim('[')
				if out.KillTimes == nil {
					if !in.IsDelim(']') {
						out.KillTimes = make([]int64, 0, 8)
					} else {
						out.KillTimes = []int64{}
					}
				} else {
					out.KillTimes = (out.KillTimes)[:0]
				}
				for !in.IsDelim(']') {
					var v53 int64
					v53 = int64(in.Int64())
					out.KillTimes = append(out.KillTimes, v53)
					in.WantComma()
				}
				in.Delim(']')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc26(out *jwriter.Writer, in MapEventLastKillTimes) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"kill_times\":"
		out.RawString(prefix[1:])
		if in.KillTimes == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v54, v55 := range in.KillTimes {
				if v54 > 0 {
					out.RawByte(',')
				}
				out.Int64(int64(v55))
			}
			out.RawByte(']')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v MapEventLastKillTimes) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc26(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v MapEventLastKillTimes) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc26(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *MapEventLastKillTimes) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc26(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *MapEventLastKillTimes) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc26(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc27(in *jlexer.Lexer, out *MapEventChapter) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "section_status":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.SectionStatus = make(map[int32]*MapEventSection)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v56 *MapEventSection
					if in.IsNull() {
						in.Skip()
						v56 = nil
					} else {
						if v56 == nil {
							v56 = new(MapEventSection)
						}
						(*v56).UnmarshalEasyJSON(in)
					}
					(out.SectionStatus)[key] = v56
					in.WantComma()
				}
				in.Delim('}')
			}
		case "explode_value":
			out.ExplodeValue = int32(in.Int32())
		case "rewards":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Rewards = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v57 int32
					v57 = int32(in.Int32())
					(out.Rewards)[key] = v57
					in.WantComma()
				}
				in.Delim('}')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc27(out *jwriter.Writer, in MapEventChapter) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"section_status\":"
		out.RawString(prefix[1:])
		if in.SectionStatus == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v58First := true
			for v58Name, v58Value := range in.SectionStatus {
				if v58First {
					v58First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v58Name))
				out.RawByte(':')
				if v58Value == nil {
					out.RawString("null")
				} else {
					(*v58Value).MarshalEasyJSON(out)
				}
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"explode_value\":"
		out.RawString(prefix)
		out.Int32(int32(in.ExplodeValue))
	}
	{
		const prefix string = ",\"rewards\":"
		out.RawString(prefix)
		if in.Rewards == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v59First := true
			for v59Name, v59Value := range in.Rewards {
				if v59First {
					v59First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v59Name))
				out.RawByte(':')
				out.Int32(int32(v59Value))
			}
			out.RawByte('}')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v MapEventChapter) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc27(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v MapEventChapter) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc27(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *MapEventChapter) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc27(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *MapEventChapter) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc27(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc28(in *jlexer.Lexer, out *MapEvent) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "unlock_chapter":
			out.UnlockChapter = int64(in.Int64())
		case "id_status":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.IdStatus = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v60 int32
					v60 = int32(in.Int32())
					(out.IdStatus)[key] = v60
					in.WantComma()
				}
				in.Delim('}')
			}
		case "chapter_status":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.ChapterStatus = make(map[int32]*MapEventChapter)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v61 *MapEventChapter
					if in.IsNull() {
						in.Skip()
						v61 = nil
					} else {
						if v61 == nil {
							v61 = new(MapEventChapter)
						}
						(*v61).UnmarshalEasyJSON(in)
					}
					(out.ChapterStatus)[key] = v61
					in.WantComma()
				}
				in.Delim('}')
			}
		case "last_kill_time":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.LastKillTime = make(map[int32]*MapEventLastKillTimes)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v62 *MapEventLastKillTimes
					if in.IsNull() {
						in.Skip()
						v62 = nil
					} else {
						if v62 == nil {
							v62 = new(MapEventLastKillTimes)
						}
						(*v62).UnmarshalEasyJSON(in)
					}
					(out.LastKillTime)[key] = v62
					in.WantComma()
				}
				in.Delim('}')
			}
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc28(out *jwriter.Writer, in MapEvent) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"unlock_chapter\":"
		out.RawString(prefix)
		out.Int64(int64(in.UnlockChapter))
	}
	{
		const prefix string = ",\"id_status\":"
		out.RawString(prefix)
		if in.IdStatus == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v63First := true
			for v63Name, v63Value := range in.IdStatus {
				if v63First {
					v63First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v63Name))
				out.RawByte(':')
				out.Int32(int32(v63Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"chapter_status\":"
		out.RawString(prefix)
		if in.ChapterStatus == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v64First := true
			for v64Name, v64Value := range in.ChapterStatus {
				if v64First {
					v64First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v64Name))
				out.RawByte(':')
				if v64Value == nil {
					out.RawString("null")
				} else {
					(*v64Value).MarshalEasyJSON(out)
				}
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"last_kill_time\":"
		out.RawString(prefix)
		if in.LastKillTime == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v65First := true
			for v65Name, v65Value := range in.LastKillTime {
				if v65First {
					v65First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v65Name))
				out.RawByte(':')
				if v65Value == nil {
					out.RawString("null")
				} else {
					(*v65Value).MarshalEasyJSON(out)
				}
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v MapEvent) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc28(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v MapEvent) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc28(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *MapEvent) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc28(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *MapEvent) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc28(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc29(in *jlexer.Lexer, out *MainTask) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "quest_id":
			out.QuestId = int32(in.Int32())
		case "progress":
			out.Progress = int64(in.Int64())
		case "status":
			out.Status = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "type":
			out.Type = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc29(out *jwriter.Writer, in MainTask) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"quest_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.QuestId))
	}
	{
		const prefix string = ",\"progress\":"
		out.RawString(prefix)
		out.Int64(int64(in.Progress))
	}
	{
		const prefix string = ",\"status\":"
		out.RawString(prefix)
		out.Int32(int32(in.Status))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"type\":"
		out.RawString(prefix)
		out.Int32(int32(in.Type))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v MainTask) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc29(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v MainTask) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc29(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *MainTask) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc29(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *MainTask) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc29(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc30(in *jlexer.Lexer, out *MainLineStage) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "stage_id":
			out.StageId = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "level_struct":
			if in.IsNull() {
				in.Skip()
				out.LevelStruct = nil
			} else {
				in.Delim('[')
				if out.LevelStruct == nil {
					if !in.IsDelim(']') {
						out.LevelStruct = make([]*LevelStruct, 0, 8)
					} else {
						out.LevelStruct = []*LevelStruct{}
					}
				} else {
					out.LevelStruct = (out.LevelStruct)[:0]
				}
				for !in.IsDelim(']') {
					var v66 *LevelStruct
					if in.IsNull() {
						in.Skip()
						v66 = nil
					} else {
						if v66 == nil {
							v66 = new(LevelStruct)
						}
						(*v66).UnmarshalEasyJSON(in)
					}
					out.LevelStruct = append(out.LevelStruct, v66)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "stage_rewards":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.StageRewards = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v67 int64
					v67 = int64(in.Int64())
					(out.StageRewards)[key] = v67
					in.WantComma()
				}
				in.Delim('}')
			}
		case "unlock_stage_id":
			out.UnlockStageId = int32(in.Int32())
		case "is_fail":
			out.IsFail = bool(in.Bool())
		case "refresh_card_ids":
			if in.IsNull() {
				in.Skip()
				out.RefreshCardIds = nil
			} else {
				in.Delim('[')
				if out.RefreshCardIds == nil {
					if !in.IsDelim(']') {
						out.RefreshCardIds = make([]int32, 0, 16)
					} else {
						out.RefreshCardIds = []int32{}
					}
				} else {
					out.RefreshCardIds = (out.RefreshCardIds)[:0]
				}
				for !in.IsDelim(']') {
					var v68 int32
					v68 = int32(in.Int32())
					out.RefreshCardIds = append(out.RefreshCardIds, v68)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "stage_finish_status":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.StageFinishStatus = make(map[int32]*StageFinishStatus)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v69 *StageFinishStatus
					if in.IsNull() {
						in.Skip()
						v69 = nil
					} else {
						if v69 == nil {
							v69 = new(StageFinishStatus)
						}
						(*v69).UnmarshalEasyJSON(in)
					}
					(out.StageFinishStatus)[key] = v69
					in.WantComma()
				}
				in.Delim('}')
			}
		case "cur_kill_monster":
			out.CurKillMonster = int32(in.Int32())
		case "cur_refresh_rouge_level":
			out.CurRefreshRougeLevel = int32(in.Int32())
		case "cur_select_card_ids":
			if in.IsNull() {
				in.Skip()
				out.CurSelectCardIds = nil
			} else {
				in.Delim('[')
				if out.CurSelectCardIds == nil {
					if !in.IsDelim(']') {
						out.CurSelectCardIds = make([]int32, 0, 16)
					} else {
						out.CurSelectCardIds = []int32{}
					}
				} else {
					out.CurSelectCardIds = (out.CurSelectCardIds)[:0]
				}
				for !in.IsDelim(']') {
					var v70 int32
					v70 = int32(in.Int32())
					out.CurSelectCardIds = append(out.CurSelectCardIds, v70)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "elite_refresh_card_ids":
			if in.IsNull() {
				in.Skip()
				out.EliteRefreshCardIds = nil
			} else {
				in.Delim('[')
				if out.EliteRefreshCardIds == nil {
					if !in.IsDelim(']') {
						out.EliteRefreshCardIds = make([]int32, 0, 16)
					} else {
						out.EliteRefreshCardIds = []int32{}
					}
				} else {
					out.EliteRefreshCardIds = (out.EliteRefreshCardIds)[:0]
				}
				for !in.IsDelim(']') {
					var v71 int32
					v71 = int32(in.Int32())
					out.EliteRefreshCardIds = append(out.EliteRefreshCardIds, v71)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "collection_rank_reward":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.CollectionRankReward = make(map[int32]bool)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v72 bool
					v72 = bool(in.Bool())
					(out.CollectionRankReward)[key] = v72
					in.WantComma()
				}
				in.Delim('}')
			}
		case "today_sweep_times":
			out.TodaySweepTimes = int32(in.Int32())
		case "is_elite":
			out.IsElite = bool(in.Bool())
		case "cur_paid_refresh_times":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.CurPaidRefreshTimes = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v73 int32
					v73 = int32(in.Int32())
					(out.CurPaidRefreshTimes)[key] = v73
					in.WantComma()
				}
				in.Delim('}')
			}
		case "killed_card_ids":
			if in.IsNull() {
				in.Skip()
				out.KilledCardIds = nil
			} else {
				in.Delim('[')
				if out.KilledCardIds == nil {
					if !in.IsDelim(']') {
						out.KilledCardIds = make([]int32, 0, 16)
					} else {
						out.KilledCardIds = []int32{}
					}
				} else {
					out.KilledCardIds = (out.KilledCardIds)[:0]
				}
				for !in.IsDelim(']') {
					var v74 int32
					v74 = int32(in.Int32())
					out.KilledCardIds = append(out.KilledCardIds, v74)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "cur_elite_cards_refresh_times":
			out.CurEliteCardsRefreshTimes = int32(in.Int32())
		case "cur_stage_start_time":
			out.CurStageStartTime = int64(in.Int64())
		case "cur_normal_cards_refresh_times":
			out.CurNormalCardsRefreshTimes = int32(in.Int32())
		case "cur_stage_finish":
			out.CurStageFinish = bool(in.Bool())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc30(out *jwriter.Writer, in MainLineStage) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"stage_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.StageId))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"level_struct\":"
		out.RawString(prefix)
		if in.LevelStruct == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v75, v76 := range in.LevelStruct {
				if v75 > 0 {
					out.RawByte(',')
				}
				if v76 == nil {
					out.RawString("null")
				} else {
					(*v76).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"stage_rewards\":"
		out.RawString(prefix)
		if in.StageRewards == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v77First := true
			for v77Name, v77Value := range in.StageRewards {
				if v77First {
					v77First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v77Name))
				out.RawByte(':')
				out.Int64(int64(v77Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"unlock_stage_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.UnlockStageId))
	}
	{
		const prefix string = ",\"is_fail\":"
		out.RawString(prefix)
		out.Bool(bool(in.IsFail))
	}
	{
		const prefix string = ",\"refresh_card_ids\":"
		out.RawString(prefix)
		if in.RefreshCardIds == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v78, v79 := range in.RefreshCardIds {
				if v78 > 0 {
					out.RawByte(',')
				}
				out.Int32(int32(v79))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"stage_finish_status\":"
		out.RawString(prefix)
		if in.StageFinishStatus == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v80First := true
			for v80Name, v80Value := range in.StageFinishStatus {
				if v80First {
					v80First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v80Name))
				out.RawByte(':')
				if v80Value == nil {
					out.RawString("null")
				} else {
					(*v80Value).MarshalEasyJSON(out)
				}
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"cur_kill_monster\":"
		out.RawString(prefix)
		out.Int32(int32(in.CurKillMonster))
	}
	{
		const prefix string = ",\"cur_refresh_rouge_level\":"
		out.RawString(prefix)
		out.Int32(int32(in.CurRefreshRougeLevel))
	}
	{
		const prefix string = ",\"cur_select_card_ids\":"
		out.RawString(prefix)
		if in.CurSelectCardIds == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v81, v82 := range in.CurSelectCardIds {
				if v81 > 0 {
					out.RawByte(',')
				}
				out.Int32(int32(v82))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"elite_refresh_card_ids\":"
		out.RawString(prefix)
		if in.EliteRefreshCardIds == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v83, v84 := range in.EliteRefreshCardIds {
				if v83 > 0 {
					out.RawByte(',')
				}
				out.Int32(int32(v84))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"collection_rank_reward\":"
		out.RawString(prefix)
		if in.CollectionRankReward == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v85First := true
			for v85Name, v85Value := range in.CollectionRankReward {
				if v85First {
					v85First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v85Name))
				out.RawByte(':')
				out.Bool(bool(v85Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"today_sweep_times\":"
		out.RawString(prefix)
		out.Int32(int32(in.TodaySweepTimes))
	}
	{
		const prefix string = ",\"is_elite\":"
		out.RawString(prefix)
		out.Bool(bool(in.IsElite))
	}
	{
		const prefix string = ",\"cur_paid_refresh_times\":"
		out.RawString(prefix)
		if in.CurPaidRefreshTimes == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v86First := true
			for v86Name, v86Value := range in.CurPaidRefreshTimes {
				if v86First {
					v86First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v86Name))
				out.RawByte(':')
				out.Int32(int32(v86Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"killed_card_ids\":"
		out.RawString(prefix)
		if in.KilledCardIds == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v87, v88 := range in.KilledCardIds {
				if v87 > 0 {
					out.RawByte(',')
				}
				out.Int32(int32(v88))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"cur_elite_cards_refresh_times\":"
		out.RawString(prefix)
		out.Int32(int32(in.CurEliteCardsRefreshTimes))
	}
	{
		const prefix string = ",\"cur_stage_start_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.CurStageStartTime))
	}
	{
		const prefix string = ",\"cur_normal_cards_refresh_times\":"
		out.RawString(prefix)
		out.Int32(int32(in.CurNormalCardsRefreshTimes))
	}
	{
		const prefix string = ",\"cur_stage_finish\":"
		out.RawString(prefix)
		out.Bool(bool(in.CurStageFinish))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v MainLineStage) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc30(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v MainLineStage) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc30(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *MainLineStage) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc30(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *MainLineStage) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc30(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc31(in *jlexer.Lexer, out *LordGemRandom) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "group_id":
			out.GroupId = int32(in.Int32())
		case "must_times":
			out.MustTimes = int32(in.Int32())
		case "last_refresh_time":
			out.LastRefreshTime = timestamp.UTCSeconds(in.Int64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "today_free_random_times":
			out.TodayFreeRandomTimes = int32(in.Int32())
		case "last_free_random_time":
			out.LastFreeRandomTime = timestamp.UTCSeconds(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc31(out *jwriter.Writer, in LordGemRandom) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"group_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.GroupId))
	}
	{
		const prefix string = ",\"must_times\":"
		out.RawString(prefix)
		out.Int32(int32(in.MustTimes))
	}
	{
		const prefix string = ",\"last_refresh_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.LastRefreshTime))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"today_free_random_times\":"
		out.RawString(prefix)
		out.Int32(int32(in.TodayFreeRandomTimes))
	}
	{
		const prefix string = ",\"last_free_random_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.LastFreeRandomTime))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v LordGemRandom) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc31(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v LordGemRandom) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc31(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *LordGemRandom) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc31(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *LordGemRandom) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc31(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc32(in *jlexer.Lexer, out *LordGem) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "config_id":
			out.ConfigId = int32(in.Int32())
		case "amount":
			out.Amount = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "is_lock":
			out.IsLock = bool(in.Bool())
		case "be_used":
			out.BeUsed = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc32(out *jwriter.Writer, in LordGem) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"config_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.ConfigId))
	}
	{
		const prefix string = ",\"amount\":"
		out.RawString(prefix)
		out.Int32(int32(in.Amount))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"is_lock\":"
		out.RawString(prefix)
		out.Bool(bool(in.IsLock))
	}
	{
		const prefix string = ",\"be_used\":"
		out.RawString(prefix)
		out.Int32(int32(in.BeUsed))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v LordGem) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc32(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v LordGem) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc32(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *LordGem) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc32(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *LordGem) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc32(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc33(in *jlexer.Lexer, out *LordEquip) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "type_id":
			out.TypeId = int32(in.Int32())
		case "config_id":
			out.ConfigId = int32(in.Int32())
		case "gene_info":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.GeneInfo = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v89 int32
					v89 = int32(in.Int32())
					(out.GeneInfo)[key] = v89
					in.WantComma()
				}
				in.Delim('}')
			}
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "grade_id":
			out.GradeId = int32(in.Int32())
		case "gene_info_list":
			if in.IsNull() {
				in.Skip()
				out.GeneInfoList = nil
			} else {
				in.Delim('[')
				if out.GeneInfoList == nil {
					if !in.IsDelim(']') {
						out.GeneInfoList = make([]*GeneInfoList, 0, 8)
					} else {
						out.GeneInfoList = []*GeneInfoList{}
					}
				} else {
					out.GeneInfoList = (out.GeneInfoList)[:0]
				}
				for !in.IsDelim(']') {
					var v90 *GeneInfoList
					if in.IsNull() {
						in.Skip()
						v90 = nil
					} else {
						if v90 == nil {
							v90 = new(GeneInfoList)
						}
						(*v90).UnmarshalEasyJSON(in)
					}
					out.GeneInfoList = append(out.GeneInfoList, v90)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "power":
			out.Power = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc33(out *jwriter.Writer, in LordEquip) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"type_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.TypeId))
	}
	{
		const prefix string = ",\"config_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.ConfigId))
	}
	{
		const prefix string = ",\"gene_info\":"
		out.RawString(prefix)
		if in.GeneInfo == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v91First := true
			for v91Name, v91Value := range in.GeneInfo {
				if v91First {
					v91First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v91Name))
				out.RawByte(':')
				out.Int32(int32(v91Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"grade_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.GradeId))
	}
	{
		const prefix string = ",\"gene_info_list\":"
		out.RawString(prefix)
		if in.GeneInfoList == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v92, v93 := range in.GeneInfoList {
				if v92 > 0 {
					out.RawByte(',')
				}
				if v93 == nil {
					out.RawString("null")
				} else {
					(*v93).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"power\":"
		out.RawString(prefix)
		out.Int32(int32(in.Power))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v LordEquip) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc33(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v LordEquip) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc33(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *LordEquip) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc33(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *LordEquip) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc33(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc34(in *jlexer.Lexer, out *Lord) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "level":
			out.Level = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc34(out *jwriter.Writer, in Lord) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"level\":"
		out.RawString(prefix)
		out.Int32(int32(in.Level))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v Lord) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc34(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v Lord) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc34(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *Lord) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc34(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *Lord) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc34(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc35(in *jlexer.Lexer, out *LevelStruct) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "card_ids":
			if in.IsNull() {
				in.Skip()
				out.CardIds = nil
			} else {
				in.Delim('[')
				if out.CardIds == nil {
					if !in.IsDelim(']') {
						out.CardIds = make([]int32, 0, 16)
					} else {
						out.CardIds = []int32{}
					}
				} else {
					out.CardIds = (out.CardIds)[:0]
				}
				for !in.IsDelim(']') {
					var v94 int32
					v94 = int32(in.Int32())
					out.CardIds = append(out.CardIds, v94)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "total_exp":
			out.TotalExp = int32(in.Int32())
		case "hero_hp":
			if in.IsNull() {
				in.Skip()
				out.HeroHp = nil
			} else {
				in.Delim('[')
				if out.HeroHp == nil {
					if !in.IsDelim(']') {
						out.HeroHp = make([]int32, 0, 16)
					} else {
						out.HeroHp = []int32{}
					}
				} else {
					out.HeroHp = (out.HeroHp)[:0]
				}
				for !in.IsDelim(']') {
					var v95 int32
					v95 = int32(in.Int32())
					out.HeroHp = append(out.HeroHp, v95)
					in.WantComma()
				}
				in.Delim(']')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc35(out *jwriter.Writer, in LevelStruct) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"card_ids\":"
		out.RawString(prefix[1:])
		if in.CardIds == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v96, v97 := range in.CardIds {
				if v96 > 0 {
					out.RawByte(',')
				}
				out.Int32(int32(v97))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"total_exp\":"
		out.RawString(prefix)
		out.Int32(int32(in.TotalExp))
	}
	{
		const prefix string = ",\"hero_hp\":"
		out.RawString(prefix)
		if in.HeroHp == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v98, v99 := range in.HeroHp {
				if v98 > 0 {
					out.RawByte(',')
				}
				out.Int32(int32(v99))
			}
			out.RawByte(']')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v LevelStruct) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc35(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v LevelStruct) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc35(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *LevelStruct) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc35(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *LevelStruct) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc35(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc36(in *jlexer.Lexer, out *HuatuoVersion) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "platform":
			out.Platform = string(in.String())
		case "client_version":
			out.ClientVersion = string(in.String())
		case "target_version":
			out.TargetVersion = string(in.String())
		case "data":
			out.Data = string(in.String())
		case "bundle_path_mc":
			out.BundlePathMc = string(in.String())
		case "bundle_cfg_mc":
			out.BundleCfgMc = string(in.String())
		case "bundle_path_gog":
			out.BundlePathGog = string(in.String())
		case "bundle_cfg_gog":
			out.BundleCfgGog = string(in.String())
		case "job_id":
			out.JobId = string(in.String())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc36(out *jwriter.Writer, in HuatuoVersion) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"platform\":"
		out.RawString(prefix)
		out.String(string(in.Platform))
	}
	{
		const prefix string = ",\"client_version\":"
		out.RawString(prefix)
		out.String(string(in.ClientVersion))
	}
	{
		const prefix string = ",\"target_version\":"
		out.RawString(prefix)
		out.String(string(in.TargetVersion))
	}
	{
		const prefix string = ",\"data\":"
		out.RawString(prefix)
		out.String(string(in.Data))
	}
	{
		const prefix string = ",\"bundle_path_mc\":"
		out.RawString(prefix)
		out.String(string(in.BundlePathMc))
	}
	{
		const prefix string = ",\"bundle_cfg_mc\":"
		out.RawString(prefix)
		out.String(string(in.BundleCfgMc))
	}
	{
		const prefix string = ",\"bundle_path_gog\":"
		out.RawString(prefix)
		out.String(string(in.BundlePathGog))
	}
	{
		const prefix string = ",\"bundle_cfg_gog\":"
		out.RawString(prefix)
		out.String(string(in.BundleCfgGog))
	}
	{
		const prefix string = ",\"job_id\":"
		out.RawString(prefix)
		out.String(string(in.JobId))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v HuatuoVersion) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc36(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v HuatuoVersion) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc36(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *HuatuoVersion) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc36(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *HuatuoVersion) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc36(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc37(in *jlexer.Lexer, out *HeroSkill) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "group_id":
			out.GroupId = int32(in.Int32())
		case "config_id":
			out.ConfigId = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "hero_id":
			out.HeroId = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc37(out *jwriter.Writer, in HeroSkill) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"group_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.GroupId))
	}
	{
		const prefix string = ",\"config_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.ConfigId))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"hero_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.HeroId))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v HeroSkill) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc37(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v HeroSkill) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc37(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *HeroSkill) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc37(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *HeroSkill) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc37(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc38(in *jlexer.Lexer, out *HeroLottery) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "level_id":
			out.LevelId = int32(in.Int32())
		case "sum_amount":
			out.SumAmount = int64(in.Int64())
		case "level_rewards":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.LevelRewards = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v100 int64
					v100 = int64(in.Int64())
					(out.LevelRewards)[key] = v100
					in.WantComma()
				}
				in.Delim('}')
			}
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "acc_amount":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.AccAmount = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v101 int32
					v101 = int32(in.Int32())
					(out.AccAmount)[key] = v101
					in.WantComma()
				}
				in.Delim('}')
			}
		case "acc_reward":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.AccReward = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v102 int64
					v102 = int64(in.Int64())
					(out.AccReward)[key] = v102
					in.WantComma()
				}
				in.Delim('}')
			}
		case "last_free_lottery_time":
			out.LastFreeLotteryTime = int64(in.Int64())
		case "today_free_lottery_times":
			out.TodayFreeLotteryTimes = int32(in.Int32())
		case "to_must_times":
			out.ToMustTimes = int32(in.Int32())
		case "not_in_tutorial":
			out.NotInTutorial = bool(in.Bool())
		case "not_in_first_second":
			out.NotInFirstSecond = bool(in.Bool())
		case "first_must_hero_id":
			out.FirstMustHeroId = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc38(out *jwriter.Writer, in HeroLottery) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"level_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.LevelId))
	}
	{
		const prefix string = ",\"sum_amount\":"
		out.RawString(prefix)
		out.Int64(int64(in.SumAmount))
	}
	{
		const prefix string = ",\"level_rewards\":"
		out.RawString(prefix)
		if in.LevelRewards == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v103First := true
			for v103Name, v103Value := range in.LevelRewards {
				if v103First {
					v103First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v103Name))
				out.RawByte(':')
				out.Int64(int64(v103Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"acc_amount\":"
		out.RawString(prefix)
		if in.AccAmount == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v104First := true
			for v104Name, v104Value := range in.AccAmount {
				if v104First {
					v104First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v104Name))
				out.RawByte(':')
				out.Int32(int32(v104Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"acc_reward\":"
		out.RawString(prefix)
		if in.AccReward == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v105First := true
			for v105Name, v105Value := range in.AccReward {
				if v105First {
					v105First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v105Name))
				out.RawByte(':')
				out.Int64(int64(v105Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"last_free_lottery_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.LastFreeLotteryTime))
	}
	{
		const prefix string = ",\"today_free_lottery_times\":"
		out.RawString(prefix)
		out.Int32(int32(in.TodayFreeLotteryTimes))
	}
	{
		const prefix string = ",\"to_must_times\":"
		out.RawString(prefix)
		out.Int32(int32(in.ToMustTimes))
	}
	{
		const prefix string = ",\"not_in_tutorial\":"
		out.RawString(prefix)
		out.Bool(bool(in.NotInTutorial))
	}
	{
		const prefix string = ",\"not_in_first_second\":"
		out.RawString(prefix)
		out.Bool(bool(in.NotInFirstSecond))
	}
	{
		const prefix string = ",\"first_must_hero_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.FirstMustHeroId))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v HeroLottery) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc38(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v HeroLottery) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc38(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *HeroLottery) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc38(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *HeroLottery) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc38(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc39(in *jlexer.Lexer, out *HeroInfo) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "config_id":
			out.ConfigId = int32(in.Int32())
		case "exp":
			out.Exp = int64(in.Int64())
		case "starId":
			out.StarId = int32(in.Int32())
		case "levelId":
			out.LevelId = int32(in.Int32())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "benefits":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Benefits = make(map[int32]float32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v106 float32
					v106 = float32(in.Float32())
					(out.Benefits)[key] = v106
					in.WantComma()
				}
				in.Delim('}')
			}
		case "equipment_level":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.EquipmentLevel = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v107 int32
					v107 = int32(in.Int32())
					(out.EquipmentLevel)[key] = v107
					in.WantComma()
				}
				in.Delim('}')
			}
		case "equipment_grade":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.EquipmentGrade = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v108 int32
					v108 = int32(in.Int32())
					(out.EquipmentGrade)[key] = v108
					in.WantComma()
				}
				in.Delim('}')
			}
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "hero_dice":
			if in.IsNull() {
				in.Skip()
				out.HeroDice = nil
			} else {
				in.Delim('[')
				if out.HeroDice == nil {
					if !in.IsDelim(']') {
						out.HeroDice = make([]*HeroDice, 0, 8)
					} else {
						out.HeroDice = []*HeroDice{}
					}
				} else {
					out.HeroDice = (out.HeroDice)[:0]
				}
				for !in.IsDelim(']') {
					var v109 *HeroDice
					if in.IsNull() {
						in.Skip()
						v109 = nil
					} else {
						if v109 == nil {
							v109 = new(HeroDice)
						}
						(*v109).UnmarshalEasyJSON(in)
					}
					out.HeroDice = append(out.HeroDice, v109)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "hero_equipment":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.HeroEquipment = make(map[int32]*HeroEquipment)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v110 *HeroEquipment
					if in.IsNull() {
						in.Skip()
						v110 = nil
					} else {
						if v110 == nil {
							v110 = new(HeroEquipment)
						}
						(*v110).UnmarshalEasyJSON(in)
					}
					(out.HeroEquipment)[key] = v110
					in.WantComma()
				}
				in.Delim('}')
			}
		case "troop_num":
			out.TroopNum = int64(in.Int64())
		case "power":
			out.Power = int64(in.Int64())
		case "is_battle":
			out.IsBattle = bool(in.Bool())
		case "gene_config_id":
			out.GeneConfigId = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc39(out *jwriter.Writer, in HeroInfo) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"config_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.ConfigId))
	}
	{
		const prefix string = ",\"exp\":"
		out.RawString(prefix)
		out.Int64(int64(in.Exp))
	}
	{
		const prefix string = ",\"starId\":"
		out.RawString(prefix)
		out.Int32(int32(in.StarId))
	}
	{
		const prefix string = ",\"levelId\":"
		out.RawString(prefix)
		out.Int32(int32(in.LevelId))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"benefits\":"
		out.RawString(prefix)
		if in.Benefits == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v111First := true
			for v111Name, v111Value := range in.Benefits {
				if v111First {
					v111First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v111Name))
				out.RawByte(':')
				out.Float32(float32(v111Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"equipment_level\":"
		out.RawString(prefix)
		if in.EquipmentLevel == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v112First := true
			for v112Name, v112Value := range in.EquipmentLevel {
				if v112First {
					v112First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v112Name))
				out.RawByte(':')
				out.Int32(int32(v112Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"equipment_grade\":"
		out.RawString(prefix)
		if in.EquipmentGrade == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v113First := true
			for v113Name, v113Value := range in.EquipmentGrade {
				if v113First {
					v113First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v113Name))
				out.RawByte(':')
				out.Int32(int32(v113Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"hero_dice\":"
		out.RawString(prefix)
		if in.HeroDice == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v114, v115 := range in.HeroDice {
				if v114 > 0 {
					out.RawByte(',')
				}
				if v115 == nil {
					out.RawString("null")
				} else {
					(*v115).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"hero_equipment\":"
		out.RawString(prefix)
		if in.HeroEquipment == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v116First := true
			for v116Name, v116Value := range in.HeroEquipment {
				if v116First {
					v116First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v116Name))
				out.RawByte(':')
				if v116Value == nil {
					out.RawString("null")
				} else {
					(*v116Value).MarshalEasyJSON(out)
				}
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"troop_num\":"
		out.RawString(prefix)
		out.Int64(int64(in.TroopNum))
	}
	{
		const prefix string = ",\"power\":"
		out.RawString(prefix)
		out.Int64(int64(in.Power))
	}
	{
		const prefix string = ",\"is_battle\":"
		out.RawString(prefix)
		out.Bool(bool(in.IsBattle))
	}
	{
		const prefix string = ",\"gene_config_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.GeneConfigId))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v HeroInfo) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc39(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v HeroInfo) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc39(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *HeroInfo) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc39(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *HeroInfo) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc39(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc40(in *jlexer.Lexer, out *HeroEquipment) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "pos":
			out.Pos = int32(in.Int32())
		case "level_id":
			out.LevelId = int32(in.Int32())
		case "grade_id":
			out.GradeId = int32(in.Int32())
		case "lock":
			out.Lock = bool(in.Bool())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc40(out *jwriter.Writer, in HeroEquipment) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"pos\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.Pos))
	}
	{
		const prefix string = ",\"level_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.LevelId))
	}
	{
		const prefix string = ",\"grade_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.GradeId))
	}
	{
		const prefix string = ",\"lock\":"
		out.RawString(prefix)
		out.Bool(bool(in.Lock))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v HeroEquipment) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc40(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v HeroEquipment) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc40(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *HeroEquipment) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc40(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *HeroEquipment) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc40(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc41(in *jlexer.Lexer, out *HeroDice) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "dice_id":
			out.DiceId = int32(in.Int32())
		case "lock":
			out.Lock = bool(in.Bool())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc41(out *jwriter.Writer, in HeroDice) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"dice_id\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.DiceId))
	}
	{
		const prefix string = ",\"lock\":"
		out.RawString(prefix)
		out.Bool(bool(in.Lock))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v HeroDice) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc41(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v HeroDice) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc41(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *HeroDice) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc41(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *HeroDice) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc41(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc42(in *jlexer.Lexer, out *GrowthTask) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "quest_id":
			out.QuestId = int32(in.Int32())
		case "progress":
			out.Progress = int64(in.Int64())
		case "status":
			out.Status = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "group_id":
			out.GroupId = int32(in.Int32())
		case "type":
			out.Type = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc42(out *jwriter.Writer, in GrowthTask) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"quest_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.QuestId))
	}
	{
		const prefix string = ",\"progress\":"
		out.RawString(prefix)
		out.Int64(int64(in.Progress))
	}
	{
		const prefix string = ",\"status\":"
		out.RawString(prefix)
		out.Int32(int32(in.Status))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"group_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.GroupId))
	}
	{
		const prefix string = ",\"type\":"
		out.RawString(prefix)
		out.Int32(int32(in.Type))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v GrowthTask) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc42(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v GrowthTask) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc42(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *GrowthTask) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc42(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *GrowthTask) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc42(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc43(in *jlexer.Lexer, out *GrowthFund) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "fund_id":
			out.FundId = int32(in.Int32())
		case "buy_times":
			out.BuyTimes = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "normal_reward_status":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.NormalRewardStatus = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v117 int32
					v117 = int32(in.Int32())
					(out.NormalRewardStatus)[key] = v117
					in.WantComma()
				}
				in.Delim('}')
			}
		case "vip_reward_status":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.VipRewardStatus = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v118 int32
					v118 = int32(in.Int32())
					(out.VipRewardStatus)[key] = v118
					in.WantComma()
				}
				in.Delim('}')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc43(out *jwriter.Writer, in GrowthFund) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"fund_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.FundId))
	}
	{
		const prefix string = ",\"buy_times\":"
		out.RawString(prefix)
		out.Int32(int32(in.BuyTimes))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"normal_reward_status\":"
		out.RawString(prefix)
		if in.NormalRewardStatus == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v119First := true
			for v119Name, v119Value := range in.NormalRewardStatus {
				if v119First {
					v119First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v119Name))
				out.RawByte(':')
				out.Int32(int32(v119Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"vip_reward_status\":"
		out.RawString(prefix)
		if in.VipRewardStatus == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v120First := true
			for v120Name, v120Value := range in.VipRewardStatus {
				if v120First {
					v120First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v120Name))
				out.RawByte(':')
				out.Int32(int32(v120Value))
			}
			out.RawByte('}')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v GrowthFund) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc43(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v GrowthFund) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc43(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *GrowthFund) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc43(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *GrowthFund) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc43(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc44(in *jlexer.Lexer, out *GlobalStagePerLevel) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "player_score":
			if in.IsNull() {
				in.Skip()
				out.PlayerScore = nil
			} else {
				in.Delim('[')
				if out.PlayerScore == nil {
					if !in.IsDelim(']') {
						out.PlayerScore = make([]*PerPlayerScore, 0, 8)
					} else {
						out.PlayerScore = []*PerPlayerScore{}
					}
				} else {
					out.PlayerScore = (out.PlayerScore)[:0]
				}
				for !in.IsDelim(']') {
					var v121 *PerPlayerScore
					if in.IsNull() {
						in.Skip()
						v121 = nil
					} else {
						if v121 == nil {
							v121 = new(PerPlayerScore)
						}
						(*v121).UnmarshalEasyJSON(in)
					}
					out.PlayerScore = append(out.PlayerScore, v121)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "max_time":
			out.MaxTime = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc44(out *jwriter.Writer, in GlobalStagePerLevel) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"player_score\":"
		out.RawString(prefix[1:])
		if in.PlayerScore == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v122, v123 := range in.PlayerScore {
				if v122 > 0 {
					out.RawByte(',')
				}
				if v123 == nil {
					out.RawString("null")
				} else {
					(*v123).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"max_time\":"
		out.RawString(prefix)
		out.Int32(int32(in.MaxTime))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v GlobalStagePerLevel) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc44(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v GlobalStagePerLevel) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc44(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *GlobalStagePerLevel) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc44(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *GlobalStagePerLevel) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc44(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc45(in *jlexer.Lexer, out *GlobalStageLevel) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "world_id":
			out.WorldId = int32(in.Int32())
		case "level_info":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.LevelInfo = make(map[int32]*GlobalStagePerLevel)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v124 *GlobalStagePerLevel
					if in.IsNull() {
						in.Skip()
						v124 = nil
					} else {
						if v124 == nil {
							v124 = new(GlobalStagePerLevel)
						}
						(*v124).UnmarshalEasyJSON(in)
					}
					(out.LevelInfo)[key] = v124
					in.WantComma()
				}
				in.Delim('}')
			}
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc45(out *jwriter.Writer, in GlobalStageLevel) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"world_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.WorldId))
	}
	{
		const prefix string = ",\"level_info\":"
		out.RawString(prefix)
		if in.LevelInfo == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v125First := true
			for v125Name, v125Value := range in.LevelInfo {
				if v125First {
					v125First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v125Name))
				out.RawByte(':')
				if v125Value == nil {
					out.RawString("null")
				} else {
					(*v125Value).MarshalEasyJSON(out)
				}
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v GlobalStageLevel) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc45(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v GlobalStageLevel) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc45(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *GlobalStageLevel) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc45(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *GlobalStageLevel) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc45(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc46(in *jlexer.Lexer, out *GeneInfoList) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "gene_info":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.GeneInfo = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v126 int32
					v126 = int32(in.Int32())
					(out.GeneInfo)[key] = v126
					in.WantComma()
				}
				in.Delim('}')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc46(out *jwriter.Writer, in GeneInfoList) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"gene_info\":"
		out.RawString(prefix[1:])
		if in.GeneInfo == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v127First := true
			for v127Name, v127Value := range in.GeneInfo {
				if v127First {
					v127First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v127Name))
				out.RawByte(':')
				out.Int32(int32(v127Value))
			}
			out.RawByte('}')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v GeneInfoList) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc46(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v GeneInfoList) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc46(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *GeneInfoList) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc46(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *GeneInfoList) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc46(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc47(in *jlexer.Lexer, out *FunctionOpen) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "open_list":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.OpenList = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v128 int32
					v128 = int32(in.Int32())
					(out.OpenList)[key] = v128
					in.WantComma()
				}
				in.Delim('}')
			}
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc47(out *jwriter.Writer, in FunctionOpen) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"open_list\":"
		out.RawString(prefix)
		if in.OpenList == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v129First := true
			for v129Name, v129Value := range in.OpenList {
				if v129First {
					v129First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v129Name))
				out.RawByte(':')
				out.Int32(int32(v129Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v FunctionOpen) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc47(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v FunctionOpen) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc47(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *FunctionOpen) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc47(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *FunctionOpen) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc47(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc48(in *jlexer.Lexer, out *FirstCharge) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "charge_id":
			out.ChargeId = int32(in.Int32())
		case "collect_status":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.CollectStatus = make(map[int32]bool)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v130 bool
					v130 = bool(in.Bool())
					(out.CollectStatus)[key] = v130
					in.WantComma()
				}
				in.Delim('}')
			}
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "first_charge_time":
			out.FirstChargeTime = timestamp.UTCSeconds(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc48(out *jwriter.Writer, in FirstCharge) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"charge_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.ChargeId))
	}
	{
		const prefix string = ",\"collect_status\":"
		out.RawString(prefix)
		if in.CollectStatus == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v131First := true
			for v131Name, v131Value := range in.CollectStatus {
				if v131First {
					v131First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v131Name))
				out.RawByte(':')
				out.Bool(bool(v131Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"first_charge_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.FirstChargeTime))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v FirstCharge) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc48(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v FirstCharge) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc48(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *FirstCharge) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc48(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *FirstCharge) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc48(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc49(in *jlexer.Lexer, out *EntityKey) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "type":
			out.Type = int32(in.Int32())
		case "world_id":
			out.WorldId = int32(in.Int32())
		case "uid":
			out.Uid = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc49(out *jwriter.Writer, in EntityKey) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"type\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.Type))
	}
	{
		const prefix string = ",\"world_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.WorldId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v EntityKey) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc49(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v EntityKey) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc49(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *EntityKey) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc49(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *EntityKey) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc49(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc50(in *jlexer.Lexer, out *Dungeon) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "dungeon_type":
			out.DungeonType = int32(in.Int32())
		case "max_level":
			out.MaxLevel = int32(in.Int32())
		case "today_reset_times":
			out.TodayResetTimes = int32(in.Int32())
		case "today_ad_times":
			out.TodayAdTimes = int32(in.Int32())
		case "last_reset_time":
			out.LastResetTime = timestamp.UTCSeconds(in.Int64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "level_rewards":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.LevelRewards = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v132 int64
					v132 = int64(in.Int64())
					(out.LevelRewards)[key] = v132
					in.WantComma()
				}
				in.Delim('}')
			}
		case "cur_stage_id":
			out.CurStageId = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc50(out *jwriter.Writer, in Dungeon) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"dungeon_type\":"
		out.RawString(prefix)
		out.Int32(int32(in.DungeonType))
	}
	{
		const prefix string = ",\"max_level\":"
		out.RawString(prefix)
		out.Int32(int32(in.MaxLevel))
	}
	{
		const prefix string = ",\"today_reset_times\":"
		out.RawString(prefix)
		out.Int32(int32(in.TodayResetTimes))
	}
	{
		const prefix string = ",\"today_ad_times\":"
		out.RawString(prefix)
		out.Int32(int32(in.TodayAdTimes))
	}
	{
		const prefix string = ",\"last_reset_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.LastResetTime))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"level_rewards\":"
		out.RawString(prefix)
		if in.LevelRewards == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v133First := true
			for v133Name, v133Value := range in.LevelRewards {
				if v133First {
					v133First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v133Name))
				out.RawByte(':')
				out.Int64(int64(v133Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"cur_stage_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.CurStageId))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v Dungeon) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc50(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v Dungeon) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc50(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *Dungeon) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc50(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *Dungeon) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc50(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc51(in *jlexer.Lexer, out *Dave) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "level_id":
			out.LevelId = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc51(out *jwriter.Writer, in Dave) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"level_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.LevelId))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v Dave) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc51(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v Dave) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc51(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *Dave) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc51(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *Dave) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc51(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc52(in *jlexer.Lexer, out *DailyTask) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "quest_id":
			out.QuestId = int32(in.Int32())
		case "type":
			out.Type = int32(in.Int32())
		case "progress":
			out.Progress = int64(in.Int64())
		case "status":
			out.Status = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "_id":
			out.XId = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc52(out *jwriter.Writer, in DailyTask) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"quest_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.QuestId))
	}
	{
		const prefix string = ",\"type\":"
		out.RawString(prefix)
		out.Int32(int32(in.Type))
	}
	{
		const prefix string = ",\"progress\":"
		out.RawString(prefix)
		out.Int64(int64(in.Progress))
	}
	{
		const prefix string = ",\"status\":"
		out.RawString(prefix)
		out.Int32(int32(in.Status))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix)
		out.String(string(in.XId))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v DailyTask) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc52(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v DailyTask) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc52(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *DailyTask) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc52(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *DailyTask) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc52(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc53(in *jlexer.Lexer, out *CurStageInfo) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "stage_id":
			out.StageId = int32(in.Int32())
		case "duration":
			out.Duration = int64(in.Int64())
		case "hp_percent":
			out.HpPercent = float32(in.Float32())
		case "finish_status":
			out.FinishStatus = int32(in.Int32())
		case "finish_status_reward":
			if in.IsNull() {
				in.Skip()
				out.FinishStatusReward = nil
			} else {
				in.Delim('[')
				if out.FinishStatusReward == nil {
					if !in.IsDelim(']') {
						out.FinishStatusReward = make([]int32, 0, 16)
					} else {
						out.FinishStatusReward = []int32{}
					}
				} else {
					out.FinishStatusReward = (out.FinishStatusReward)[:0]
				}
				for !in.IsDelim(']') {
					var v134 int32
					v134 = int32(in.Int32())
					out.FinishStatusReward = append(out.FinishStatusReward, v134)
					in.WantComma()
				}
				in.Delim(']')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc53(out *jwriter.Writer, in CurStageInfo) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"stage_id\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.StageId))
	}
	{
		const prefix string = ",\"duration\":"
		out.RawString(prefix)
		out.Int64(int64(in.Duration))
	}
	{
		const prefix string = ",\"hp_percent\":"
		out.RawString(prefix)
		out.Float32(float32(in.HpPercent))
	}
	{
		const prefix string = ",\"finish_status\":"
		out.RawString(prefix)
		out.Int32(int32(in.FinishStatus))
	}
	{
		const prefix string = ",\"finish_status_reward\":"
		out.RawString(prefix)
		if in.FinishStatusReward == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v135, v136 := range in.FinishStatusReward {
				if v135 > 0 {
					out.RawByte(',')
				}
				out.Int32(int32(v136))
			}
			out.RawByte(']')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v CurStageInfo) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc53(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v CurStageInfo) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc53(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *CurStageInfo) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc53(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *CurStageInfo) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc53(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc54(in *jlexer.Lexer, out *Consumable) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "config_id":
			out.ConfigId = int32(in.Int32())
		case "quantity":
			out.Quantity = int64(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc54(out *jwriter.Writer, in Consumable) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"config_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.ConfigId))
	}
	{
		const prefix string = ",\"quantity\":"
		out.RawString(prefix)
		out.Int64(int64(in.Quantity))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v Consumable) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc54(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v Consumable) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc54(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *Consumable) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc54(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *Consumable) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc54(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc55(in *jlexer.Lexer, out *ClientVersion) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "platform":
			out.Platform = string(in.String())
		case "client_version":
			out.ClientVersion = string(in.String())
		case "bundle_version_r":
			out.BundleVersionR = string(in.String())
		case "bundle_version_g":
			out.BundleVersionG = string(in.String())
		case "bundle_md5":
			out.BundleMd5 = string(in.String())
		case "preload_md5":
			out.PreloadMd5 = string(in.String())
		case "config_md5":
			out.ConfigMd5 = string(in.String())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc55(out *jwriter.Writer, in ClientVersion) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"platform\":"
		out.RawString(prefix)
		out.String(string(in.Platform))
	}
	{
		const prefix string = ",\"client_version\":"
		out.RawString(prefix)
		out.String(string(in.ClientVersion))
	}
	{
		const prefix string = ",\"bundle_version_r\":"
		out.RawString(prefix)
		out.String(string(in.BundleVersionR))
	}
	{
		const prefix string = ",\"bundle_version_g\":"
		out.RawString(prefix)
		out.String(string(in.BundleVersionG))
	}
	{
		const prefix string = ",\"bundle_md5\":"
		out.RawString(prefix)
		out.String(string(in.BundleMd5))
	}
	{
		const prefix string = ",\"preload_md5\":"
		out.RawString(prefix)
		out.String(string(in.PreloadMd5))
	}
	{
		const prefix string = ",\"config_md5\":"
		out.RawString(prefix)
		out.String(string(in.ConfigMd5))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v ClientVersion) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc55(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v ClientVersion) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc55(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *ClientVersion) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc55(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *ClientVersion) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc55(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc56(in *jlexer.Lexer, out *BuildingInfo) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "building_id":
			out.BuildingId = int64(in.Int64())
		case "config_id":
			out.ConfigId = int32(in.Int32())
		case "job_id":
			out.JobId = int64(in.Int64())
		case "type":
			out.Type = int32(in.Int32())
		case "work_hero":
			if in.IsNull() {
				in.Skip()
				out.WorkHero = nil
			} else {
				in.Delim('[')
				if out.WorkHero == nil {
					if !in.IsDelim(']') {
						out.WorkHero = make([]int32, 0, 16)
					} else {
						out.WorkHero = []int32{}
					}
				} else {
					out.WorkHero = (out.WorkHero)[:0]
				}
				for !in.IsDelim(']') {
					var v137 int32
					v137 = int32(in.Int32())
					out.WorkHero = append(out.WorkHero, v137)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "_version":
			out.XVersion = int64(in.Int64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "ext_data":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.ExtData = make(map[int64]int64)
				for !in.IsDelim('}') {
					key := int64(in.Int64Str())
					in.WantColon()
					var v138 int64
					v138 = int64(in.Int64())
					(out.ExtData)[key] = v138
					in.WantComma()
				}
				in.Delim('}')
			}
		case "train_job_id":
			out.TrainJobId = int64(in.Int64())
		case "work_village":
			if in.IsNull() {
				in.Skip()
				out.WorkVillage = nil
			} else {
				in.Delim('[')
				if out.WorkVillage == nil {
					if !in.IsDelim(']') {
						out.WorkVillage = make([]int32, 0, 16)
					} else {
						out.WorkVillage = []int32{}
					}
				} else {
					out.WorkVillage = (out.WorkVillage)[:0]
				}
				for !in.IsDelim(']') {
					var v139 int32
					v139 = int32(in.Int32())
					out.WorkVillage = append(out.WorkVillage, v139)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "status":
			out.Status = int32(in.Int32())
		case "end_time":
			out.EndTime = timestamp.UTCSeconds(in.Int64())
		case "trace":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Trace = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v140 int64
					v140 = int64(in.Int64())
					(out.Trace)[key] = v140
					in.WantComma()
				}
				in.Delim('}')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc56(out *jwriter.Writer, in BuildingInfo) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"building_id\":"
		out.RawString(prefix)
		out.Int64(int64(in.BuildingId))
	}
	{
		const prefix string = ",\"config_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.ConfigId))
	}
	{
		const prefix string = ",\"job_id\":"
		out.RawString(prefix)
		out.Int64(int64(in.JobId))
	}
	{
		const prefix string = ",\"type\":"
		out.RawString(prefix)
		out.Int32(int32(in.Type))
	}
	{
		const prefix string = ",\"work_hero\":"
		out.RawString(prefix)
		if in.WorkHero == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v141, v142 := range in.WorkHero {
				if v141 > 0 {
					out.RawByte(',')
				}
				out.Int32(int32(v142))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"ext_data\":"
		out.RawString(prefix)
		if in.ExtData == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v143First := true
			for v143Name, v143Value := range in.ExtData {
				if v143First {
					v143First = false
				} else {
					out.RawByte(',')
				}
				out.Int64Str(int64(v143Name))
				out.RawByte(':')
				out.Int64(int64(v143Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"train_job_id\":"
		out.RawString(prefix)
		out.Int64(int64(in.TrainJobId))
	}
	{
		const prefix string = ",\"work_village\":"
		out.RawString(prefix)
		if in.WorkVillage == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v144, v145 := range in.WorkVillage {
				if v144 > 0 {
					out.RawByte(',')
				}
				out.Int32(int32(v145))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"status\":"
		out.RawString(prefix)
		out.Int32(int32(in.Status))
	}
	{
		const prefix string = ",\"end_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.EndTime))
	}
	{
		const prefix string = ",\"trace\":"
		out.RawString(prefix)
		if in.Trace == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v146First := true
			for v146Name, v146Value := range in.Trace {
				if v146First {
					v146First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v146Name))
				out.RawByte(':')
				out.Int64(int64(v146Value))
			}
			out.RawByte('}')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v BuildingInfo) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc56(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v BuildingInfo) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc56(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *BuildingInfo) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc56(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *BuildingInfo) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc56(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc57(in *jlexer.Lexer, out *BuffInfo) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "buff_id":
			out.BuffId = int32(in.Int32())
		case "buff_end_time":
			out.BuffEndTime = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc57(out *jwriter.Writer, in BuffInfo) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"buff_id\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.BuffId))
	}
	{
		const prefix string = ",\"buff_end_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.BuffEndTime))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v BuffInfo) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc57(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v BuffInfo) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc57(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *BuffInfo) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc57(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *BuffInfo) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc57(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc58(in *jlexer.Lexer, out *BattlePosInfo) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "heroId":
			out.HeroId = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc58(out *jwriter.Writer, in BattlePosInfo) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"heroId\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.HeroId))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v BattlePosInfo) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc58(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v BattlePosInfo) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc58(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *BattlePosInfo) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc58(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *BattlePosInfo) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc58(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc59(in *jlexer.Lexer, out *ArenaRankInfo) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "world_id":
			out.WorldId = int32(in.Int32())
		case "rank_id":
			out.RankId = int32(in.Int32())
		case "entity_id":
			out.EntityId = int64(in.Int64())
		case "is_robot":
			out.IsRobot = bool(in.Bool())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc59(out *jwriter.Writer, in ArenaRankInfo) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"world_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.WorldId))
	}
	{
		const prefix string = ",\"rank_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.RankId))
	}
	{
		const prefix string = ",\"entity_id\":"
		out.RawString(prefix)
		out.Int64(int64(in.EntityId))
	}
	{
		const prefix string = ",\"is_robot\":"
		out.RawString(prefix)
		out.Bool(bool(in.IsRobot))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v ArenaRankInfo) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc59(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v ArenaRankInfo) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc59(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *ArenaRankInfo) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc59(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *ArenaRankInfo) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc59(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc60(in *jlexer.Lexer, out *Animal) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "config_id":
			out.ConfigId = int32(in.Int32())
		case "work_building":
			out.WorkBuilding = int64(in.Int64())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc60(out *jwriter.Writer, in Animal) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"config_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.ConfigId))
	}
	{
		const prefix string = ",\"work_building\":"
		out.RawString(prefix)
		out.Int64(int64(in.WorkBuilding))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v Animal) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc60(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v Animal) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc60(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *Animal) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc60(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *Animal) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc60(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc61(in *jlexer.Lexer, out *AllianceTask) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "quest_id":
			out.QuestId = int32(in.Int32())
		case "type":
			out.Type = int32(in.Int32())
		case "progress":
			out.Progress = int64(in.Int64())
		case "status":
			out.Status = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "_id":
			out.XId = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc61(out *jwriter.Writer, in AllianceTask) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"quest_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.QuestId))
	}
	{
		const prefix string = ",\"type\":"
		out.RawString(prefix)
		out.Int32(int32(in.Type))
	}
	{
		const prefix string = ",\"progress\":"
		out.RawString(prefix)
		out.Int64(int64(in.Progress))
	}
	{
		const prefix string = ",\"status\":"
		out.RawString(prefix)
		out.Int32(int32(in.Status))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix)
		out.String(string(in.XId))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v AllianceTask) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc61(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v AllianceTask) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc61(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *AllianceTask) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc61(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *AllianceTask) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc61(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc62(in *jlexer.Lexer, out *AllianceShopBuy) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "alliance_id":
			out.AllianceId = int64(in.Int64())
		case "uid":
			out.Uid = int64(in.Int64())
		case "commodity_id":
			out.CommodityId = int32(in.Int32())
		case "buy_times":
			out.BuyTimes = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc62(out *jwriter.Writer, in AllianceShopBuy) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"alliance_id\":"
		out.RawString(prefix)
		out.Int64(int64(in.AllianceId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"commodity_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.CommodityId))
	}
	{
		const prefix string = ",\"buy_times\":"
		out.RawString(prefix)
		out.Int32(int32(in.BuyTimes))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v AllianceShopBuy) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc62(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v AllianceShopBuy) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc62(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *AllianceShopBuy) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc62(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *AllianceShopBuy) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc62(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc63(in *jlexer.Lexer, out *AllianceMember) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "alliance_id":
			out.AllianceId = int64(in.Int64())
		case "uid":
			out.Uid = int64(in.Int64())
		case "step":
			out.Step = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc63(out *jwriter.Writer, in AllianceMember) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"alliance_id\":"
		out.RawString(prefix)
		out.Int64(int64(in.AllianceId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"step\":"
		out.RawString(prefix)
		out.Int32(int32(in.Step))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v AllianceMember) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc63(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v AllianceMember) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc63(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *AllianceMember) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc63(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *AllianceMember) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc63(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc64(in *jlexer.Lexer, out *Alliance) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "alliance_id":
			out.AllianceId = int64(in.Int64())
		case "world_id":
			out.WorldId = int32(in.Int32())
		case "name":
			out.Name = string(in.String())
		case "acronym":
			out.Acronym = string(in.String())
		case "flag_base":
			out.FlagBase = int32(in.Int32())
		case "boss_uid":
			out.BossUid = int64(in.Int64())
		case "level":
			out.Level = int32(in.Int32())
		case "notice":
			out.Notice = string(in.String())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "application_list":
			if in.IsNull() {
				in.Skip()
				out.ApplicationList = nil
			} else {
				in.Delim('[')
				if out.ApplicationList == nil {
					if !in.IsDelim(']') {
						out.ApplicationList = make([]int64, 0, 8)
					} else {
						out.ApplicationList = []int64{}
					}
				} else {
					out.ApplicationList = (out.ApplicationList)[:0]
				}
				for !in.IsDelim(']') {
					var v147 int64
					v147 = int64(in.Int64())
					out.ApplicationList = append(out.ApplicationList, v147)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "recruit_setting":
			out.RecruitSetting = int32(in.Int32())
		case "flag_emblem":
			out.FlagEmblem = int32(in.Int32())
		case "step_name":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.StepName = make(map[int32]string)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v148 string
					v148 = string(in.String())
					(out.StepName)[key] = v148
					in.WantComma()
				}
				in.Delim('}')
			}
		case "free_change_name_times":
			out.FreeChangeNameTimes = int32(in.Int32())
		case "free_change_acronym_times":
			out.FreeChangeAcronymTimes = int32(in.Int32())
		case "power_condition":
			out.PowerCondition = int32(in.Int32())
		case "max_stage_condition":
			out.MaxStageCondition = int32(in.Int32())
		case "exp":
			out.Exp = int32(in.Int32())
		case "power":
			out.Power = int64(in.Int64())
		case "member_amount":
			out.MemberAmount = int32(in.Int32())
		case "shop_last_reset_time":
			out.ShopLastResetTime = timestamp.UTCSeconds(in.Int64())
		case "cur_shop_period":
			out.CurShopPeriod = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc64(out *jwriter.Writer, in Alliance) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"alliance_id\":"
		out.RawString(prefix)
		out.Int64(int64(in.AllianceId))
	}
	{
		const prefix string = ",\"world_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.WorldId))
	}
	{
		const prefix string = ",\"name\":"
		out.RawString(prefix)
		out.String(string(in.Name))
	}
	{
		const prefix string = ",\"acronym\":"
		out.RawString(prefix)
		out.String(string(in.Acronym))
	}
	{
		const prefix string = ",\"flag_base\":"
		out.RawString(prefix)
		out.Int32(int32(in.FlagBase))
	}
	{
		const prefix string = ",\"boss_uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.BossUid))
	}
	{
		const prefix string = ",\"level\":"
		out.RawString(prefix)
		out.Int32(int32(in.Level))
	}
	{
		const prefix string = ",\"notice\":"
		out.RawString(prefix)
		out.String(string(in.Notice))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"application_list\":"
		out.RawString(prefix)
		if in.ApplicationList == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v149, v150 := range in.ApplicationList {
				if v149 > 0 {
					out.RawByte(',')
				}
				out.Int64(int64(v150))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"recruit_setting\":"
		out.RawString(prefix)
		out.Int32(int32(in.RecruitSetting))
	}
	{
		const prefix string = ",\"flag_emblem\":"
		out.RawString(prefix)
		out.Int32(int32(in.FlagEmblem))
	}
	{
		const prefix string = ",\"step_name\":"
		out.RawString(prefix)
		if in.StepName == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v151First := true
			for v151Name, v151Value := range in.StepName {
				if v151First {
					v151First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v151Name))
				out.RawByte(':')
				out.String(string(v151Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"free_change_name_times\":"
		out.RawString(prefix)
		out.Int32(int32(in.FreeChangeNameTimes))
	}
	{
		const prefix string = ",\"free_change_acronym_times\":"
		out.RawString(prefix)
		out.Int32(int32(in.FreeChangeAcronymTimes))
	}
	{
		const prefix string = ",\"power_condition\":"
		out.RawString(prefix)
		out.Int32(int32(in.PowerCondition))
	}
	{
		const prefix string = ",\"max_stage_condition\":"
		out.RawString(prefix)
		out.Int32(int32(in.MaxStageCondition))
	}
	{
		const prefix string = ",\"exp\":"
		out.RawString(prefix)
		out.Int32(int32(in.Exp))
	}
	{
		const prefix string = ",\"power\":"
		out.RawString(prefix)
		out.Int64(int64(in.Power))
	}
	{
		const prefix string = ",\"member_amount\":"
		out.RawString(prefix)
		out.Int32(int32(in.MemberAmount))
	}
	{
		const prefix string = ",\"shop_last_reset_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.ShopLastResetTime))
	}
	{
		const prefix string = ",\"cur_shop_period\":"
		out.RawString(prefix)
		out.Int32(int32(in.CurShopPeriod))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v Alliance) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc64(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v Alliance) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc64(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *Alliance) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc64(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *Alliance) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc64(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc65(in *jlexer.Lexer, out *AgreeJoinAlliance) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "alliance_id":
			out.AllianceId = int64(in.Int64())
		case "name":
			out.Name = string(in.String())
		case "acronym":
			out.Acronym = string(in.String())
		case "isAgree":
			out.IsAgree = bool(in.Bool())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc65(out *jwriter.Writer, in AgreeJoinAlliance) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"alliance_id\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.AllianceId))
	}
	{
		const prefix string = ",\"name\":"
		out.RawString(prefix)
		out.String(string(in.Name))
	}
	{
		const prefix string = ",\"acronym\":"
		out.RawString(prefix)
		out.String(string(in.Acronym))
	}
	{
		const prefix string = ",\"isAgree\":"
		out.RawString(prefix)
		out.Bool(bool(in.IsAgree))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v AgreeJoinAlliance) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc65(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v AgreeJoinAlliance) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc65(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *AgreeJoinAlliance) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc65(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *AgreeJoinAlliance) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc65(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc66(in *jlexer.Lexer, out *AddFriendInfo) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "isAgree":
			out.IsAgree = bool(in.Bool())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc66(out *jwriter.Writer, in AddFriendInfo) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"isAgree\":"
		out.RawString(prefix)
		out.Bool(bool(in.IsAgree))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v AddFriendInfo) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc66(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v AddFriendInfo) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc66(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *AddFriendInfo) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc66(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *AddFriendInfo) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc66(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc67(in *jlexer.Lexer, out *ActivityTask) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "quest_id":
			out.QuestId = int32(in.Int32())
		case "progress":
			out.Progress = int64(in.Int64())
		case "status":
			out.Status = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "type":
			out.Type = int32(in.Int32())
		case "activity_id":
			out.ActivityId = int32(in.Int32())
		case "activity_type":
			out.ActivityType = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc67(out *jwriter.Writer, in ActivityTask) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"quest_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.QuestId))
	}
	{
		const prefix string = ",\"progress\":"
		out.RawString(prefix)
		out.Int64(int64(in.Progress))
	}
	{
		const prefix string = ",\"status\":"
		out.RawString(prefix)
		out.Int32(int32(in.Status))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"type\":"
		out.RawString(prefix)
		out.Int32(int32(in.Type))
	}
	{
		const prefix string = ",\"activity_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.ActivityId))
	}
	{
		const prefix string = ",\"activity_type\":"
		out.RawString(prefix)
		out.Int32(int32(in.ActivityType))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v ActivityTask) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc67(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v ActivityTask) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc67(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *ActivityTask) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc67(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *ActivityTask) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc67(l, v)
}
func easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc68(in *jlexer.Lexer, out *AchievementTask) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "_id":
			out.XId = string(in.String())
		case "uid":
			out.Uid = int64(in.Int64())
		case "quest_id":
			out.QuestId = int32(in.Int32())
		case "progress":
			out.Progress = int64(in.Int64())
		case "status":
			out.Status = int32(in.Int32())
		case "ctime":
			out.Ctime = timestamp.UTCSeconds(in.Int64())
		case "mtime":
			out.Mtime = timestamp.UTCSeconds(in.Int64())
		case "_version":
			out.XVersion = int64(in.Int64())
		case "type":
			out.Type = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc68(out *jwriter.Writer, in AchievementTask) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.XId))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"quest_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.QuestId))
	}
	{
		const prefix string = ",\"progress\":"
		out.RawString(prefix)
		out.Int64(int64(in.Progress))
	}
	{
		const prefix string = ",\"status\":"
		out.RawString(prefix)
		out.Int32(int32(in.Status))
	}
	{
		const prefix string = ",\"ctime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Ctime))
	}
	{
		const prefix string = ",\"mtime\":"
		out.RawString(prefix)
		out.Int64(int64(in.Mtime))
	}
	{
		const prefix string = ",\"_version\":"
		out.RawString(prefix)
		out.Int64(int64(in.XVersion))
	}
	{
		const prefix string = ",\"type\":"
		out.RawString(prefix)
		out.Int32(int32(in.Type))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v AchievementTask) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc68(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v AchievementTask) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson68cf4dafEncodeBitbucketOrgKingsgroupGogKnightsMinirpc68(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *AchievementTask) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc68(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *AchievementTask) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson68cf4dafDecodeBitbucketOrgKingsgroupGogKnightsMinirpc68(l, v)
}
